#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定文件的修复功能
"""

import os
import re
import shutil

def test_specific_file():
    """测试特定文件的修复功能"""
    
    source_file = "optimized_batch_results/5.2.11.1.91 0x6300740001 重删数据有冗余.md"
    test_file = "test_重删数据有冗余.md"
    
    # 复制文件用于测试
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    shutil.copy2(source_file, test_file)
    print(f"📄 复制测试文件: {test_file}")
    
    # 显示原始内容
    with open(test_file, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    print(f"\n📋 原始内容:")
    print("="*50)
    print(original_content)
    print("="*50)
    
    # 应用修复逻辑
    keywords = [
        "告警解释", "告警属性", "告警参数", "对系统的影响", 
        "可能原因", "处理步骤", "告警清除", "参考信息",
        "操作场景", "前提条件", "操作步骤"
    ]
    
    lines = original_content.split('\n')
    new_lines = []
    
    # 预处理：标记需要删除的重复行
    skip_lines = set()
    duplicates_removed = 0
    titles_fixed = 0
    
    # 检查标题下的重复内容
    if lines and lines[0].strip().startswith('#'):
        title_text = re.sub(r'^#+\s*', '', lines[0].strip())
        print(f"\n🔍 检查标题重复: '{title_text}'")
        
        # 查找第一个非空行
        for check_idx in range(1, min(10, len(lines))):
            if check_idx < len(lines):
                check_line = lines[check_idx].strip()
                if check_line:  # 找到第一个非空行
                    print(f"  检查第{check_idx+1}行: '{check_line}'")
                    
                    # 检查是否重复（去掉标点符号进行比较）
                    title_clean = re.sub(r'[^\w\s]', '', title_text)
                    check_clean = re.sub(r'[^\w\s]', '', check_line)
                    
                    print(f"  标题清理后: '{title_clean}'")
                    print(f"  检查行清理后: '{check_clean}'")
                    
                    if title_clean and check_clean and title_clean in check_clean:
                        print(f"  ✅ 发现重复内容，将删除第{check_idx+1}行")
                        skip_lines.add(check_idx)
                        duplicates_removed += 1
                    break
    
    # 处理每一行
    for i, line in enumerate(lines):
        # 如果这行被标记为跳过，则跳过
        if i in skip_lines:
            print(f"  🗑️  跳过第{i+1}行（重复内容）: '{line.strip()}'")
            continue
            
        # 检查关键词是否为标题
        line_stripped = line.strip()
        if line_stripped in keywords and not line_stripped.startswith('#'):
            new_lines.append(f"##### {line_stripped}")
            print(f"  🔧 修复第{i+1}行标题: '{line_stripped}' -> '##### {line_stripped}'")
            titles_fixed += 1
        else:
            new_lines.append(line)
    
    # 生成修复后的内容
    fixed_content = '\n'.join(new_lines)
    
    # 写入修复后的文件
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"\n📋 修复后内容:")
    print("="*50)
    print(fixed_content)
    print("="*50)
    
    print(f"\n📊 修复统计:")
    print(f"  🗑️  删除重复内容: {duplicates_removed} 处")
    print(f"  🔧 修复标题格式: {titles_fixed} 处")
    
    # 询问是否保留测试文件
    keep_file = input(f"\n❓ 是否保留测试文件 {test_file}? (y/n): ").lower().strip()
    if keep_file != 'y':
        os.remove(test_file)
        print(f"🗑️  已删除测试文件: {test_file}")
    else:
        print(f"📁 保留测试文件: {test_file}")

if __name__ == "__main__":
    test_specific_file()
