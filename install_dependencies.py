#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装Word到Markdown转换所需的依赖库
"""

import subprocess
import sys
import os


def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False


def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package.replace('-', '_'))
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"❌ {package} 未安装")
        return False


def install_pandoc_instructions():
    """显示pandoc安装说明"""
    print("\n📚 Pandoc 安装说明:")
    print("Pandoc 是一个强大的文档转换工具，需要单独安装：")
    print("\n🍎 macOS:")
    print("   brew install pandoc")
    print("\n🐧 Ubuntu/Debian:")
    print("   sudo apt-get install pandoc")
    print("\n🪟 Windows:")
    print("   1. 访问 https://pandoc.org/installing.html")
    print("   2. 下载并安装 Windows 版本")
    print("\n📦 使用 conda:")
    print("   conda install pandoc")
    print("\n或者使用 pip (可能不是最新版):")
    print("   pip install pypandoc")


def main():
    """主函数"""
    print("🔧 Word到Markdown转换工具 - 依赖安装器")
    print("=" * 50)
    
    # Python包依赖
    python_packages = [
        'python-docx',
        'mammoth', 
        'Pillow',
        'lxml'
    ]
    
    print("\n📦 检查Python包依赖...")
    
    # 检查已安装的包
    installed = []
    to_install = []
    
    for package in python_packages:
        if check_package(package):
            installed.append(package)
        else:
            to_install.append(package)
    
    # 安装缺失的包
    if to_install:
        print(f"\n🔄 需要安装 {len(to_install)} 个包...")
        
        failed = []
        for package in to_install:
            if not install_package(package):
                failed.append(package)
        
        if failed:
            print(f"\n❌ 以下包安装失败: {', '.join(failed)}")
            print("请手动安装这些包:")
            for pkg in failed:
                print(f"   pip install {pkg}")
        else:
            print("\n✅ 所有Python包安装成功!")
    else:
        print("\n✅ 所有Python包都已安装!")
    
    # 检查pandoc
    print("\n📚 检查Pandoc...")
    try:
        result = subprocess.run(['pandoc', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"✅ 发现 {version}")
        else:
            print("❌ Pandoc 未正确安装")
            install_pandoc_instructions()
    except FileNotFoundError:
        print("❌ 未找到 Pandoc")
        install_pandoc_instructions()
    
    print("\n🎉 依赖检查完成!")
    print("\n📋 总结:")
    print(f"   ✅ 已安装的Python包: {len(installed)}")
    print(f"   📦 新安装的Python包: {len(to_install) - len([p for p in to_install if p in failed]) if 'failed' in locals() else len(to_install)}")
    
    print("\n🚀 现在您可以运行转换工具:")
    print("   python docx_converter_suite.py test.docx")


if __name__ == "__main__":
    main()
