#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的批量转换器
专门处理华为云Stack告警处理参考文档的格式
"""

from enhanced_doc_converter import EnhancedDocxToMarkdown
import os
import re


class ImprovedBatchConverter:
    def __init__(self, source_docx, output_file="华为云Stack告警处理参考_完整版.md"):
        self.source_docx = source_docx
        self.output_file = output_file
        
        print(f"正在加载文档: {source_docx}")
        # 使用我们优化的转换器
        self.converter = EnhancedDocxToMarkdown(source_docx, "batch_output")
        print("文档加载完成")
    
    def convert_with_improvements(self):
        """转换并改进格式"""
        print("开始转换整个文档...")
        
        try:
            # 转换整个文档
            markdown_file = self.converter.convert_to_markdown("complete_alarms.md")
            
            # 读取转换结果
            with open(markdown_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 改进格式
            content = self._improve_document_formatting(content)
            
            # 保存最终文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"\n✅ 转换完成!")
            print(f"📄 输出文件: {self.output_file}")
            
            # 显示统计信息
            self._show_statistics(content)
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _improve_document_formatting(self, content):
        """改进文档格式"""
        print("改进文档格式...")
        
        lines = content.split('\\n')
        improved_lines = []
        
        # 替换文档标题
        improved_lines.append("# 华为云Stack告警处理参考")
        improved_lines.append("")
        improved_lines.append("本文档包含所有ALM告警的处理步骤和参考信息。")
        improved_lines.append("")
        improved_lines.append("---")
        improved_lines.append("")
        
        current_alm = None
        in_possible_causes = False
        in_processing_steps = False
        step_counter = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 跳过原始标题
            if line == "# 文档转换结果" or not line:
                continue
            
            # 检测ALM章节标题
            if re.match(r'^\\d+\\.\\s*\\d+\\.\\d+\\.\\d+\\.\\d+\\s+ALM-\\d+', line):
                # 新的ALM章节
                current_alm = re.search(r'ALM-\\d+', line).group()
                alm_title = re.sub(r'^\\d+\\.\\s*\\d+\\.\\d+\\.\\d+\\.\\d+\\s+', '', line)
                
                improved_lines.append(f"## {alm_title}")
                improved_lines.append("")
                
                in_possible_causes = False
                in_processing_steps = False
                step_counter = 0
                continue
            
            # 检测子章节
            if line in ["告警解释", "告警属性", "告警参数", "对系统的影响"]:
                improved_lines.append(f"### {line}")
                improved_lines.append("")
                in_possible_causes = False
                in_processing_steps = False
                continue
            
            if line == "可能原因":
                improved_lines.append(f"### {line}")
                improved_lines.append("")
                in_possible_causes = True
                in_processing_steps = False
                continue
            
            if line == "处理步骤":
                improved_lines.append(f"### {line}")
                improved_lines.append("")
                in_possible_causes = False
                in_processing_steps = True
                step_counter = 0
                continue
            
            # 处理可能原因（转换为无序列表）
            if in_possible_causes and line and not line.startswith('#'):
                if not line.startswith('-'):
                    improved_lines.append(f"- {line}")
                else:
                    improved_lines.append(line)
                continue
            
            # 处理处理步骤（保持有序列表）
            if in_processing_steps and line and not line.startswith('#'):
                # 检查是否是步骤开始
                if re.match(r'^\\d+\\.', line):
                    step_counter += 1
                    step_text = re.sub(r'^\\d+\\.\\s*', '', line)
                    improved_lines.append(f"{step_counter}. {step_text}")
                elif line.startswith('-'):
                    # 子步骤保持无序列表
                    improved_lines.append(f"   {line}")
                else:
                    # 其他内容保持缩进
                    improved_lines.append(f"   {line}")
                continue
            
            # 其他内容直接添加
            improved_lines.append(line)
        
        return '\\n'.join(improved_lines)
    
    def _show_statistics(self, content):
        """显示统计信息"""
        lines = len(content.split('\\n'))
        chars = len(content)
        alm_count = len(re.findall(r'## ALM-\\d+', content))
        
        print(f"📏 总行数: {lines}")
        print(f"📝 总字符数: {chars}")
        print(f"🚨 告警数量: {alm_count}")
        
        # 显示前几个ALM
        alm_titles = re.findall(r'## (ALM-\\d+ .+)', content)
        print(f"\\n📋 包含的告警:")
        for i, title in enumerate(alm_titles[:10], 1):
            print(f"  {i:2d}. {title}")
        
        if len(alm_titles) > 10:
            print(f"  ... 还有 {len(alm_titles) - 10} 个告警")


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 找不到源文件: {source_file}")
        return
    
    try:
        converter = ImprovedBatchConverter(source_file)
        converter.convert_with_improvements()
        
        print("\\n🎉 完整转换完成！")
        print("现在您有了一个包含所有ALM告警的完整Markdown文档，")
        print("每个告警都按照优化的格式进行了处理。")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
