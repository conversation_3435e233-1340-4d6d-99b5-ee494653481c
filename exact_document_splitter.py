#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确文档拆分工具
原封不动地保持原文档的所有格式、样式、表格等
"""

from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls, qn
import os
import re
import xml.etree.ElementTree as ET
from copy import deepcopy


class ExactDocumentSplitter:
    def __init__(self, source_docx, output_dir="exact_split_docs"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"正在加载文档: {source_docx}")
        self.doc = Document(source_docx)
        print(f"文档加载完成，共 {len(self.doc.paragraphs)} 段落")
    
    def find_chapter_boundaries(self):
        """查找章节边界"""
        print("查找章节边界...")
        
        chapters = []
        
        # 获取所有body元素的索引
        body_elements = list(self.doc.element.body)
        
        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()
            
            # 检查是否是告警章节标题
            if (paragraph.style.name.startswith('Heading') and 
                'ALM-' in text and 
                text.startswith('*******.')):
                
                # 找到对应的XML元素索引
                para_element = paragraph._element
                try:
                    element_index = body_elements.index(para_element)
                except ValueError:
                    continue
                
                chapters.append({
                    'title': text,
                    'start_para': i,
                    'start_element': element_index,
                    'alm_code': self._extract_alm_code(text)
                })
                
                if len(chapters) % 10 == 0:
                    print(f"  已找到 {len(chapters)} 个章节...")
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
                chapter['end_element'] = chapters[i + 1]['start_element']
            else:
                chapter['end_para'] = len(self.doc.paragraphs)
                chapter['end_element'] = len(body_elements)
        
        print(f"找到 {len(chapters)} 个告警章节")
        return chapters
    
    def _extract_alm_code(self, title):
        """提取ALM代码"""
        match = re.search(r'ALM-\d+', title)
        return match.group() if match else "ALM-UNKNOWN"
    
    def create_exact_document(self, chapter):
        """创建完全保持原格式的章节文档"""
        # 创建新文档
        new_doc = Document()
        
        # 复制样式
        self._copy_styles(new_doc)
        
        # 获取原文档的body元素
        source_body = self.doc.element.body
        new_body = new_doc.element.body
        
        # 清空新文档的默认内容
        for element in list(new_body):
            new_body.remove(element)
        
        # 复制指定范围的元素
        start_idx = chapter['start_element']
        end_idx = chapter['end_element']
        
        source_elements = list(source_body)
        
        for i in range(start_idx, min(end_idx, len(source_elements))):
            element = source_elements[i]
            # 深度复制元素
            new_element = deepcopy(element)
            new_body.append(new_element)
        
        return new_doc
    
    def _copy_styles(self, new_doc):
        """复制样式到新文档"""
        try:
            source_styles = self.doc.styles
            target_styles = new_doc.styles
            
            # 复制段落样式
            for style in source_styles:
                if style.name not in [s.name for s in target_styles]:
                    try:
                        # 创建新样式
                        new_style = target_styles.add_style(style.name, style.type)
                        
                        # 复制样式属性
                        if hasattr(style, 'font'):
                            if hasattr(style.font, 'name') and style.font.name:
                                new_style.font.name = style.font.name
                            if hasattr(style.font, 'size') and style.font.size:
                                new_style.font.size = style.font.size
                            if hasattr(style.font, 'bold'):
                                new_style.font.bold = style.font.bold
                            if hasattr(style.font, 'italic'):
                                new_style.font.italic = style.font.italic
                        
                        if hasattr(style, 'paragraph_format'):
                            if hasattr(style.paragraph_format, 'alignment'):
                                new_style.paragraph_format.alignment = style.paragraph_format.alignment
                            if hasattr(style.paragraph_format, 'left_indent') and style.paragraph_format.left_indent:
                                new_style.paragraph_format.left_indent = style.paragraph_format.left_indent
                            if hasattr(style.paragraph_format, 'space_before') and style.paragraph_format.space_before:
                                new_style.paragraph_format.space_before = style.paragraph_format.space_before
                            if hasattr(style.paragraph_format, 'space_after') and style.paragraph_format.space_after:
                                new_style.paragraph_format.space_after = style.paragraph_format.space_after
                    
                    except Exception as e:
                        # 如果复制样式失败，跳过
                        continue
        
        except Exception as e:
            print(f"  警告：样式复制失败: {e}")
    
    def generate_safe_filename(self, chapter):
        """生成安全的文件名"""
        alm_code = chapter['alm_code']
        title = chapter['title']
        
        # 提取告警名称
        name_part = re.sub(r'^[\d\.\s]+ALM-\d+\s*', '', title)
        name_part = re.sub(r'[<>:"/\\|?*]', '_', name_part)
        name_part = name_part.strip()[:40]  # 限制长度
        
        return f"{alm_code}_{name_part}.docx"
    
    def split_documents(self):
        """执行精确拆分"""
        print("开始精确拆分文档...")
        
        # 查找章节
        chapters = self.find_chapter_boundaries()
        
        if not chapters:
            print("未找到章节，拆分失败")
            return []
        
        created_files = []
        
        # 拆分每个章节
        for i, chapter in enumerate(chapters):
            try:
                print(f"处理 {i+1}/{len(chapters)}: {chapter['alm_code']}")
                
                # 创建精确复制的文档
                new_doc = self.create_exact_document(chapter)
                
                # 保存文档
                filename = self.generate_safe_filename(chapter)
                filepath = os.path.join(self.output_dir, filename)
                new_doc.save(filepath)
                
                created_files.append(filepath)
                
                if (i + 1) % 20 == 0:
                    print(f"  已完成 {i + 1} 个文档...")
                
            except Exception as e:
                print(f"  错误处理 {chapter['alm_code']}: {e}")
                continue
        
        print(f"\n✅ 精确拆分完成!")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📄 成功创建: {len(created_files)} 个文档")
        
        return created_files


class XMLBasedSplitter:
    """基于XML的更精确拆分方法"""
    
    def __init__(self, source_docx, output_dir="xml_split_docs"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"正在加载文档: {source_docx}")
        self.doc = Document(source_docx)
        print(f"文档加载完成")
    
    def split_by_xml_copy(self):
        """通过XML级别的复制来拆分"""
        print("使用XML级别复制进行拆分...")
        
        # 找到章节边界
        chapters = self._find_xml_chapters()
        
        created_files = []
        
        for i, chapter in enumerate(chapters):
            try:
                print(f"处理 {i+1}/{len(chapters)}: {chapter['alm_code']}")
                
                # 创建新文档并复制XML内容
                new_doc = self._create_xml_document(chapter)
                
                # 保存
                filename = self._generate_filename(chapter)
                filepath = os.path.join(self.output_dir, filename)
                new_doc.save(filepath)
                
                created_files.append(filepath)
                
            except Exception as e:
                print(f"  错误: {e}")
                continue
        
        print(f"\n✅ XML拆分完成! 创建了 {len(created_files)} 个文档")
        return created_files
    
    def _find_xml_chapters(self):
        """查找章节的XML边界"""
        chapters = []
        body_elements = list(self.doc.element.body)
        
        for i, para in enumerate(self.doc.paragraphs):
            text = para.text.strip()
            if (para.style.name.startswith('Heading') and 
                'ALM-' in text and text.startswith('*******.')):
                
                # 找到XML元素位置
                para_element = para._element
                try:
                    xml_index = body_elements.index(para_element)
                    chapters.append({
                        'title': text,
                        'xml_start': xml_index,
                        'alm_code': re.search(r'ALM-\d+', text).group() if re.search(r'ALM-\d+', text) else "ALM-UNKNOWN"
                    })
                except ValueError:
                    continue
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['xml_end'] = chapters[i + 1]['xml_start']
            else:
                chapter['xml_end'] = len(body_elements)
        
        return chapters
    
    def _create_xml_document(self, chapter):
        """创建包含精确XML内容的新文档"""
        # 创建基础文档
        new_doc = Document()
        
        # 获取源文档的XML元素
        source_body = self.doc.element.body
        new_body = new_doc.element.body
        
        # 清空新文档
        for element in list(new_body):
            new_body.remove(element)
        
        # 复制指定范围的XML元素
        source_elements = list(source_body)
        start = chapter['xml_start']
        end = chapter['xml_end']
        
        for i in range(start, min(end, len(source_elements))):
            # 深度复制XML元素
            element_copy = deepcopy(source_elements[i])
            new_body.append(element_copy)
        
        return new_doc
    
    def _generate_filename(self, chapter):
        """生成文件名"""
        alm_code = chapter['alm_code']
        title = chapter['title']
        name_part = re.sub(r'^[\d\.\s]+ALM-\d+\s*', '', title)
        name_part = re.sub(r'[<>:"/\\|?*]', '_', name_part)[:40]
        return f"{alm_code}_{name_part}.docx"


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 找不到源文件: {source_file}")
        return
    
    print("选择拆分方法:")
    print("1. 精确拆分 (保持完整格式)")
    print("2. XML级别拆分 (最精确)")
    
    choice = input("请选择 (1 或 2，默认 2): ").strip() or "2"
    
    try:
        if choice == "1":
            splitter = ExactDocumentSplitter(source_file, "exact_split_docs")
            created_files = splitter.split_documents()
        else:
            splitter = XMLBasedSplitter(source_file, "xml_split_docs")
            created_files = splitter.split_by_xml_copy()
        
        # 显示结果
        if created_files:
            print(f"\n📋 示例文档:")
            for filepath in created_files[:5]:
                filename = os.path.basename(filepath)
                print(f"  • {filename}")
            
            if len(created_files) > 5:
                print(f"  ... 还有 {len(created_files) - 5} 个文档")
        
    except Exception as e:
        print(f"❌ 拆分失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
