#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装OCR功能所需的依赖
"""

import subprocess
import sys
import os
import platform


def install_pytesseract():
    """安装pytesseract Python包"""
    try:
        print("正在安装 pytesseract...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytesseract"])
        print("✅ pytesseract 安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ pytesseract 安装失败")
        return False


def install_tesseract_engine():
    """安装Tesseract OCR引擎"""
    system = platform.system().lower()
    
    print(f"检测到操作系统: {system}")
    
    if system == "darwin":  # macOS
        print("\n🍎 macOS 安装说明:")
        print("请使用 Homebrew 安装 Tesseract:")
        print("   brew install tesseract")
        print("   brew install tesseract-lang  # 安装中文语言包")
        
    elif system == "linux":  # Linux
        print("\n🐧 Linux 安装说明:")
        print("Ubuntu/Debian:")
        print("   sudo apt-get update")
        print("   sudo apt-get install tesseract-ocr")
        print("   sudo apt-get install tesseract-ocr-chi-sim  # 中文简体")
        print("   sudo apt-get install tesseract-ocr-chi-tra  # 中文繁体")
        print("\nCentOS/RHEL:")
        print("   sudo yum install epel-release")
        print("   sudo yum install tesseract")
        print("   sudo yum install tesseract-langpack-chi_sim")
        
    elif system == "windows":  # Windows
        print("\n🪟 Windows 安装说明:")
        print("1. 下载 Tesseract 安装包:")
        print("   https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. 安装时确保选择中文语言包")
        print("3. 将安装路径添加到系统 PATH 环境变量")
        print("   默认路径: C:\\Program Files\\Tesseract-OCR")
        
    else:
        print(f"❌ 不支持的操作系统: {system}")


def check_tesseract_installation():
    """检查Tesseract是否已安装"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.split('\n')[0]
            print(f"✅ 发现 {version}")
            
            # 检查中文语言包
            lang_result = subprocess.run(['tesseract', '--list-langs'], 
                                       capture_output=True, text=True)
            if 'chi_sim' in lang_result.stdout:
                print("✅ 中文简体语言包已安装")
            else:
                print("⚠️  中文简体语言包未安装")
                
            return True
        else:
            return False
    except FileNotFoundError:
        return False


def test_ocr_functionality():
    """测试OCR功能"""
    try:
        import pytesseract
        from PIL import Image
        import numpy as np
        
        print("\n🧪 测试OCR功能...")
        
        # 创建一个简单的测试图片
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建白色背景图片
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加文本
        try:
            # 尝试使用系统字体
            font = ImageFont.load_default()
        except:
            font = None
            
        draw.text((10, 30), "Hello World 测试", fill='black', font=font)
        
        # 保存测试图片
        test_image_path = "test_ocr.png"
        img.save(test_image_path)
        
        # 进行OCR测试
        custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
        text = pytesseract.image_to_string(img, config=custom_config)
        
        print(f"OCR识别结果: {text.strip()}")
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            
        print("✅ OCR功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ OCR功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 OCR功能依赖安装器")
    print("=" * 50)
    
    # 1. 安装Python包
    print("\n📦 安装Python依赖...")
    pytesseract_ok = install_pytesseract()
    
    # 2. 检查Tesseract引擎
    print("\n🔍 检查Tesseract OCR引擎...")
    tesseract_ok = check_tesseract_installation()
    
    if not tesseract_ok:
        print("❌ 未找到Tesseract OCR引擎")
        install_tesseract_engine()
        print("\n请按照上述说明安装Tesseract，然后重新运行此脚本进行测试。")
        return
    
    # 3. 测试OCR功能
    if pytesseract_ok and tesseract_ok:
        test_ocr_functionality()
    
    print("\n🎉 安装检查完成!")
    print("\n📋 总结:")
    print(f"   ✅ pytesseract: {'已安装' if pytesseract_ok else '安装失败'}")
    print(f"   ✅ Tesseract引擎: {'已安装' if tesseract_ok else '未安装'}")
    
    if pytesseract_ok and tesseract_ok:
        print("\n🚀 现在您可以使用OCR功能:")
        print("   python docx_converter_suite.py test.docx -m 1")
    else:
        print("\n⚠️  请完成所有依赖的安装后再使用OCR功能")


if __name__ == "__main__":
    main()
