#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节转换器 - 专门用于转换文档中的特定章节
支持从Document对象中提取特定段落范围并转换为Markdown
"""

from docx import Document
from docx.shared import Inches
from docx.oxml.ns import qn
import os
import re


class ChapterConverter:
    def __init__(self, output_dir="output"):
        """
        初始化章节转换器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)

    def extract_chapter_content(self, doc, start_para_idx, end_para_idx, chapter_title=""):
        """
        从Document对象中提取章节内容并转换为Markdown

        Args:
            doc: Document对象
            start_para_idx: 起始段落索引
            end_para_idx: 结束段落索引
            chapter_title: 章节标题

        Returns:
            Markdown格式的内容字符串
        """
        content_lines = []

        # 添加一级标题（根据新需求）
        if chapter_title:
            content_lines.append(f"# {chapter_title}")
            content_lines.append("")

        # 用于跟踪不同编号系统的计数器
        numbering_systems = {}

        # 用于跟踪有序列表编号
        numbering_tracker = {}

        # 获取所有body元素（包括段落和表格）
        body_elements = list(doc.element.body)

        # 找到段落对应的元素索引范围
        start_element_idx = None
        end_element_idx = None

        para_count = 0
        for i, element in enumerate(body_elements):
            if element.tag.endswith('p'):  # 段落元素
                if para_count == start_para_idx:
                    start_element_idx = i
                elif para_count == end_para_idx:
                    end_element_idx = i
                    break
                para_count += 1

        if start_element_idx is None:
            return ""

        if end_element_idx is None:
            end_element_idx = len(body_elements)

        # 处理指定范围内的所有元素
        skip_first_title = True  # 跳过第一个标题（需求1）
        current_section = ""  # 记录当前所在的部分

        for i in range(start_element_idx, end_element_idx):
            element = body_elements[i]

            if element.tag.endswith('p'):  # 段落
                # 找到对应的段落对象
                for para in doc.paragraphs:
                    if para._element == element:
                        # 跳过第一个标题行（需求1）
                        if skip_first_title and self.is_chapter_title(para):
                            skip_first_title = False
                            continue

                        # 更新当前部分
                        current_section = self.update_current_section(para.text.strip(), current_section)

                        md_text = self.paragraph_to_markdown_with_context(para, current_section, numbering_tracker)
                        if md_text:
                            content_lines.append(md_text)
                        break

            elif element.tag.endswith('tbl'):  # 表格
                # 找到对应的表格对象
                for table in doc.tables:
                    if table._tbl == element:
                        md_table = self.table_to_markdown(table)
                        if md_table:
                            content_lines.append(md_table)
                        break

        # 后处理：清理内容
        final_content = "\n".join(content_lines)
        final_content = self.post_process_content(final_content)

        return final_content



    def table_to_markdown(self, table):
        """将表格转换为Markdown格式"""
        if not table.rows:
            return ""

        lines = []

        # 处理表头
        if table.rows:
            header_cells = []
            for cell in table.rows[0].cells:
                cell_text = cell.text.strip().replace('\n', ' ')
                header_cells.append(cell_text if cell_text else " ")
            lines.append("| " + " | ".join(header_cells) + " |")
            lines.append("| " + " | ".join(["---"] * len(header_cells)) + " |")

        # 处理数据行
        for row in table.rows[1:]:
            data_cells = []
            for cell in row.cells:
                cell_text = cell.text.strip().replace('\n', '<br>')
                data_cells.append(cell_text if cell_text else " ")
            lines.append("| " + " | ".join(data_cells) + " |")

        return "\n".join(lines)

    def convert_chapter(self, doc, start_para_idx, end_para_idx, chapter_info, output_filename=None):
        """
        转换单个章节

        Args:
            doc: Document对象
            start_para_idx: 起始段落索引
            end_para_idx: 结束段落索引
            chapter_info: 章节信息字典，包含alarm_code, alarm_name等
            output_filename: 输出文件名（可选）

        Returns:
            输出文件路径
        """
        # 生成章节标题（包含章节号）
        alarm_code = chapter_info.get('alarm_code', '')
        alarm_name = chapter_info.get('alarm_name', '')
        chapter_title_text = chapter_info.get('title', '')

        # 从原始标题中提取章节号
        section_number = ""
        if chapter_title_text:
            # 匹配章节号格式：5.x.x.x.x
            import re
            match = re.match(r'^(5\.\d+\.\d+\.\d+\.\d+)', chapter_title_text)
            if match:
                section_number = match.group(1)

        # 组合完整标题
        if section_number and alarm_code and alarm_name:
            chapter_title = f"{section_number} {alarm_code} {alarm_name}"
        elif alarm_code and alarm_name:
            chapter_title = f"{alarm_code} {alarm_name}"
        else:
            chapter_title = ""

        # 提取并转换内容
        markdown_content = self.extract_chapter_content(
            doc, start_para_idx, end_para_idx, chapter_title
        )

        # 生成输出文件名
        if not output_filename:
            safe_name = self.generate_safe_filename(chapter_info)
            output_filename = f"{safe_name}.md"

        # 保存文件
        output_path = os.path.join(self.output_dir, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        return output_path

    def generate_safe_filename(self, chapter_info):
        """生成安全的文件名（以章节号开头）"""
        chapter_title_text = chapter_info.get('title', '')

        # 从原始标题中提取章节号和告警信息
        section_number = ""
        alarm_code = ""
        alarm_name = ""

        if chapter_title_text:
            # 匹配格式：5.x.x.x.x ALM-xxxx 名称
            match = re.match(r'^(5\.\d+\.\d+\.\d+\.\d+)\s+(ALM-\d+)\s+(.+)', chapter_title_text)
            if match:
                section_number = match.group(1)
                alarm_code = match.group(2)
                alarm_name = match.group(3)
            else:
                # 如果不匹配，使用原文本
                alarm_name = chapter_title_text

        # 清理文件名中的非法字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', alarm_name)
        safe_name = safe_name.strip()

        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]

        # 以章节号开头的文件名
        if section_number and alarm_code:
            return f"{section_number} {alarm_code} {safe_name}"
        elif alarm_code:
            return f"{alarm_code} {safe_name}"
        else:
            return safe_name

    def get_list_info(self, paragraph):
        """获取列表信息，包括原始编号"""
        if not hasattr(paragraph, '_element') or paragraph._element is None:
            return None

        pPr = paragraph._element.find(qn('w:pPr'))
        if pPr is None:
            return None

        numPr = pPr.find(qn('w:numPr'))
        if numPr is None:
            return None

        # 获取列表级别
        ilvl = numPr.find(qn('w:ilvl'))
        level = 0
        if ilvl is not None:
            level = int(ilvl.get(qn('w:val'), 0))

        # 获取编号ID
        numId = numPr.find(qn('w:numId'))
        if numId is None:
            return None

        num_id = numId.get(qn('w:val'))

        # 简单判断是否为有序列表
        is_numbered = True  # 默认假设是有序的

        return {
            'level': level,
            'is_numbered': is_numbered,
            'num_id': num_id
        }

    def is_processing_step(self, text, level):
        """判断是否为处理步骤"""
        # 更精确的判断标准

        # 如果不是最外层，不是处理步骤
        if level != 0:
            return False

        # 处理步骤的关键词和模式（更宽松的匹配）
        step_patterns = [
            r'登录',
            r'执行',
            r'输入',
            r'点击',
            r'选择',
            r'检查',
            r'查看',
            r'配置',
            r'修改',
            r'重启',
            r'使用',
            r'等待',
            r'联系',
            r'运行',
            r'启动',
            r'停止',
            r'获取',
            r'设置',
            r'触发',
            r'确认',
            r'重新',
            r'手动'
        ]

        # 检查是否匹配处理步骤模式
        for pattern in step_patterns:
            if pattern in text:
                return True

        return False

    def get_actual_number(self, num_id, level, numbering_tracker):
        """根据num_id和level计算实际编号"""
        if not num_id:
            return 1

        # 对于处理步骤中的level=0项，使用全局连续编号
        if level == 0:
            # 使用全局计数器
            global_key = "processing_steps_global"
            if global_key not in numbering_tracker:
                numbering_tracker[global_key] = 0
            numbering_tracker[global_key] += 1
            return numbering_tracker[global_key]
        else:
            # 对于子级别，按num_id分组
            key = f"{num_id}_{level}"
            if key not in numbering_tracker:
                numbering_tracker[key] = 0
            numbering_tracker[key] += 1
            return numbering_tracker[key]

    def is_in_processing_section(self, text):
        """判断当前文本是否在处理步骤部分"""
        # 这里可以根据上下文来判断，简化处理：如果包含关键词就认为在处理步骤部分
        # 更精确的做法是记录当前段落的上下文状态
        processing_section_indicators = [
            '处理步骤', '操作步骤', '解决方法',
            '登录', '执行', '输入', '点击', '选择',
            '检查', '查看', '配置', '修改', '重启',
            '使用.*登录', '等待.*查看'
        ]

        for indicator in processing_section_indicators:
            if indicator in text:
                return True

        return False

    def is_chapter_title(self, paragraph):
        """判断是否为章节标题（包含ALM代码的标题）"""
        text = paragraph.text.strip()
        # 匹配章节标题格式：5.x.x.x.x ALM-xxxx 标题
        return bool(re.match(r'^5\.\d+\.\d+\.\d+\.\d+\s+ALM-\d+', text))

    def update_current_section(self, text, current_section):
        """更新当前所在的部分"""
        # 检查是否是新的部分标题（不仅仅是#开头）
        if '可能原因' in text:
            return 'possible_causes'
        elif '处理步骤' in text:
            return 'processing_steps'
        elif '操作步骤' in text:
            return 'processing_steps'
        elif '解决方法' in text:
            return 'processing_steps'
        elif text.startswith('#') and any(keyword in text for keyword in ['告警解释', '告警属性', '告警参数', '对系统的影响', '参考信息']):
            return 'other'

        return current_section

    def paragraph_to_markdown_with_context(self, paragraph, current_section, numbering_tracker):
        """根据上下文将段落转换为Markdown格式"""
        text = paragraph.text.strip()

        if not text:
            return ""

        # 检查段落样式
        style_name = paragraph.style.name.lower()

        # 标题处理
        if 'heading' in style_name:
            if 'heading 1' in style_name:
                return f"## {text}"  # 使用二级标题，因为一级标题已删除
            elif 'heading 2' in style_name:
                return f"### {text}"
            elif 'heading 3' in style_name:
                return f"#### {text}"
            elif 'heading 4' in style_name:
                return f"##### {text}"
            elif 'heading 5' in style_name:
                return f"###### {text}"
            elif 'heading 6' in style_name:
                return f"###### {text}"

        # 列表处理（根据需求2和上下文）
        list_info = self.get_list_info(paragraph)
        if list_info:
            level = list_info['level']
            num_id = list_info.get('num_id')

            # 根据上下文和级别决定列表类型
            if current_section == 'processing_steps':
                if level == 0:
                    # 处理步骤部分的最外层：使用有序列表
                    actual_number = self.get_actual_number(num_id, level, numbering_tracker)
                    return f"{actual_number}. {text}"
                else:
                    # 处理步骤部分的子级别：使用无序列表
                    indent = "  " * level
                    return f"{indent}- {text}"
            else:
                # 其他情况（包括可能原因部分）：使用无序列表
                indent = "  " * level
                return f"{indent}- {text}"

        # 普通段落
        return text

    def post_process_content(self, content):
        """后处理内容：清理不需要的信息"""
        lines = content.split('\n')
        processed_lines = []

        for line in lines:
            line = line.strip()

            # 根据需求3：删除参考信息中的不需要内容
            if self.should_remove_line(line):
                continue

            processed_lines.append(line)

        # 清理多余的空行
        final_lines = []
        prev_empty = False

        for line in processed_lines:
            if not line:
                if not prev_empty:
                    final_lines.append(line)
                prev_empty = True
            else:
                final_lines.append(line)
                prev_empty = False

        # 处理有序列表的编号（确保连续编号）
        final_lines = self.fix_ordered_list_numbering(final_lines)

        return '\n'.join(final_lines)

    def should_remove_line(self, line):
        """判断是否应该删除该行"""
        # 需要删除的内容模式
        remove_patterns = [
            r'父主题：.*',
            r'版权所有.*华为技术有限公司.*',
            r'下一节.*>.*',
            r'FusionSphere OpenStack告警参考.*',
            r'版权所有.*华为.*',
            r'^©.*华为.*',  # 版权符号开头
            r'.*版权所有.*',
            r'.*下一节.*',
            r'.*父主题.*'
        ]

        for pattern in remove_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                return True

        return False

    def fix_ordered_list_numbering(self, lines):
        """修复有序列表的编号，保持原始编号"""
        # 现在我们保持原始编号，所以这里不需要做太多处理
        result_lines = []

        for line in lines:
            # 如果有我们的特殊标记，就处理一下（但现在应该不会有了）
            if line.startswith('ORDERED_LIST_ITEM: '):
                content = line.replace('ORDERED_LIST_ITEM: ', '')
                result_lines.append(f"1. {content}")
            elif line.startswith('KEEP_ORIGINAL_NUMBER: '):
                content = line.replace('KEEP_ORIGINAL_NUMBER: ', '')
                result_lines.append(content)
            else:
                result_lines.append(line)

        return result_lines


# 使用示例
def example_usage():
    """使用示例"""

    # 创建转换器
    converter = ChapterConverter("chapter_output")

    # 打开文档
    doc = Document("华为云Stack告警处理参考.docx")

    # 章节信息
    chapter_info = {
        'alarm_code': 'ALM-6008',
        'alarm_name': '上传日志到OBS服务失败',
        'section_type': 'ALM告警',
        'title': '5.2.3.1.1 ALM-6008 上传日志到OBS服务失败'
    }

    # 转换章节（段落5-206）
    output_path = converter.convert_chapter(
        doc=doc,
        start_para_idx=5,
        end_para_idx=206,
        chapter_info=chapter_info
    )

    print(f"章节转换完成: {output_path}")


if __name__ == "__main__":
    example_usage()
