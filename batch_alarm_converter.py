#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量告警文档转换器
将华为云Stack告警处理参考.docx转换为一个大的Markdown文件
每个ALM告警章节都按照优化的格式处理
"""

from enhanced_doc_converter import EnhancedDocxToMarkdown
from docx import Document
import os
import re


class BatchAlarmConverter:
    def __init__(self, source_docx, output_file="华为云Stack告警处理参考.md"):
        self.source_docx = source_docx
        self.output_file = output_file
        self.doc = Document(source_docx)
        
        print(f"正在加载文档: {source_docx}")
        print(f"文档加载完成，共 {len(self.doc.paragraphs)} 段落")
    
    def find_alarm_chapters(self):
        """查找所有ALM告警章节"""
        print("查找ALM告警章节...")
        
        chapters = []
        
        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()
            
            # 检查是否是告警章节标题
            if (paragraph.style.name.startswith('Heading') and 
                'ALM-' in text and 
                text.startswith('*******.')):
                
                chapters.append({
                    'title': text,
                    'start_para': i,
                    'alm_code': self._extract_alm_code(text)
                })
                
                if len(chapters) % 20 == 0:
                    print(f"  已找到 {len(chapters)} 个告警章节...")
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                chapter['end_para'] = len(self.doc.paragraphs)
        
        print(f"找到 {len(chapters)} 个ALM告警章节")
        return chapters
    
    def _extract_alm_code(self, title):
        """提取ALM代码"""
        match = re.search(r'ALM-\d+', title)
        return match.group() if match else "ALM-UNKNOWN"
    
    def convert_chapter_to_markdown(self, chapter):
        """将单个章节转换为Markdown"""
        try:
            # 创建临时文档包含该章节内容
            temp_doc = Document()
            
            # 复制章节内容到临时文档
            start = chapter['start_para']
            end = chapter['end_para']
            
            for i in range(start, min(end, len(self.doc.paragraphs))):
                para = self.doc.paragraphs[i]
                new_para = temp_doc.add_paragraph(para.text)
                try:
                    new_para.style = para.style
                except:
                    pass
            
            # 保存临时文档
            temp_file = f"temp_{chapter['alm_code']}.docx"
            temp_doc.save(temp_file)
            
            # 使用我们的转换器转换
            converter = EnhancedDocxToMarkdown(temp_file, "temp_output")
            markdown_file = converter.convert_to_markdown(f"{chapter['alm_code']}.md")
            
            # 读取转换结果
            with open(markdown_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 清理临时文件
            try:
                os.remove(temp_file)
                os.remove(markdown_file)
                if os.path.exists("temp_output"):
                    import shutil
                    shutil.rmtree("temp_output")
            except:
                pass
            
            return content
            
        except Exception as e:
            print(f"  转换 {chapter['alm_code']} 时出错: {e}")
            return f"# {chapter['title']}\n\n转换失败: {str(e)}\n\n"
    
    def create_unified_markdown(self):
        """创建统一的Markdown文档"""
        print("开始批量转换...")
        
        # 查找所有章节
        chapters = self.find_alarm_chapters()
        
        if not chapters:
            print("未找到ALM告警章节")
            return
        
        # 创建主Markdown文件
        markdown_content = []
        
        # 添加文档标题和目录
        markdown_content.append("# 华为云Stack告警处理参考")
        markdown_content.append("")
        markdown_content.append("## 目录")
        markdown_content.append("")
        
        # 生成目录
        for chapter in chapters:
            alm_code = chapter['alm_code']
            title = chapter['title'].replace(f'*******.{chapters.index(chapter)+1} {alm_code} ', '')
            markdown_content.append(f"- [{alm_code} {title}](#{alm_code.lower()})")
        
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")
        
        # 转换每个章节
        for i, chapter in enumerate(chapters):
            print(f"转换 {i+1}/{len(chapters)}: {chapter['alm_code']}")
            
            # 转换章节内容
            chapter_content = self.convert_chapter_to_markdown(chapter)
            
            # 处理章节内容
            if chapter_content:
                # 移除文档转换结果标题
                chapter_content = re.sub(r'^# 文档转换结果\s*\n', '', chapter_content)
                
                # 添加章节锚点
                alm_code = chapter['alm_code']
                chapter_content = f"## {alm_code} {{#{alm_code.lower()}}}\n\n" + chapter_content
                
                markdown_content.append(chapter_content)
                markdown_content.append("")
                markdown_content.append("---")
                markdown_content.append("")
            
            if (i + 1) % 10 == 0:
                print(f"  已完成 {i + 1} 个章节...")
        
        # 写入最终文件
        final_content = '\n'.join(markdown_content)
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"\n✅ 批量转换完成!")
        print(f"📄 输出文件: {self.output_file}")
        print(f"📊 转换了 {len(chapters)} 个告警章节")
        
        # 显示文件统计
        lines = len(final_content.split('\n'))
        chars = len(final_content)
        print(f"📏 总行数: {lines}")
        print(f"📝 总字符数: {chars}")


class DirectBatchConverter:
    """直接批量转换器（不创建临时文件）"""
    
    def __init__(self, source_docx, output_file="华为云Stack告警处理参考_直接转换.md"):
        self.source_docx = source_docx
        self.output_file = output_file
        
        print(f"正在加载文档: {source_docx}")
        # 直接使用我们的转换器
        self.converter = EnhancedDocxToMarkdown(source_docx, "direct_output")
        print("文档加载完成")
    
    def convert_entire_document(self):
        """直接转换整个文档"""
        print("开始直接转换整个文档...")
        
        try:
            # 转换整个文档
            markdown_file = self.converter.convert_to_markdown("complete_document.md")
            
            # 读取转换结果
            with open(markdown_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 后处理：改进标题格式
            content = self._improve_formatting(content)
            
            # 保存最终文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"\n✅ 直接转换完成!")
            print(f"📄 输出文件: {self.output_file}")
            
            # 显示统计信息
            lines = len(content.split('\n'))
            chars = len(content)
            alm_count = content.count('ALM-')
            
            print(f"📏 总行数: {lines}")
            print(f"📝 总字符数: {chars}")
            print(f"🚨 告警数量: {alm_count}")
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _improve_formatting(self, content):
        """改进格式"""
        # 替换文档标题
        content = re.sub(r'^# 文档转换结果', '# 华为云Stack告警处理参考', content)
        
        # 改进ALM章节标题格式
        content = re.sub(r'^#### (5\.2\.3\.1\.\d+ ALM-\d+ .+)$', r'## \1', content, flags=re.MULTILINE)
        
        # 添加目录锚点
        def add_anchor(match):
            title = match.group(1)
            alm_match = re.search(r'ALM-\d+', title)
            if alm_match:
                alm_code = alm_match.group()
                return f"## {title} {{#{alm_code.lower()}}}"
            return match.group(0)
        
        content = re.sub(r'^## (5\.2\.3\.1\.\d+ ALM-\d+ .+)$', add_anchor, content, flags=re.MULTILINE)
        
        return content


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 找不到源文件: {source_file}")
        return
    
    print("选择转换方式:")
    print("1. 章节分别转换后合并 (更精确，但较慢)")
    print("2. 直接转换整个文档 (更快，推荐)")
    
    choice = input("请选择 (1 或 2，默认 2): ").strip() or "2"
    
    try:
        if choice == "1":
            converter = BatchAlarmConverter(source_file)
            converter.create_unified_markdown()
        else:
            converter = DirectBatchConverter(source_file)
            converter.convert_entire_document()
        
        print("\n🎉 转换完成！现在您有了一个包含所有ALM告警的完整Markdown文档。")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
