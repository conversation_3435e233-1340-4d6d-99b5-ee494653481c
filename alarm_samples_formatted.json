[{"主题": "[提示信息]弹性负载均衡", "告警ID": "", "告警级别": "提示", "告警源": "ELB", "来源系统": "ManageOne10.200.66.115", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=management", "附加信息": "云服务=ELB，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Database发生数据库连接异常", "告警ID": "1223005", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.74.17", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.221.218)，对端地址=(other_database=10.200.2.6)", "可能原因": "未知"}, {"主题": "[重要告警]ETCD发生ETCD集群健康检查告警", "告警ID": "1223006", "告警级别": "重要", "告警源": "ETCD", "来源系统": "ServiceOM10.200.119.231", "定位信息": "区域=SH_CSVW，云服务=ETCD，节点类型=mgt", "附加信息": "云服务=ETCD，服务=mgt，本端地址=(10.200.119.238)，对端地址=(other_etcd=10.200.116.130)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生集群中存在主机连接异常", "告警ID": "1223013", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.249.97", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.244.61)，对端地址=(other_network=10.200.86.167)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生僵尸进程告警", "告警ID": "1223014", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.191.158", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.241.211)，对端地址=(other_process=10.200.72.243)", "可能原因": "未知"}, {"主题": "[次要告警]ELB发生ELB管理节点脑裂告警", "告警ID": "1223016", "告警级别": "次要", "告警源": "ELB", "来源系统": "ServiceOM10.200.146.41", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.49.223)，对端地址=(other_elb=10.200.233.251)", "可能原因": "未知"}, {"主题": "[提示信息]灾备服务", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.77.158", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]eBackup", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.63.122", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]License发生License文件无效", "告警ID": "0x1000F40000", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.134.39", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.114.155)，错误代码=0x1000F40000", "可能原因": "未知"}, {"主题": "[严重告警]License发生License授权容量即将耗尽", "告警ID": "0x201000F40008", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.165.151", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.33.205)，错误代码=0x201000F40008", "可能原因": "未知"}, {"主题": "[严重告警]License发生存在License不支持的特性", "告警ID": "0x201000F4000C", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.249.56", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.215.37)，错误代码=0x201000F4000C", "可能原因": "未知"}, {"主题": "[次要告警]License发生存在License不支持的特性", "告警ID": "0x201000F40013", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.184.91", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.239.218)，错误代码=0x201000F40013", "可能原因": "未知"}, {"主题": "[次要告警]License发生存在License不支持的特性", "告警ID": "0x201000F40014", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.143.35", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.104.41)，错误代码=0x201000F40014", "可能原因": "未知"}, {"主题": "[严重告警]License发生存在License不支持的特性", "告警ID": "0x201000F40016", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.27.4", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.35.253)，错误代码=0x201000F40016", "可能原因": "未知"}, {"主题": "[重要告警]License发生存在License不支持的特性", "告警ID": "0x201000F40017", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.33.156", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.133.62)，错误代码=0x201000F40017", "可能原因": "未知"}, {"主题": "[次要告警]License发生存在License不支持的特性", "告警ID": "0x201000F40018", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.244.203", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.32.218)，错误代码=0x201000F40018", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生事件转储目录所用空间已超出阈值", "告警ID": "0x1000310000", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.206.60", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.37.160)，错误代码=0x1000310000", "可能原因": "未知"}, {"主题": "[严重告警]Certificate发生证书校验失败", "告警ID": "0x20100031000A", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.35.193", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.171.42)，错误代码=0x20100031000A", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书校验失败", "告警ID": "0x20100031000C", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.247.51", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.231.210)，错误代码=0x20100031000C", "可能原因": "未知"}, {"主题": "[重要告警]License发生License未配置", "告警ID": "0x1000F40001", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.55.112", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.201.36)，错误代码=0x1000F40001", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生访问告警服务器失败", "告警ID": "0x201000310010", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.60.251", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.115.241)，错误代码=0x201000310010", "可能原因": "未知"}, {"主题": "[严重告警]Database发生数据库连接失败", "告警ID": "0x201000310015", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.90.56", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.142.172)，错误代码=0x201000310015", "可能原因": "未知"}, {"主题": "[重要告警]Microservice发生微服务注册失败", "告警ID": "0x210000000101", "告警级别": "重要", "告警源": "Microservice", "来源系统": "eBackup10.200.243.66", "定位信息": "区域=SH_CSVW，云服务=Microservice，节点类型=backup", "附加信息": "云服务=Microservice，服务=backup，节点地址=(10.200.160.209)，错误代码=0x210000000101", "可能原因": "未知"}, {"主题": "[次要告警]Microservice发生微服务已停止", "告警ID": "0x210000000100", "告警级别": "次要", "告警源": "Microservice", "来源系统": "eBackup10.200.163.148", "定位信息": "区域=SH_CSVW，云服务=Microservice，节点类型=backup", "附加信息": "云服务=Microservice，服务=backup，节点地址=(10.200.139.79)，错误代码=0x210000000100", "可能原因": "未知"}, {"主题": "[重要告警]Certificate发生证书已经过期", "告警ID": "0x6000840001", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.228.17", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.14.139)，错误代码=0x6000840001", "可能原因": "未知"}, {"主题": "[严重告警]Certificate发生证书即将过期", "告警ID": "0x6000840002", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.73.11", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.31.73)，错误代码=0x6000840002", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书校验失败", "告警ID": "0x210000000200", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.135.44", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.79.188)，错误代码=0x210000000200", "可能原因": "未知"}, {"主题": "[重要告警]Backup发生清理备份记录失败", "告警ID": "0x105800860001", "告警级别": "重要", "告警源": "Backup", "来源系统": "eBackup10.200.168.128", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.132.188)，错误代码=0x105800860001", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生清理leftover删除快照失败", "告警ID": "0x21000000090E", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.207.33", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.167.16)，错误代码=0x21000000090E", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生组件连接异常", "告警ID": "0x21000000090F", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.100.95", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.67.225)，错误代码=0x21000000090F", "可能原因": "未知"}, {"主题": "[重要告警]License发生License进入宽限期", "告警ID": "0x1000F40002", "告警级别": "重要", "告警源": "License", "来源系统": "eBackup10.200.104.121", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.91.175)，错误代码=0x1000F40002", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生监控进程启动失败", "告警ID": "0x210000000901", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.220.39", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.108.101)，错误代码=0x210000000901", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书校验失败", "告警ID": "0x2010E01D0005", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.245.53", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.158.251)，错误代码=0x2010E01D0005", "可能原因": "未知"}, {"主题": "[严重告警]Backup发生执行备份时CBT机制未生效", "告警ID": "0x1010E01A0018", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.97.232", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.39.181)，错误代码=0x1010E01A0018", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生系统配置数据所占空间已超出最大阈值", "告警ID": "0x101000C90003", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.185.34", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.250.79)，错误代码=0x101000C90003", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生系统配置数据所占空间已超出阈值", "告警ID": "0x101000C90004", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.186.146", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.251.150)，错误代码=0x101000C90004", "可能原因": "未知"}, {"主题": "[严重告警]Database发生访问系统数据库备份共享存储失败", "告警ID": "0x1000C90002", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.36.125", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.156.142)，错误代码=0x1000C90002", "可能原因": "未知"}, {"主题": "[重要告警]Certificate发生证书校验失败", "告警ID": "0x1000C90035", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.17.123", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.134.187)，错误代码=0x1000C90035", "可能原因": "未知"}, {"主题": "[严重告警]Database发生系统数据库备份共享存储空间不足", "告警ID": "0x1000C90003", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.221.34", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.36.190)，错误代码=0x1000C90003", "可能原因": "未知"}, {"主题": "[严重告警]Backup发生未配置备份服务器", "告警ID": "0x1000C90005", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.181.213", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.190.60)，错误代码=0x1000C90005", "可能原因": "未知"}, {"主题": "[严重告警]Certificate发生证书校验失败", "告警ID": "0x1000C90006", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.63.189", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.197.226)，错误代码=0x1000C90006", "可能原因": "未知"}, {"主题": "[严重告警]License发生License已经过期", "告警ID": "0x1000F40003", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.34.157", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.14.34)，错误代码=0x1000F40003", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生FTP服务器空间不足", "告警ID": "0x1000C90032", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.12.152", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.88.137)，错误代码=0x1000C90032", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生登录FTP服务器被拒绝", "告警ID": "0x1000C90033", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.152.29", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.22.197)，错误代码=0x1000C90033", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生上传管理数据到FTP服务器失败", "告警ID": "0x1000C90034", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.66.13", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.92.162)，错误代码=0x1000C90034", "可能原因": "未知"}, {"主题": "[严重告警]Database发生当前挂载的系统数据库备份共享存储类型与预置类型不匹", "告警ID": "0x1000C90004", "告警级别": "严重", "告警源": "Database", "来源系统": "eBackup10.200.2.150", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.162.247)，错误代码=0x1000C90004", "可能原因": "未知"}, {"主题": "[次要告警]Backup发生eBackup没有存储单元的写权限", "告警ID": "0x2010E00E0007", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.158.213", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.179.119)，错误代码=0x2010E00E0007", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书校验失败", "告警ID": "0x2010E00E000A", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.228.155", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.189.242)，错误代码=0x2010E00E000A", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生存储单元没有可用容量", "告警ID": "0x2010E00E0006", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.79.33", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.247.75)，错误代码=0x2010E00E0006", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书校验失败", "告警ID": "0x2010E01D0006", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.131.136", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.250.144)，错误代码=0x2010E01D0006", "可能原因": "未知"}, {"主题": "[严重告警]Backup发生eBackup服务器之间失去连接", "告警ID": "0x1000C90000", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.185.113", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.222.232)，错误代码=0x1000C90000", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生存储库容量不足", "告警ID": "0x10E00C0000", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.110.108", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.47.161)，错误代码=0x10E00C0000", "可能原因": "未知"}, {"主题": "[严重告警]License发生License ESN不匹配", "告警ID": "0x101000F40000", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.156.144", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.45.42)，错误代码=0x101000F40000", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生添加Workflow或Proxy失败", "告警ID": "0x10E01C0000", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.176.96", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.91.202)，错误代码=0x10E01C0000", "可能原因": "未知"}, {"主题": "[严重告警]Certificate发生HA的证书已过期", "告警ID": "0x6000760001", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.201.85", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.160.74)，错误代码=0x6000760001", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生服务进程异常", "告警ID": "0x10E01C0027", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.238.10", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.128.214)，错误代码=0x10E01C0027", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生连接存储单元失败", "告警ID": "0x10E00E0000", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.110.14", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(*************)，错误代码=0x10E00E0000", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生浮动IP连接异常", "告警ID": "0x10E01C0029", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.221.175", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(**************)，错误代码=0x10E01C0029", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生Workflow（Proxy）和Manager（S", "告警ID": "0x10E01C0001", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.93.149", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(*************)，错误代码=0x10E01C0001", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生主备参数不一致导致HA功能异常", "告警ID": "0x10E01C0002", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.70.211", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.218.146)，错误代码=0x10E01C0002", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生恢复主备倒换功能失败", "告警ID": "0x10E01C0003", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.142.251", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.239.37)，错误代码=0x10E01C0003", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生HA主备节点心跳中断", "告警ID": "0x10E01C0004", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.154.223", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.82.49)，错误代码=0x10E01C0004", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生HA主节点向备节点同步文件失败", "告警ID": "0x10E01C0005", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.249.45", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.237.160)，错误代码=0x10E01C0005", "可能原因": "未知"}, {"主题": "[次要告警]License发生License文件版本不匹配", "告警ID": "0x101000F40001", "告警级别": "次要", "告警源": "License", "来源系统": "eBackup10.200.57.138", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.148.21)，错误代码=0x101000F40001", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生HA仲裁网关不可达", "告警ID": "0x10E01C0009", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.65.80", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.193.165)，错误代码=0x10E01C0009", "可能原因": "未知"}, {"主题": "[次要告警]Database发生数据库升主失败", "告警ID": "0x10E01C000B", "告警级别": "次要", "告警源": "Database", "来源系统": "eBackup10.200.84.84", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.253.176)，错误代码=0x10E01C000B", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生AdminNode服务异常", "告警ID": "0x10E01C000E", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.66.201", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(**************)，错误代码=0x10E01C000E", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生浮动IP服务异常", "告警ID": "0x10E01C000F", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.156.50", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(*************)，错误代码=0x10E01C000F", "可能原因": "未知"}, {"主题": "[次要告警]Database发生GaussDB服务异常", "告警ID": "0x10E01C0010", "告警级别": "次要", "告警源": "Database", "来源系统": "eBackup10.200.180.223", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(***********)，错误代码=0x10E01C0010", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生ibase服务异常", "告警ID": "0x10E01C0011", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.248.50", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.135.128)，错误代码=0x10E01C0011", "可能原因": "未知"}, {"主题": "[严重告警]Certificate发生证书校验失败", "告警ID": "0x10E01C0028", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.74.87", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.174.131)，错误代码=0x10E01C0028", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生访问存储单元失败", "告警ID": "0x10E00E0001", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.51.227", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.1.170)，错误代码=0x10E00E0001", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生扫描受保护环境失败", "告警ID": "0x10E0140001", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.29.12", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.41.115)，错误代码=0x10E0140001", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生连接受保护环境失败", "告警ID": "0x10E0140000", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.51.236", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.33.117)，错误代码=0x10E0140000", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生试用即将到期", "告警ID": "0x201000F40004", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.13.14", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.129.10)，错误代码=0x201000F40004", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书校验失败", "告警ID": "0x2010E014000C", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.207.228", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.153.228)，错误代码=0x2010E014000C", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生存储池空间使用率超出临界值", "告警ID": "0x10E00D0000", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.173.11", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.67.150)，错误代码=0x10E00D0000", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生不支持当前时区信息", "告警ID": "0x201000C90002", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.197.80", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.157.35)，错误代码=0x201000C90002", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生NTP时间差异过大", "告警ID": "0x201000C90025", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.88.253", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.74.172)，错误代码=0x201000C90025", "可能原因": "未知"}, {"主题": "[次要告警]Backup发生eBackup服务器到NTP服务器连接异常", "告警ID": "0x201000C90024", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.70.76", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.132.60)，错误代码=0x201000C90024", "可能原因": "未知"}, {"主题": "[次要告警]Backup发生eBackup服务器未设置NTP时钟源", "告警ID": "0x201000C90009", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.213.28", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.165.92)，错误代码=0x201000C90009", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生SFTP服务器空间不足", "告警ID": "0x5800790001", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.20.93", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.5.139)，错误代码=0x5800790001", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生登录SFTP服务器被拒绝", "告警ID": "0x5800790002", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.211.185", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.206.198)，错误代码=0x5800790002", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生上传管理数据到SFTP服务器失败", "告警ID": "0x5800790003", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.89.61", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.60.159)，错误代码=0x5800790003", "可能原因": "未知"}, {"主题": "[严重告警]Backup发生备份副本中缺失受保护对象元数据", "告警ID": "0x10E01A0010", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.111.62", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.87.157)，错误代码=0x10E01A0010", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生试用期已过", "告警ID": "0x201000F40005", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.236.166", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.200.139)，错误代码=0x201000F40005", "可能原因": "未知"}, {"主题": "[次要告警]Backup发生备份副本的元数据损坏", "告警ID": "0x10E01A0011", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.100.234", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.48.101)，错误代码=0x10E01A0011", "可能原因": "未知"}, {"主题": "[次要告警]Backup发生删除备份副本失败", "告警ID": "0x10E01A0019", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.88.187", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.74.174)，错误代码=0x10E01A0019", "可能原因": "未知"}, {"主题": "[重要告警]Backup发生检测到备份副本的数据块有损坏", "告警ID": "0x2010E01A0008", "告警级别": "重要", "告警源": "Backup", "来源系统": "eBackup10.200.185.250", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.117.8)，错误代码=0x2010E01A0008", "可能原因": "未知"}, {"主题": "[重要告警]Backup发生验证备份副本的任务失败", "告警ID": "0x2010E01A000E", "告警级别": "重要", "告警源": "Backup", "来源系统": "eBackup10.200.191.198", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.131.45)，错误代码=0x2010E01A000E", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生Proxy上的NTP服务异常", "告警ID": "0x201000C9000A", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.229.253", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.179.143)，错误代码=0x201000C9000A", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生连接远端vpp服务器失败", "告警ID": "0x210000000D00", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.154.252", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.245.163)，错误代码=0x210000000D00", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生清理复制任务的残留资源失败", "告警ID": "0x201000C90021", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.62.240", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.219.191)，错误代码=0x201000C90021", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生复制任务失败", "告警ID": "0x2010E01A001D", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.41.67", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.88.35)，错误代码=0x2010E01A001D", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生卸载FusionStorage卷失败", "告警ID": "0x10E01A0014", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.16.158", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.110.209)，错误代码=0x10E01A0014", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生删除FusionStorage卷失败", "告警ID": "0x10E01A0015", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.253.169", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.76.40)，错误代码=0x10E01A0015", "可能原因": "未知"}, {"主题": "[严重告警]License发生License授权容量耗尽", "告警ID": "0x201000F40007", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.124.169", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.117.127)，错误代码=0x201000F40007", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生卸载OceanStor V3_V5卷或者Dorad", "告警ID": "0x10E01A0017", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.187.143", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.196.108)，错误代码=0x10E01A0017", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生重删数据有冗余", "告警ID": "0x6300740001", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.110.64", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.99.99)，错误代码=0x6300740001", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生访问ManageOne失败", "告警ID": "0x6000840003", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.248.248", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.80.195)，错误代码=0x6000840003", "可能原因": "未知"}, {"主题": "[严重告警]Backup发生备份代理存在进度长时间未更新任务", "告警ID": "0x105800740001", "告警级别": "严重", "告警源": "Backup", "来源系统": "eBackup10.200.35.227", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.91.20)，错误代码=0x105800740001", "可能原因": "未知"}, {"主题": "[提示信息]附录", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.1.65", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]登录eBackup服务器", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.142.204", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]Ka<PERSON><PERSON>", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.84.127", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Process发生创建云服务器复制副本失败", "告警ID": "1020799", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.24.37", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.19.32)，错误代码=1020799", "可能原因": "未知"}, {"主题": "[次要告警]System发生云硬盘备份策略自动调度失败", "告警ID": "1020771", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.239.247", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.68.56)，错误代码=1020771", "可能原因": "未知"}, {"主题": "[重要告警]System发生云硬盘复制策略自动调度失败", "告警ID": "1020770", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.155.65", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.55.107)，错误代码=1020770", "可能原因": "未知"}, {"主题": "[次要告警]System发生云硬盘备份失败", "告警ID": "1020768", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.101.248", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.66.193)，错误代码=1020768", "可能原因": "未知"}, {"主题": "[重要告警]System发生FSP证书校验失败", "告警ID": "1020762", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.120.156", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.42.33)，错误代码=1020762", "可能原因": "未知"}, {"主题": "[次要告警]System发生IAM证书校验失败", "告警ID": "1020761", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.70.129", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.41.226)，错误代码=1020761", "可能原因": "未知"}, {"主题": "[重要告警]System发生节点状态异常", "告警ID": "1023299", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.88.115", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.158.129)，错误代码=1023299", "可能原因": "未知"}, {"主题": "[重要告警]System发生组件状态异常", "告警ID": "1023298", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.139.87", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.85.69)，错误代码=1023298", "可能原因": "未知"}, {"主题": "[重要告警]System发生外部NTP时钟同步异常", "告警ID": "1023296", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.137.11", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.33.83)，错误代码=1023296", "可能原因": "未知"}, {"主题": "[次要告警]System发生备份系统数据发生失败", "告警ID": "1023295", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.3.220", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.194.37)，错误代码=1023295", "可能原因": "未知"}, {"主题": "[次要告警]Process发生FTP服务器证书校验失败", "告警ID": "1023282", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.16.110", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.234.90)，错误代码=1023282", "可能原因": "未知"}, {"主题": "[次要告警]Process发生云服务器备份配额消耗到配额总量的阈值", "告警ID": "1020796", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.40.38", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.123.43)，错误代码=1020796", "可能原因": "未知"}, {"主题": "[次要告警]System发生系统证书即将过期", "告警ID": "1023279", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.190.206", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.156.170)，错误代码=1023279", "可能原因": "未知"}, {"主题": "[重要告警]System发生系统证书已经过期", "告警ID": "1023278", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.76.58", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.184.66)，错误代码=1023278", "可能原因": "未知"}, {"主题": "[次要告警]System发生消息队列卡死", "告警ID": "1023277", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.127.163", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.38.204)，错误代码=1023277", "可能原因": "未知"}, {"主题": "[次要告警]Network发生消息队列产生网络分区", "告警ID": "1023276", "告警级别": "次要", "告警源": "Network", "来源系统": "SystemOM10.200.114.214", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=compute", "附加信息": "云服务=Network，服务=compute，节点地址=(10.200.15.42)，错误代码=1023276", "可能原因": "未知"}, {"主题": "[次要告警]System发生CPU使用率超过阈值", "告警ID": "1023099", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.61.156", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.209.252)，错误代码=1023099", "可能原因": "未知"}, {"主题": "[次要告警]System发生内存使用率超过阈值", "告警ID": "1023098", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.68.34", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.197.72)，错误代码=1023098", "可能原因": "未知"}, {"主题": "[次要告警]Storage发生磁盘使用率超过阈值", "告警ID": "1023097", "告警级别": "次要", "告警源": "Storage", "来源系统": "SystemOM10.200.70.145", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=compute", "附加信息": "云服务=Storage，服务=compute，节点地址=(10.200.9.74)，错误代码=1023097", "可能原因": "未知"}, {"主题": "[重要告警]System发生执行复制策略失败", "告警ID": "1020800", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.38.205", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.59.120)，错误代码=1020800", "可能原因": "未知"}, {"主题": "[重要告警]System发生跨区域复制策略自动调度失败", "告警ID": "1020803", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.93.107", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.58.33)，错误代码=1020803", "可能原因": "未知"}, {"主题": "[次要告警]Network发生连接ManageOne运营平台失败", "告警ID": "1020759", "告警级别": "次要", "告警源": "Network", "来源系统": "SystemOM10.200.226.198", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=compute", "附加信息": "云服务=Network，服务=compute，节点地址=(10.200.99.158)，错误代码=1020759", "可能原因": "未知"}, {"主题": "[重要告警]Process发生云服务器备份策略自动调度失败", "告警ID": "1020791", "告警级别": "重要", "告警源": "Process", "来源系统": "SystemOM10.200.234.53", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.254.201)，错误代码=1020791", "可能原因": "未知"}, {"主题": "[重要告警]System发生上报计量数据失败", "告警ID": "1020758", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.213.121", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.220.72)，错误代码=1020758", "可能原因": "未知"}, {"主题": "[次要告警]Network发生备份服务节点间网络异常", "告警ID": "1023093", "告警级别": "次要", "告警源": "Network", "来源系统": "SystemOM10.200.155.168", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=compute", "附加信息": "云服务=Network，服务=compute，节点地址=(10.200.56.62)，错误代码=1023093", "可能原因": "未知"}, {"主题": "[次要告警]Process发生注册CSBS-VBS到统一证书管理服务失败", "告警ID": "1020756", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.181.134", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.201.146)，错误代码=1020756", "可能原因": "未知"}, {"主题": "[次要告警]Process发生云服务器备份复制策略自动调度失败", "告警ID": "1020790", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.7.148", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.182.243)，错误代码=1020790", "可能原因": "未知"}, {"主题": "[次要告警]Process发生云服务器备份失败", "告警ID": "1020788", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.7.205", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.92.244)，错误代码=1020788", "可能原因": "未知"}, {"主题": "[次要告警]Process发生启动消息队列服务失败", "告警ID": "1020783", "告警级别": "次要", "告警源": "Process", "来源系统": "SystemOM10.200.215.122", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=compute", "附加信息": "云服务=Process，服务=compute，节点地址=(10.200.230.249)，错误代码=1020783", "可能原因": "未知"}, {"主题": "[重要告警]System发生消息队列存在消息响应超时", "告警ID": "1020782", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.64.249", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.2.208)，错误代码=1020782", "可能原因": "未知"}, {"主题": "[次要告警]System发生创建云硬盘复制副本失败", "告警ID": "1020779", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.182.123", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.62.55)，错误代码=1020779", "可能原因": "未知"}, {"主题": "[重要告警]System发生云硬盘备份配额消耗到配额总量的阈值", "告警ID": "1020776", "告警级别": "重要", "告警源": "System", "来源系统": "SystemOM10.200.222.112", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.241.213)，错误代码=1020776", "可能原因": "未知"}, {"主题": "[提示信息]eReplication", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.187.169", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Backup发生备份失败", "告警ID": "0x3230014", "告警级别": "次要", "告警源": "Backup", "来源系统": "eBackup10.200.214.53", "定位信息": "区域=SH_CSVW，云服务=Backup，节点类型=backup", "附加信息": "云服务=Backup，服务=backup，节点地址=(10.200.190.56)，错误代码=0x3230014", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生HA网关不通", "告警ID": "0x3230034", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.142.86", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.188.92)，错误代码=0x3230034", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生仲裁服务异常", "告警ID": "0x3230036", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.91.163", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.234.147)，错误代码=0x3230036", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生服务实例故障恢复失败", "告警ID": "0x3230037", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.8.19", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.41.187)，错误代码=0x3230037", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生服务实例重保护失败", "告警ID": "0x3230038", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.54.194", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.199.181)，错误代码=0x3230038", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生虚拟机不满足保护要求", "告警ID": "0x323003A", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.166.33", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.220.186)，错误代码=0x323003A", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生虚拟机中已卸载的卷未从服务实例中清理", "告警ID": "0x323003B", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.16.159", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.236.251)，错误代码=0x323003B", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生虚拟机的卷未创建容灾保护", "告警ID": "0x323003C", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.69.7", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.37.211)，错误代码=0x323003C", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生受保护虚拟机从服务实例中被移除", "告警ID": "0x323003D", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.19.241", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.149.155)，错误代码=0x323003D", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生服务实例不满足故障恢复要求", "告警ID": "0x323003E", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.238.2", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.185.166)，错误代码=0x323003E", "可能原因": "未知"}, {"主题": "[重要告警]Certificate发生IAM证书更新失败", "告警ID": "0x323003F", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.245.203", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.211.221)，错误代码=0x323003F", "可能原因": "未知"}, {"主题": "[次要告警]Certificate发生证书已过期", "告警ID": "0x3230024", "告警级别": "次要", "告警源": "Certificate", "来源系统": "eBackup10.200.55.18", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.241.242)，错误代码=0x3230024", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生上报计量信息失败", "告警ID": "0x3230041", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.71.136", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.99.1)，错误代码=0x3230041", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生连接日志服务器异常", "告警ID": "0x3230042", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.229.204", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.118.67)，错误代码=0x3230042", "可能原因": "未知"}, {"主题": "[次要告警]Database发生高可用集群中高斯数据库主备间复制中断", "告警ID": "0x3230043", "告警级别": "次要", "告警源": "Database", "来源系统": "eBackup10.200.2.56", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=backup", "附加信息": "云服务=Database，服务=backup，节点地址=(10.200.152.74)，错误代码=0x3230043", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生共享卷关联的虚拟机不在同一个服务实例中", "告警ID": "0x3230046", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.93.28", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.57.249)，错误代码=0x3230046", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生订单实施结果通知失败", "告警ID": "0x3230047", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.38.79", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.176.92)，错误代码=0x3230047", "可能原因": "未知"}, {"主题": "[严重告警]License发生License授权容量耗尽", "告警ID": "0x3230048", "告警级别": "严重", "告警源": "License", "来源系统": "eBackup10.200.226.74", "定位信息": "区域=SH_CSVW，云服务=License，节点类型=backup", "附加信息": "云服务=License，服务=backup，节点地址=(10.200.101.187)，错误代码=0x3230048", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生系统90天试用期到期", "告警ID": "0x3230049", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.99.21", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.165.226)，错误代码=0x3230049", "可能原因": "未知"}, {"主题": "[重要告警]Certificate发生证书即将过期", "告警ID": "0x323005C", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.186.84", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.2.130)，错误代码=0x323005C", "可能原因": "未知"}, {"主题": "[重要告警]Certificate发生证书已经过期", "告警ID": "0x323005D", "告警级别": "重要", "告警源": "Certificate", "来源系统": "eBackup10.200.47.243", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.131.116)，错误代码=0x323005D", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生对接管理平台失败", "告警ID": "0x3230064", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.63.18", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.31.106)，错误代码=0x3230064", "可能原因": "未知"}, {"主题": "[严重告警]Certificate发生证书校验失败", "告警ID": "0x3230025", "告警级别": "严重", "告警源": "Certificate", "来源系统": "eBackup10.200.171.128", "定位信息": "区域=SH_CSVW，云服务=Certificate，节点类型=backup", "附加信息": "云服务=Certificate，服务=backup，节点地址=(10.200.55.50)，错误代码=0x3230025", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生占位虚拟机不存在", "告警ID": "0x323002C", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.53.242", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.68.82)，错误代码=0x323002C", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生占位虚拟机未配置", "告警ID": "0x323002D", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.214.199", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.28.101)，错误代码=0x323002D", "可能原因": "未知"}, {"主题": "[次要告警]eBackup发生一致性组状态异常", "告警ID": "0x323002F", "告警级别": "次要", "告警源": "eBackup", "来源系统": "eBackup10.200.27.150", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.121.244)，错误代码=0x323002F", "可能原因": "未知"}, {"主题": "[严重告警]eBackup发生HA心跳中断", "告警ID": "0x3230030", "告警级别": "严重", "告警源": "eBackup", "来源系统": "eBackup10.200.172.37", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.48.59)，错误代码=0x3230030", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生HA同步失败", "告警ID": "0x3230031", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.237.217", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.164.245)，错误代码=0x3230031", "可能原因": "未知"}, {"主题": "[重要告警]eBackup发生HA链路中断", "告警ID": "0x3230033", "告警级别": "重要", "告警源": "eBackup", "来源系统": "eBackup10.200.223.8", "定位信息": "区域=SH_CSVW，云服务=eBackup，节点类型=backup", "附加信息": "云服务=eBackup，服务=backup，节点地址=(10.200.193.96)，错误代码=0x3230033", "可能原因": "未知"}, {"主题": "[提示信息]消息通知服务", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.41.162", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生tomcat进程不存在", "告警ID": "2000401", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.20.151", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.245.250)，对端地址=(other_process=10.200.67.34)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生GaussdbHA上传远端备份服务器失败", "告警ID": "2000463", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.200.146", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.156.59)，对端地址=(other_database=10.200.17.144)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生zookeeper进程不存在", "告警ID": "2000471", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.18.183", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.47.147)，对端地址=(other_process=10.200.174.169)", "可能原因": "未知"}, {"主题": "[严重告警]System发生证书异常", "告警ID": "2000493", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.59.255", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.69.186)，对端地址=(other_system=10.200.163.140)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生ns进程不存在", "告警ID": "2000420", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.50.162", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.202.254)，对端地址=(other_process=10.200.241.1)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生ps进程不存在", "告警ID": "2000421", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.69.169", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.83.51)，对端地址=(other_process=10.200.14.95)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生memcached进程不存在", "告警ID": "2000456", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.81.213", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.93.247)，对端地址=(other_process=10.200.200.137)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生kafka进程不存在", "告警ID": "2000469", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.121.238", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.112.49)，对端地址=(other_process=10.200.59.18)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生smn_haproxy进程不存在", "告警ID": "2000488", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.110.221", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.17.111)，对端地址=(other_process=10.200.3.141)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生smn_keepalived进程不存在", "告警ID": "2000489", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.113.243", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.72.34)，对端地址=(other_process=10.200.26.214)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生GaussdbHA服务进程异常", "告警ID": "2000460", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.76.32", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.247.135)，对端地址=(other_database=10.200.248.40)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生GaussdbHA备份失败", "告警ID": "2000462", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.144.83", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.100.122)，对端地址=(other_database=10.200.34.158)", "可能原因": "未知"}, {"主题": "[提示信息]公共组件", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.30.137", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]ECS UI", "告警ID": "", "告警级别": "提示", "告警源": "Compute", "来源系统": "ManageOne10.200.58.198", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=management", "附加信息": "云服务=Compute，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Process发生ECS_UI Tomcat进程不存在", "告警ID": "1160001", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.18.144", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.92.180)，对端地址=(other_process=10.200.117.118)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生ECS_UI中ntp进程故障", "告警ID": "1160003", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.192.43", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.240.228)，对端地址=(other_process=10.200.83.218)", "可能原因": "未知"}, {"主题": "[提示信息]SDR", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.116.61", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]计量话单告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.122.147", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]System发生计量话单生成话单失败", "告警ID": "2000301", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.60.36", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.243.84)，对端地址=(other_system=10.200.239.204)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生计量话单服务异常", "告警ID": "2000317", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.234.215", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.83.110)，对端地址=(other_process=10.200.125.144)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生计量话单证书告警", "告警ID": "2000327", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.217.212", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.136.80)，对端地址=(other_monitor=10.200.230.97)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生计量话单证书告警", "告警ID": "2000328", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.133.19", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.141.91)，对端地址=(other_monitor=10.200.131.125)", "可能原因": "未知"}, {"主题": "[提示信息]参考信息", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.205.178", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]配置屏蔽规则", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.73.148", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]CCS", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.191.80", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Process发生ccs进程异常", "告警ID": "1320004", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.216.40", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.152.105)，对端地址=(other_process=10.200.216.78)", "可能原因": "未知"}, {"主题": "[次要告警]System发生证书异常", "告警ID": "1320019", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.110.53", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.246.167)，对端地址=(other_system=10.200.203.77)", "可能原因": "未知"}, {"主题": "[提示信息]云平台仲裁服务", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.18.224", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]System发生节点系统时钟跳变超过一分钟", "告警ID": "2000266", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.219.109", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.100.177)，对端地址=(other_system=10.200.182.190)", "可能原因": "未知"}, {"主题": "[次要告警]ETCD发生Etcd服务状态异常", "告警ID": "2001106", "告警级别": "次要", "告警源": "ETCD", "来源系统": "ServiceOM10.200.206.148", "定位信息": "区域=SH_CSVW，云服务=ETCD，节点类型=mgt", "附加信息": "云服务=ETCD，服务=mgt，本端地址=(10.200.188.31)，对端地址=(other_etcd=10.200.36.39)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Monitor进程异常", "告警ID": "2002101", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.248.102", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.57.53)，对端地址=(other_process=10.200.65.5)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生Monitor与对端站点通信异常", "告警ID": "2002302", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.244.169", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.243.109)，对端地址=(other_monitor=10.200.86.144)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生站点网络状态异常", "告警ID": "2002501", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.91.55", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.81.227)，对端地址=(other_network=10.200.238.155)", "可能原因": "未知"}, {"主题": "[严重告警]System发生第三方站点异常", "告警ID": "2001107", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.129.25", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.113.160)，对端地址=(other_system=10.200.125.81)", "可能原因": "未知"}, {"主题": "[提示信息]DMK", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.127.125", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Process发生dmk服务异常", "告警ID": "8000888", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.250.232", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.89.171)，对端地址=(other_process=10.200.41.35)", "可能原因": "未知"}, {"主题": "[提示信息]GaussDB", "告警ID": "", "告警级别": "提示", "告警源": "Database", "来源系统": "ManageOne10.200.73.134", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=management", "附加信息": "云服务=Database，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Database发生GaussdbHA服务进程异常", "告警ID": "1510000", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.84.97", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.152.21)，对端地址=(other_database=10.200.100.195)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生GaussdbHA备份失败", "告警ID": "1510002", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.198.125", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.233.161)，对端地址=(other_database=10.200.207.181)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生GaussdbHA证书异常", "告警ID": "1510003", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.8.85", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.4.66)，对端地址=(other_database=10.200.164.55)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生GaussdbHA主备同步异常", "告警ID": "1510005", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.67.237", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.151.143)，对端地址=(other_database=10.200.250.154)", "可能原因": "未知"}, {"主题": "[次要告警]Database发生GaussdbHA上传远端备份服务器失败", "告警ID": "1510006", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.65.199", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.104.247)，对端地址=(other_database=10.200.205.63)", "可能原因": "未知"}, {"主题": "[提示信息]LVS", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.183.83", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生lvs_keepalived进程不存在", "告警ID": "2001101", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.141.194", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.107.215)，对端地址=(other_process=10.200.165.88)", "可能原因": "未知"}, {"主题": "[提示信息]Nginx", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.188.115", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生nginx服务异常", "告警ID": "2001002", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.51.168", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.233.116)，对端地址=(other_process=10.200.53.57)", "可能原因": "未知"}, {"主题": "[提示告警]System发生证书异常", "告警ID": "2001006", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.193.226", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.153.220)，对端地址=(other_system=10.200.89.121)", "可能原因": "未知"}, {"主题": "[提示信息]DNS", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.3.246", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生DNS named进程异常", "告警ID": "8000021", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.133.179", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.103.118)，对端地址=(other_process=10.200.248.209)", "可能原因": "未知"}, {"主题": "[提示信息]NTP", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.74.138", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生NTP时钟源偏移告警", "告警ID": "7700073", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.190.17", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.125.53)，对端地址=(other_monitor=10.200.37.177)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生NTP进程异常", "告警ID": "7700071", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.118.133", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.25.220)，对端地址=(other_process=10.200.198.135)", "可能原因": "未知"}, {"主题": "[提示信息]HAProxy", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.80.16", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Process发生haproxy服务异常", "告警ID": "2000904", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.192.245", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(************)，对端地址=(other_process=**************)", "可能原因": "未知"}, {"主题": "[重要告警]System发生haproxy的浮动IP不可达", "告警ID": "2000906", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.83.221", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=*************)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生keepalived进程不存在", "告警ID": "2000908", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.234.206", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(************)，对端地址=(other_process=*************)", "可能原因": "未知"}, {"主题": "[提示告警]System发生haproxy浮动IP端口不可达", "告警ID": "2000909", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.152.122", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=*************)", "可能原因": "未知"}, {"主题": "[提示信息]TaskCenter", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.66.56", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Process发生taskcenter服务异常", "告警ID": "2000722", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.50.122", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.168.55)，对端地址=(other_process=10.200.38.103)", "可能原因": "未知"}, {"主题": "[重要告警]System发生证书异常", "告警ID": "2000724", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.202.153", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.45.170)，对端地址=(other_system=10.200.198.232)", "可能原因": "未知"}, {"主题": "[提示信息]APIGateway", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.250.143", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]通信告警", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.145.70", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Unknown发生", "告警ID": "48101-无法访问shubao", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.33.145", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.17.10)，对端地址=(other_unknown=10.200.216.114)", "可能原因": "未知"}, {"主题": "[严重告警]Unknown发生", "告警ID": "48118-无法访问redis-mgr", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.157.93", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.197.34)，对端地址=(other_unknown=10.200.48.34)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "48102-无法访问etcd", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.194.235", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.187.196)，对端地址=(other_unknown=10.200.36.173)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "48103-无法访问orchestration", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.1.166", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.150.75)，对端地址=(other_unknown=10.200.21.21)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "48104-无法访问authadv", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.136.139", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.230.45)，对端地址=(other_unknown=10.200.146.255)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48107-无法访问FTP", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.115.115", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.157.4)，对端地址=(other_unknown=10.200.172.10)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48108-无法访问shubao-orchestration", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.63.165", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.139.50)，对端地址=(other_unknown=10.200.83.15)", "可能原因": "未知"}, {"主题": "[重要告警]Unknown发生", "告警ID": "48110-无法访问DB", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.55.21", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.135.89)，对端地址=(other_unknown=10.200.237.45)", "可能原因": "未知"}, {"主题": "[重要告警]Unknown发生", "告警ID": "48111-无法访问apimgr", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.170.222", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.132.213)，对端地址=(other_unknown=10.200.147.163)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "48117-无法正常接收opsagent心跳", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.145.249", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.62.203)，对端地址=(other_unknown=10.200.62.144)", "可能原因": "未知"}, {"主题": "[提示信息]处理错误告警", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.89.41", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Unknown发生", "告警ID": "48303-无法读写FTP文件", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.206.179", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.217.203)，对端地址=(other_unknown=10.200.151.32)", "可能原因": "未知"}, {"主题": "[重要告警]Unknown发生", "告警ID": "48304-证书即将过期", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.91.28", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.207.211)，对端地址=(other_unknown=10.200.23.66)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48305-证书已经过期", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.214.248", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.80.147)，对端地址=(other_unknown=10.200.100.53)", "可能原因": "未知"}, {"主题": "[严重告警]Unknown发生", "告警ID": "48306-证书校验失败", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.241.61", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.111.154)，对端地址=(other_unknown=10.200.225.26)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "48316-请求超出单实例流控阈值", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.131.128", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.58.155)，对端地址=(other_unknown=10.200.250.223)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "48317-重新加载LB失败", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.18.122", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.151.151)，对端地址=(other_unknown=10.200.242.133)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48318-证书回退失败", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.146.65", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.51.136)，对端地址=(other_unknown=10.200.113.240)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48319-证书无法生效", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.159.251", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.241.17)，对端地址=(other_unknown=10.200.119.10)", "可能原因": "未知"}, {"主题": "[严重告警]Unknown发生", "告警ID": "48320-证书即将过期", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.102.204", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.203.118)，对端地址=(other_unknown=10.200.55.83)", "可能原因": "未知"}, {"主题": "[提示信息]设备告警", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.65.2", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48401-进程未启动", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.86.218", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.216.1)，对端地址=(other_unknown=10.200.46.245)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "48402-端口冲突", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.141.43", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.41.214)，对端地址=(other_unknown=10.200.181.226)", "可能原因": "未知"}, {"主题": "[重要告警]Unknown发生", "告警ID": "48409-文件句柄使用率超过门限值", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.134.203", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.218.94)，对端地址=(other_unknown=10.200.187.168)", "可能原因": "未知"}, {"主题": "[提示信息]FusionSphere OpenStack告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.23.74", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Process发生上传日志到OBS服务失败", "告警ID": "6008", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.162.17", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.137.203)，对端地址=(other_process=10.200.213.126)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生主机网口状态异常", "告警ID": "6021", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.74.251", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.25.168)，对端地址=(other_network=10.200.134.96)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生裸金属服务器审计告警", "告警ID": "1126002", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.14.30", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.99.87)，对端地址=(other_process=10.200.190.34)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生DHCP服务不可用", "告警ID": "1200067", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.59.102", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.144.70)，对端地址=(other_process=10.200.43.225)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生主机设备端口错包告警", "告警ID": "1200075", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.105.155", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.61.244)，对端地址=(other_network=10.200.191.162)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机设备端口丢包告警", "告警ID": "1200076", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.94.159", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.174.198)，对端地址=(other_network=10.200.8.194)", "可能原因": "未知"}, {"主题": "[次要告警]System发生Dpdk lacp bond聚合失败", "告警ID": "1200077", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.185.12", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.3.69)，对端地址=(other_system=10.200.69.248)", "可能原因": "未知"}, {"主题": "[严重告警]ELB发生负载均衡器后端实例不在线", "告警ID": "1223017", "告警级别": "严重", "告警源": "ELB", "来源系统": "ServiceOM10.200.196.110", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.2.22)，对端地址=(other_elb=10.200.193.91)", "可能原因": "未知"}, {"主题": "[次要告警]System发生FusionSphere Neutron和Agile Controller-DCN 进行数据一致性修", "告警ID": "1240001", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.89.87", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.250.95)，对端地址=(other_system=10.200.245.64)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生FusionSphere Neutron和Agile Controller-DCN HTTP连接故障", "告警ID": "1240002", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.150.65", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.97.147)，对端地址=(other_network=10.200.124.104)", "可能原因": "未知"}, {"主题": "[提示告警]System发生FusionSphere Neutron和Agile Controller-DCN Websocke", "告警ID": "1240003", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.21.106", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.213.52)，对端地址=(other_system=10.200.198.122)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生EVS_OVS-DPDK 转发能力达到瓶颈告警", "告警ID": "1301021", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.100.59", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.206.190)，对端地址=(other_monitor=10.200.34.159)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机与NTP服务器心跳状态异常", "告警ID": "6022", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.71.196", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.45.213)，对端地址=(other_network=10.200.247.114)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生OpenStack服务包升级完成后未提交", "告警ID": "1316000", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.254.86", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.222.155)，对端地址=(other_process=10.200.187.205)", "可能原因": "未知"}, {"主题": "[次要告警]System发生忽略升级的单板未处理", "告警ID": "1316001", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.79.209", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.94.230)，对端地址=(other_system=10.200.103.176)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生热补丁运行异常告警", "告警ID": "1316002", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.103.11", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.133.251)，对端地址=(other_monitor=10.200.69.34)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生QEMU版本异常告警", "告警ID": "1316003", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.92.180", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.146.94)，对端地址=(other_monitor=10.200.38.227)", "可能原因": "未知"}, {"主题": "[提示告警]System发生RabbitMQ资源使用率超过阈值", "告警ID": "1507002", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.86.31", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.148.146)，对端地址=(other_system=10.200.178.54)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机存储链路中断", "告警ID": "6023", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.138.103", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.151.84)，对端地址=(other_network=10.200.191.13)", "可能原因": "未知"}, {"主题": "[重要告警]Storage发生存储资源管理链路中断或认证失败", "告警ID": "6024", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.55.35", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.244.175)，对端地址=(other_storage=10.200.87.116)", "可能原因": "未知"}, {"主题": "[重要告警]Storage发生存储使用率超过阈值", "告警ID": "6025", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.103.171", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.202.122)，对端地址=(other_storage=10.200.37.216)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生主机光纤通道中断", "告警ID": "6026", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.90.144", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.209.15)，对端地址=(other_network=10.200.143.179)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生网口自协商速率没有达到服务器网口最大速率的一半", "告警ID": "6027", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.222.134", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.223.181)，对端地址=(other_process=10.200.187.11)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生本地NTP客户端与本地NTP服务器时间差超过60秒", "告警ID": "6028", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.84.225", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.117.91)，对端地址=(other_process=************)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生服务自动备份失败", "告警ID": "6029", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.44.187", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(************)，对端地址=(other_process=**************)", "可能原因": "未知"}, {"主题": "[严重告警]System发生IP冲突故障", "告警ID": "6030", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.182.9", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(***********)，对端地址=(other_system=*************)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生NTP服务器与外部时钟源时间差超过阈值", "告警ID": "6010", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.224.125", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(*************)，对端地址=(other_process=*************)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生上传日志到FTP服务器失败", "告警ID": "6031", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.114.167", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.88.63)，对端地址=(other_process=10.200.231.19)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生I层服务CPU占用率超过阈值", "告警ID": "6033", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.126.174", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.217.107)，对端地址=(other_process=10.200.165.157)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生I层服务内存占用率超过阈值", "告警ID": "6034", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.147.56", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.130.182)，对端地址=(other_process=10.200.242.19)", "可能原因": "未知"}, {"主题": "[次要告警]Compute发生虚拟机CPU占用率超过阈值", "告警ID": "6036", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.141.139", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.73.90)，对端地址=(other_compute=10.200.137.250)", "可能原因": "未知"}, {"主题": "[重要告警]Compute发生虚拟机虚拟内存_Swap分区占用率超过阈值", "告警ID": "6037", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.127.242", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.203.22)，对端地址=(other_compute=10.200.248.72)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生主机磁盘占用率超过阈值", "告警ID": "6038", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.64.105", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.248.39)，对端地址=(other_network=10.200.148.115)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机虚拟化域内存占用率超过阈值", "告警ID": "6039", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.155.95", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.19.39)，对端地址=(other_network=10.200.66.91)", "可能原因": "未知"}, {"主题": "[重要告警]Compute发生虚拟机审计告警", "告警ID": "70100", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.195.111", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.189.127)，对端地址=(other_compute=10.200.35.223)", "可能原因": "未知"}, {"主题": "[提示告警]Compute发生虚拟机操作系统故障告警", "告警ID": "70101", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.81.30", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.222.142)，对端地址=(other_compute=10.200.12.100)", "可能原因": "未知"}, {"主题": "[次要告警]Compute发生虚拟机ERROR状态告警", "告警ID": "70102", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.224.137", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.232.53)，对端地址=(other_compute=10.200.174.224)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生DNS服务器连接中断", "告警ID": "6014", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.100.4", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.33.35)，对端地址=(other_network=10.200.20.119)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机VCPU使用数目超过限制", "告警ID": "70103", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.159.141", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.47.1)，对端地址=(other_network=10.200.89.90)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机内存使用超过主机总内存", "告警ID": "70104", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.147.39", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.195.108)，对端地址=(other_network=10.200.115.131)", "可能原因": "未知"}, {"主题": "[提示告警]Compute发生异构VMware虚拟机卷数据残留告警", "告警ID": "70105", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.151.221", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.171.212)，对端地址=(other_compute=10.200.71.73)", "可能原因": "未知"}, {"主题": "[重要告警]Compute发生虚拟机HA中间态告警", "告警ID": "70106", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.92.49", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.225.94)，对端地址=(other_compute=10.200.141.61)", "可能原因": "未知"}, {"主题": "[提示告警]Compute发生虚拟机目录文件异常", "告警ID": "70108", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.133.242", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.221.64)，对端地址=(other_compute=10.200.159.23)", "可能原因": "未知"}, {"主题": "[严重告警]Compute发生虚拟机BDM残留审计告警", "告警ID": "70109", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.253.179", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.162.76)，对端地址=(other_compute=10.200.179.214)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生本地盘ERROR状态告警", "告警ID": "70111", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.246.86", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.106.23)，对端地址=(other_monitor=10.200.23.111)", "可能原因": "未知"}, {"主题": "[严重告警]System发生IB网卡故障", "告警ID": "70112", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.210.13", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.159.199)，对端地址=(other_system=10.200.96.44)", "可能原因": "未知"}, {"主题": "[严重告警]Compute发生NVME SSD盘或卡故障", "告警ID": "70113", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.52.45", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.154.134)，对端地址=(other_compute=10.200.157.80)", "可能原因": "未知"}, {"主题": "[严重告警]Compute发生异构VMware野虚拟机残留告警", "告警ID": "70126", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.53.169", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.42.40)，对端地址=(other_compute=10.200.28.94)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生NTP服务器与外部时钟源网络故障或外部时钟源故障", "告警ID": "6015", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.214.224", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.65.60)，对端地址=(other_network=10.200.99.216)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机组内NVMe SSD大盘使用率超过阈值", "告警ID": "70127", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.226.111", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.70.56)，对端地址=(other_network=10.200.8.175)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机组内NVMe SSD小盘使用率超过阈值", "告警ID": "70128", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.206.105", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.156.38)，对端地址=(other_network=10.200.226.232)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机组内GPU使用率超过阈值", "告警ID": "70129", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.139.22", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.35.19)，对端地址=(other_network=10.200.128.106)", "可能原因": "未知"}, {"主题": "[重要告警]Compute发生虚拟机HA失败", "告警ID": "70130", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.137.63", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.226.244)，对端地址=(other_compute=10.200.228.219)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机组CPU分配率超过阈值", "告警ID": "70131", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.101.131", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.67.17)，对端地址=(other_network=10.200.170.213)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机组内存分配率超过阈值", "告警ID": "70132", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.62.151", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.194.42)，对端地址=(other_network=10.200.27.17)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生内存复用率超过阈值告警", "告警ID": "70135", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.195.196", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.109.92)，对端地址=(other_monitor=10.200.109.154)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生DHCP-agent管理的network数量超过阈值", "告警ID": "70201", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.193.82", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.204.118)，对端地址=(other_network=10.200.44.103)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生主机上虚拟端口上线处理异常", "告警ID": "70203", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.254.171", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.100.203)，对端地址=(other_network=10.200.71.31)", "可能原因": "未知"}, {"主题": "[重要告警]System发生聚合网口状态异常", "告警ID": "70251", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.194.202", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.134.69)，对端地址=(other_system=10.200.204.166)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生日志服务连接OBS失败", "告警ID": "6016", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.20.105", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.15.102)，对端地址=(other_network=10.200.55.247)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生卷审计告警", "告警ID": "70300", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.103.198", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.181.57)，对端地址=(other_monitor=10.200.38.235)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生快照审计告警", "告警ID": "70310", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.216.24", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.180.105)，对端地址=(other_monitor=10.200.192.101)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生镜像审计告警", "告警ID": "70400", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.146.110", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.245.49)，对端地址=(other_monitor=10.200.64.1)", "可能原因": "未知"}, {"主题": "[次要告警]System发生镜像完整性校验失败", "告警ID": "70401", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.232.221", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.214.33)，对端地址=(other_system=10.200.108.52)", "可能原因": "未知"}, {"主题": "[次要告警]System发生整机快照残留", "告警ID": "70402", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.126.75", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.69.239)，对端地址=(other_system=10.200.123.255)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生虚拟网络资源审计告警", "告警ID": "73008", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.138.72", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.142.96)，对端地址=(other_network=10.200.64.161)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生文件系统故障告警", "告警ID": "73010", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.253.218", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.177.172)，对端地址=(other_monitor=10.200.141.126)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生关键进程异常告警", "告警ID": "73011", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.164.94", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.88.70)，对端地址=(other_process=10.200.210.9)", "可能原因": "未知"}, {"主题": "[次要告警]Storage发生磁盘分区inode资源不足告警", "告警ID": "73012", "告警级别": "次要", "告警源": "Storage", "来源系统": "ServiceOM10.200.185.142", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.147.133)，对端地址=(other_storage=10.200.80.99)", "可能原因": "未知"}, {"主题": "[重要告警]Storage发生存储磁盘I_O时延过大告警", "告警ID": "73013", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.128.136", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.190.70)，对端地址=(other_storage=10.200.153.218)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机状态异常", "告警ID": "6017", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.136.60", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.8.64)，对端地址=(other_network=10.200.158.63)", "可能原因": "未知"}, {"主题": "[提示告警]System发生系统文件完整性异常", "告警ID": "73014", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.211.212", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.126.80)，对端地址=(other_system=10.200.76.81)", "可能原因": "未知"}, {"主题": "[重要告警]System发生大页内存不足", "告警ID": "73015", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.195.183", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.168.43)，对端地址=(other_system=10.200.193.140)", "可能原因": "未知"}, {"主题": "[提示告警]System发生CPU主频异常", "告警ID": "73016", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.200.80", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.95.113)，对端地址=(other_system=10.200.177.214)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机系统用户密码过期预警", "告警ID": "73017", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.60.205", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.112.153)，对端地址=(other_network=10.200.86.57)", "可能原因": "未知"}, {"主题": "[重要告警]System发生swap分区I_O时延过大", "告警ID": "73018", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.92.150", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.63.65)，对端地址=(other_system=10.200.242.194)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生主机进程数异常", "告警ID": "73019", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.105.63", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.36.155)，对端地址=(other_network=10.200.249.1)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生虚拟网络端口错包率超过告警阈值告警", "告警ID": "73102", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.103.186", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.141.62)，对端地址=(other_network=10.200.43.232)", "可能原因": "未知"}, {"主题": "[次要告警]System发生OVS_EVS卡死", "告警ID": "73104", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.23.120", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.26.125)，对端地址=(other_system=10.200.246.47)", "可能原因": "未知"}, {"主题": "[重要告警]Compute发生虚拟机发生反复重启故障", "告警ID": "73107", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.42.82", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.10.187)，对端地址=(other_compute=10.200.111.105)", "可能原因": "未知"}, {"主题": "[重要告警]Compute发生虚拟机Watchdog异常告警", "告警ID": "73108", "告警级别": "重要", "告警源": "Compute", "来源系统": "ServiceOM10.200.181.126", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.205.105)，对端地址=(other_compute=10.200.139.104)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机CPU占用率超过阈值", "告警ID": "6018", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.161.180", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.205.169)，对端地址=(other_network=10.200.209.47)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生UVP关键进程内存占用率超过阈值告警", "告警ID": "73109", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.20.14", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.250.230)，对端地址=(other_process=10.200.144.242)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生虚拟网络关键资源不足", "告警ID": "73110", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.201.41", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.30.98)，对端地址=(other_network=10.200.38.142)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机连接跟踪表超过阈值告警", "告警ID": "73111", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.213.7", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.31.75)，对端地址=(other_network=***********3)", "可能原因": "未知"}, {"主题": "[严重告警]Compute发生虚拟机网卡异常", "告警ID": "73112", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.152.163", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.13.50)，对端地址=(other_compute=10.200.225.34)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生HAProxy代理服务不可用", "告警ID": "73201", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.64.49", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.52.140)，对端地址=(other_process=10.200.53.49)", "可能原因": "未知"}, {"主题": "[严重告警]System发生组件故障", "告警ID": "73203", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.243.222", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.72.255)，对端地址=(other_system=10.200.178.129)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生主机版本不一致告警", "告警ID": "73204", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.63.10", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.200.51)，对端地址=(other_network=10.200.125.114)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生Iaas层资源配置不一致告警", "告警ID": "73205", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.79.114", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.35.146)，对端地址=(other_monitor=10.200.105.59)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机名重复", "告警ID": "73207", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.60.122", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.228.242)，对端地址=(other_network=10.200.197.133)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生对接外部仲裁服务异常", "告警ID": "73208", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.35.170", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.71.156)，对端地址=(other_process=10.200.36.181)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机内存占用率超过阈值", "告警ID": "6019", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.31.232", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.172.175)，对端地址=(other_network=10.200.126.141)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生zookeeper健康检查告警", "告警ID": "73209", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.212.63", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.154.174)，对端地址=(other_monitor=10.200.83.240)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生未配置管理数据备份至第三方服务器", "告警ID": "73210", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.21.218", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.91.82)，对端地址=(other_process=10.200.225.57)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生vCenter连接失败", "告警ID": "73301", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.213.139", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.69.89)，对端地址=(other_network=10.200.168.239)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生存在网络未配置VLAN探测平面", "告警ID": "73302", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.16.252", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.168.218)，对端地址=(other_network=10.200.164.149)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生VRM服务器连接失败", "告警ID": "73303", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.203.107", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.166.83)，对端地址=(other_network=10.200.57.67)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生rabbitmq服务故障", "告警ID": "73401", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.217.218", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.255.96)，对端地址=(other_process=10.200.195.116)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生gaussdb主备数据不同步", "告警ID": "73403", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.200.48", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.191.104)，对端地址=(other_database=10.200.7.138)", "可能原因": "未知"}, {"主题": "[次要告警]Database发生检测到gaussdb恶意访问", "告警ID": "73404", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.161.42", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.36.54)，对端地址=(other_database=10.200.239.134)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生gaussdb连接数超过阈值", "告警ID": "73405", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.212.109", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.11.255)，对端地址=(other_database=10.200.204.126)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生UVP关键进程CPU占用率超过阈值", "告警ID": "73410", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.16.112", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.140.107)，对端地址=(other_process=10.200.120.66)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机逻辑磁盘占用率超过阈值", "告警ID": "6020", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.86.208", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.157.113)，对端地址=(other_network=10.200.185.133)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生数据库文件损坏", "告警ID": "73411", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.79.10", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.54.68)，对端地址=(other_database=10.200.194.108)", "可能原因": "未知"}, {"主题": "[次要告警]Database发生检测到gaussdb存在长事务", "告警ID": "73412", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.141.178", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.87.126)，对端地址=(other_database=10.200.226.255)", "可能原因": "未知"}, {"主题": "[提示告警]Storage发生后端存储虚拟容量使用率超过阈值", "告警ID": "1060047", "告警级别": "提示", "告警源": "Storage", "来源系统": "ServiceOM10.200.190.132", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.30.34)，对端地址=(other_storage=10.200.20.175)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生挂载双活卷单边故障告警", "告警ID": "1060049", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.205.142", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.138.91)，对端地址=(other_monitor=10.200.84.65)", "可能原因": "未知"}, {"主题": "[提示告警]Storage发生后端存储证书异常", "告警ID": "1060050", "告警级别": "提示", "告警源": "Storage", "来源系统": "ServiceOM10.200.161.25", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.99.29)，对端地址=(other_storage=10.200.121.109)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生主机组内本地直通盘使用率超过阈值", "告警ID": "1101315", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.68.50", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.192.49)，对端地址=(other_network=10.200.53.105)", "可能原因": "未知"}, {"主题": "[次要告警]System发生NPU使用率超过阈值", "告警ID": "1101320", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.246.78", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.128.239)，对端地址=(other_system=10.200.74.122)", "可能原因": "未知"}, {"主题": "[严重告警]Storage发生虚拟机存储链路未完全恢复", "告警ID": "1101321", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.149.79", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.37.216)，对端地址=(other_storage=10.200.89.12)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生裸金属服务器电源状态未获取到", "告警ID": "1126000", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.190.157", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.123.229)，对端地址=(other_process=10.200.119.222)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生裸金属服务器管理状态不可用", "告警ID": "1126001", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.67.148", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.88.130)，对端地址=(other_process=10.200.88.222)", "可能原因": "未知"}, {"主题": "[提示信息]Service OM告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.106.4", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Network发生Service OM与SNMP管理站连接异常", "告警ID": "9002", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.204.45", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.118.145)，对端地址=(other_network=10.200.249.16)", "可能原因": "未知"}, {"主题": "[严重告警]Storage发生系统磁盘使用率过大", "告警ID": "9215", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.83.103", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.194.247)，对端地址=(other_storage=10.200.95.12)", "可能原因": "未知"}, {"主题": "[重要告警]System发生软件订阅与保障年费即将到期", "告警ID": "9216", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.52.104", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.205.226)，对端地址=(other_system=10.200.60.125)", "可能原因": "未知"}, {"主题": "[次要告警]System发生软件订阅与保障年费已经过期", "告警ID": "9217", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.14.152", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.17.72)，对端地址=(other_system=10.200.210.157)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Service OM主备倒换功能被禁用", "告警ID": "9226", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.205.116", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.30.48)，对端地址=(other_process=10.200.86.186)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Service OM资源异常", "告警ID": "9801", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.50.244", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.92.4)，对端地址=(other_process=10.200.135.13)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生Service OM与内部部件连接异常", "告警ID": "9803", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.238.119", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.65.19)，对端地址=(other_network=10.200.93.72)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Service OM双机倒换", "告警ID": "9901", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.198.41", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.178.228)，对端地址=(other_process=10.200.150.48)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生Service OM双机心跳中断", "告警ID": "9902", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.193.1", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.70.141)，对端地址=(other_process=10.200.36.137)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生Service OM双机文件同步失败", "告警ID": "9903", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.216.42", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.102.245)，对端地址=(other_process=10.200.25.11)", "可能原因": "未知"}, {"主题": "[重要告警]System发生与License Server通信异常", "告警ID": "9911", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.43.200", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.51.39)，对端地址=(other_system=10.200.244.87)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Service OM与上级时间服务器同步时间失败", "告警ID": "9201", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.203.160", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.117.238)，对端地址=(other_process=10.200.218.171)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生未配置OpenStack告警上报", "告警ID": "9912", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.247.45", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.244.39)，对端地址=(other_monitor=10.200.40.218)", "可能原因": "未知"}, {"主题": "[严重告警]System发生系统默认证书使用提醒", "告警ID": "9913", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.129.202", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.84.54)，对端地址=(other_system=10.200.122.172)", "可能原因": "未知"}, {"主题": "[提示告警]System发生证书过期预警", "告警ID": "9915", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.250.49", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.11.22)，对端地址=(other_system=10.200.1.127)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生OMM-Server服务异常", "告警ID": "9916", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.199.9", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.166.47)，对端地址=(other_process=10.200.92.216)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Service OM虚拟机CPU占用率超过阈值", "告警ID": "9917", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.88.17", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.161.255)，对端地址=(other_process=10.200.25.78)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生Service OM虚拟机内存占用率超过阈值", "告警ID": "9918", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.238.223", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.247.149)，对端地址=(other_process=10.200.132.214)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生Service OM服务器时间被修改", "告警ID": "9203", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.137.165", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.165.172)，对端地址=(other_process=10.200.76.185)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生Service OM与上级时间服务器时间差异过大", "告警ID": "9204", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.17.1", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.173.69)，对端地址=(other_process=10.200.43.118)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生Service OM数据备份失败", "告警ID": "9206", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.10.248", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.36.240)，对端地址=(other_process=10.200.3.51)", "可能原因": "未知"}, {"主题": "[提示告警]System发生License即将过期", "告警ID": "9207", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.142.201", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.197.83)，对端地址=(other_system=10.200.105.48)", "可能原因": "未知"}, {"主题": "[次要告警]System发生License已经过期", "告警ID": "9208", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.40.148", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.142.168)，对端地址=(other_system=10.200.140.25)", "可能原因": "未知"}, {"主题": "[提示告警]System发生当前资源数量大于License许可上限", "告警ID": "9209", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.15.43", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.9.48)，对端地址=(other_system=10.200.70.31)", "可能原因": "未知"}, {"主题": "[严重告警]System发生当前License已失效", "告警ID": "9210", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.126.71", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.203.200)，对端地址=(other_system=10.200.219.240)", "可能原因": "未知"}, {"主题": "[提示信息]云管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.200.29", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]性能监控", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.176.218", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】网络设备的设备平均CPU利用率阈值", "告警ID": "0001000100010001", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.43.193", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.78.84)，对端地址=(other_network=10.200.211.68)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口流入带宽利用率阈值", "告警ID": "0001000200010003", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.241.52", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.96.168)，对端地址=(other_monitor=10.200.83.246)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】接口流出带宽利用率阈值", "告警ID": "0001000200010004", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.236.152", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.225.77)，对端地址=(other_monitor=10.200.82.145)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】接口接收包丢弃率阈值", "告警ID": "0001000200020001", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.18.253", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.67.160)，对端地址=(other_monitor=10.200.43.217)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】接口发送包丢弃率阈值", "告警ID": "0001000200020002", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.209.44", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.218.43)，对端地址=(other_monitor=10.200.24.65)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】接口丢弃发送包数阈值", "告警ID": "0001000200020003", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.206.112", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.192.166)，对端地址=(other_monitor=10.200.6.71)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口丢弃接收包数阈值", "告警ID": "0001000200020004", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.229.10", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.124.230)，对端地址=(other_monitor=10.200.145.26)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口发送包错误率阈值", "告警ID": "0001000200020005", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.147.102", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.148.245)，对端地址=(other_monitor=10.200.69.82)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】接口接收包错误率阈值", "告警ID": "0001000200020006", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.246.208", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.166.113)，对端地址=(other_monitor=10.200.122.181)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口发送包数阈值", "告警ID": "0001000200020007", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.125.237", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.233.45)，对端地址=(other_monitor=10.200.99.36)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】接口接收包数阈值", "告警ID": "0001000200020008", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.69.53", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.130.192)，对端地址=(other_monitor=10.200.95.10)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】网络设备的设备平均内存利用率阈值", "告警ID": "0001000100010002", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.42.108", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.208.217)，对端地址=(other_network=10.200.56.236)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】接口接收包速率阈值", "告警ID": "0001000200020009", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.174.104", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.255.243)，对端地址=(other_monitor=10.200.58.240)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】接口发送包速率阈值", "告警ID": "000100020002000A", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.54.111", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.85.57)，对端地址=(other_monitor=10.200.167.107)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】接口发送错误包数阈值", "告警ID": "000100020002000B", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.236.241", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.240.99)，对端地址=(other_monitor=10.200.187.46)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】接口接收错误包数阈值", "告警ID": "000100020002000C", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.116.127", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.38.5)，对端地址=(other_monitor=10.200.223.247)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】接口发送字节数阈值", "告警ID": "000100020002000D", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.12.215", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.170.106)，对端地址=(other_monitor=10.200.169.144)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口接收字节数阈值", "告警ID": "000100020002000E", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.190.199", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.65.214)，对端地址=(other_monitor=10.200.250.30)", "可能原因": "未知"}, {"主题": "[重要告警]ELB发生【自定义监控】弹性负载均衡的并发连接数阈值", "告警ID": "0001000300010001", "告警级别": "重要", "告警源": "ELB", "来源系统": "ServiceOM10.200.87.85", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.35.148)，对端地址=(other_elb=10.200.130.58)", "可能原因": "未知"}, {"主题": "[提示告警]ELB发生【自定义监控】弹性负载均衡的网络流入流速阈值", "告警ID": "0001000300020003", "告警级别": "提示", "告警源": "ELB", "来源系统": "ServiceOM10.200.23.151", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.29.208)，对端地址=(other_elb=10.200.45.206)", "可能原因": "未知"}, {"主题": "[次要告警]ELB发生【自定义监控】弹性负载均衡的网络流出流速阈值", "告警ID": "0001000300020004", "告警级别": "次要", "告警源": "ELB", "来源系统": "ServiceOM10.200.223.49", "定位信息": "区域=SH_CSVW，云服务=ELB，节点类型=mgt", "附加信息": "云服务=ELB，服务=mgt，本端地址=(10.200.70.160)，对端地址=(other_elb=10.200.67.42)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】单板CPU利用率阈值", "告警ID": "0001000400010001", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.175.221", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.36.185)，对端地址=(other_monitor=10.200.241.177)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生【自定义监控】网络设备的响应时间阈值", "告警ID": "0001000100010003", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.240.197", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.168.241)，对端地址=(other_network=10.200.138.85)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】单板内存利用率阈值", "告警ID": "0001000400010002", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.36.33", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.165.38)，对端地址=(other_monitor=10.200.160.44)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】光口的光模块接收功率阈值", "告警ID": "0001000500010001", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.36.155", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.122.62)，对端地址=(other_monitor=10.200.144.48)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】光口的光模块发送功率阈值", "告警ID": "0001000500010002", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.128.111", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.93.30)，对端地址=(other_monitor=10.200.132.80)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】电源的电源功率阈值", "告警ID": "0003000200010001", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.193.61", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.93.10)，对端地址=(other_monitor=10.200.49.6)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】宿主机的CPU使用率阈值", "告警ID": "0005000100010001", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.216.138", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.188.201)，对端地址=(other_network=10.200.103.149)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】宿主机的内存使用率阈值", "告警ID": "0005000100020008", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.182.134", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.85.216)，对端地址=(other_network=10.200.78.8)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生【自定义监控】宿主机的网络流入速率阈", "告警ID": "000500010003000B", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.124.97", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.203.189)，对端地址=(other_network=10.200.129.66)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生【自定义监控】宿主机的网络流出速率阈", "告警ID": "000500010003000C", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.54.128", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.165.72)，对端地址=(other_network=10.200.244.12)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生【自定义监控】宿主机的磁盘使用率阈值", "告警ID": "0005000100040014", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.58.83", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.217.167)，对端地址=(other_network=10.200.115.137)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生【自定义监控】弹性云服务器的CPU使用率阈值", "告警ID": "0002000200010001", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.69.152", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.177.143)，对端地址=(other_process=10.200.28.241)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】网络设备的当日不可达比率阈值", "告警ID": "0001000100010004", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.108.16", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.114.219)，对端地址=(other_network=10.200.180.51)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生【自定义监控】弹性云服务器的内存使用率阈值", "告警ID": "0002000200020003", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.90.142", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.20.53)，对端地址=(other_process=10.200.199.90)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生【自定义监控】弹性云服务器的云硬盘使用率阈值", "告警ID": "0002000200040004", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.199.138", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.40.102)，对端地址=(other_process=10.200.216.100)", "可能原因": "未知"}, {"主题": "[次要告警]Compute发生【自定义监控】虚拟机的CPU使用率阈值", "告警ID": "0005000200010001", "告警级别": "次要", "告警源": "Compute", "来源系统": "ServiceOM10.200.238.225", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.129.2)，对端地址=(other_compute=10.200.161.251)", "可能原因": "未知"}, {"主题": "[提示告警]Compute发生【自定义监控】虚拟机的内存使用率阈值", "告警ID": "0005000200020003", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.106.210", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.239.16)，对端地址=(other_compute=10.200.11.51)", "可能原因": "未知"}, {"主题": "[提示告警]Compute发生【自定义监控】虚拟机的云硬盘使用率阈值", "告警ID": "0005000200040004", "告警级别": "提示", "告警源": "Compute", "来源系统": "ServiceOM10.200.190.140", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.178.11)，对端地址=(other_compute=10.200.97.238)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】主机组的CPU使用率阈值", "告警ID": "0005000300010001", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.202.77", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.201.245)，对端地址=(other_network=10.200.58.35)", "可能原因": "未知"}, {"主题": "[次要告警]Network发生【自定义监控】主机组的内存使用率阈值", "告警ID": "0005000300020007", "告警级别": "次要", "告警源": "Network", "来源系统": "ServiceOM10.200.177.102", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.224.72)，对端地址=(other_network=10.200.46.11)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】主机组的网络流入速率阈值", "告警ID": "0005000300030010", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.174.205", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.11.15)，对端地址=(other_network=10.200.21.93)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】主机组的网络流出速率阈值", "告警ID": "0005000300030011", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.174.14", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.140.71)，对端地址=(other_network=10.200.111.72)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】端口性能的速率阈值", "告警ID": "0007000100010001", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.251.92", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.74.30)，对端地址=(other_monitor=************5)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生【自定义监控】网络设备的当前会话新建速率阈值", "告警ID": "0001000100020003", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.42.31", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.44.154)，对端地址=(other_network=10.200.143.167)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】端口性能的CRC错误阈值", "告警ID": "0007000100010002", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.8.30", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.10.94)，对端地址=(other_monitor=10.200.209.195)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】端口性能的发送link reset错误阈值", "告警ID": "0007000100010003", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.11.168", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.44.52)，对端地址=(other_monitor=10.200.251.10)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】端口性能的接收link reset错误阈值", "告警ID": "0007000100010004", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.63.207", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.222.89)，对端地址=(other_monitor=10.200.52.68)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】端口性能的link reset错误总数阈值", "告警ID": "0007000100010005", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.148.185", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.248.88)，对端地址=(other_monitor=10.200.248.9)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】端口性能的class 3 discard错误阈值", "告警ID": "0007000100010006", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.248.203", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.47.106)，对端地址=(other_monitor=10.200.241.44)", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生【自定义监控】端口性能的sync loss错误阈值", "告警ID": "0007000100010007", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.84.181", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.238.164)，对端地址=(other_monitor=10.200.104.32)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】端口性能的接收利用率阈值", "告警ID": "0007000100010008", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.100.233", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.192.116)，对端地址=(other_monitor=10.200.236.127)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】端口性能的缓冲信用量阈值", "告警ID": "0007000100010009", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.221.51", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.173.97)，对端地址=(other_monitor=10.200.98.163)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】端口性能的接收速率阈值", "告警ID": "000700010001000A", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.125.201", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.242.221)，对端地址=(other_monitor=10.200.161.171)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】端口性能的发送利用率阈", "告警ID": "000700010001000B", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.70.103", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.240.197)，对端地址=(other_monitor=10.200.245.55)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生【自定义监控】网络设备的当前会话总数阈值", "告警ID": "0001000100020004", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.254.143", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.87.19)，对端地址=(other_network=10.200.3.51)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】端口性能的带宽利用率阈", "告警ID": "000700010001000C", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.210.231", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.75.214)，对端地址=(other_monitor=10.200.211.213)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】端口性能的link f", "告警ID": "000700010001000D", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.92.190", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.79.183)，对端地址=(other_monitor=10.200.212.186)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生【自定义监控】端口性能的signal", "告警ID": "000700010001000E", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.230.131", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.179.196)，对端地址=(other_monitor=10.200.115.55)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】端口性能的总error", "告警ID": "000700010001000F", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.56.23", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.129.253)，对端地址=(other_monitor=10.200.44.49)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】端口性能的发送速率阈值", "告警ID": "0007000100010010", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.238.63", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.97.67)，对端地址=(other_monitor=10.200.32.35)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生【自定义监控】大数据主机组CPU使用率阈值", "告警ID": "0008000100010001", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.98.154", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.143.185)，对端地址=(other_network=10.200.242.91)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生【自定义监控】大数据主机组的内存使用率阈", "告警ID": "0008000100010002", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.147.220", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.154.194)，对端地址=(other_network=10.200.45.44)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生【自定义监控】大数据主机组的磁盘使用率阈值", "告警ID": "0008000100010003", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.50.243", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(*************)，对端地址=(other_network=************)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】弹性IP的上行流量阈值", "告警ID": "0002000500000001", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.97.178", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(************)，对端地址=(other_monitor=************)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】弹性IP的下行流量阈值", "告警ID": "0002000500000003", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.164.125", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(************)，对端地址=(other_monitor=**************)", "可能原因": "未知"}, {"主题": "[重要告警]Network发生【自定义监控】网络设备的网络流量值阈值", "告警ID": "0001000100020005", "告警级别": "重要", "告警源": "Network", "来源系统": "ServiceOM10.200.121.175", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(*************)，对端地址=(other_network=*************)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生【自定义监控】裸金属服务器的用户空间", "告警ID": "0002000A00000001", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.4.203", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.111.5)，对端地址=(other_process=10.200.147.165)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生【自定义监控】裸金属服务器的内存使用", "告警ID": "0002000A0000000D", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.230.137", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.76.139)，对端地址=(other_process=10.200.68.64)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生【自定义监控】裸金属服务器的磁盘使用", "告警ID": "0002000A00000014", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.152.236", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.168.132)，对端地址=(other_process=10.200.94.204)", "可能原因": "未知"}, {"主题": "[提示告警]Database发生【自定义监控】关系数据库的CPU使用率阈值", "告警ID": "0002000700000001", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.137.87", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.235.200)，对端地址=(other_database=10.200.241.154)", "可能原因": "未知"}, {"主题": "[次要告警]Database发生【自定义监控】关系数据库的内存使用率阈值", "告警ID": "0002000700000002", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.49.87", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.227.148)，对端地址=(other_database=10.200.39.3)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生【自定义监控】关系数据库的IOPS阈值", "告警ID": "0002000700000003", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.157.248", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.169.191)，对端地址=(other_database=10.200.72.214)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生【自定义监控】关系数据库的数据库连接", "告警ID": "000200070000002A", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.224.5", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.163.42)，对端地址=(other_database=10.200.50.255)", "可能原因": "未知"}, {"主题": "[提示告警]Database发生【自定义监控】关系数据库的使用中的数据库连接数阈值告警", "告警ID": "0002000700000036", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.175.108", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.131.198)，对端地址=(other_database=10.200.208.31)", "可能原因": "未知"}, {"主题": "[次要告警]Database发生【自定义监控】Oracle CDB关系型数据库的主机内存使用率阈值", "告警ID": "0002000800000003", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.26.181", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.102.16)，对端地址=(other_database=10.200.74.63)", "可能原因": "未知"}, {"主题": "[提示告警]Database发生【自定义监控】Oracle CDB关系型数据库的主机CPU使用率阈值", "告警ID": "0002000800000004", "告警级别": "提示", "告警源": "Database", "来源系统": "ServiceOM10.200.90.19", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.56.201)，对端地址=(other_database=10.200.43.112)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口接收速率阈值", "告警ID": "0001000200010001", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.107.178", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.175.163)，对端地址=(other_monitor=10.200.147.73)", "可能原因": "未知"}, {"主题": "[次要告警]Database发生【自定义监控】Oracle PDB关系型数据库的主机内存使用率阈值", "告警ID": "0002000900000003", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.223.253", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.235.240)，对端地址=(other_database=10.200.44.157)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生【自定义监控】Oracle PDB关系型数据库的主机CPU使用率阈值", "告警ID": "0002000900000004", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.177.148", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.89.119)，对端地址=(other_database=10.200.19.211)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生【自定义监控】关系数据库的CPU使用率超过阈值", "告警ID": "0002000700000048", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.238.10", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.102.77)，对端地址=(other_database=10.200.4.151)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生【自定义监控】关系数据库的内存使用率超过阈值", "告警ID": "0002000700000049", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.68.57", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.62.65)，对端地址=(other_database=10.200.216.50)", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生【自定义监控】电源的电源功率超过阈值", "告警ID": "0003000200010005", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.97.13", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.248.176)，对端地址=(other_monitor=10.200.39.12)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生【自定义监控】分布式缓存服务的每秒并", "告警ID": "000A000200000010", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.15.9", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.117.120)，对端地址=(other_process=10.200.156.109)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生【自定义监控】分布式缓存服务的缓存命", "告警ID": "000A000200000021", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.72.217", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.68.85)，对端地址=(other_process=10.200.74.102)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生【自定义监控】分布式缓存服务的每秒并", "告警ID": "000A00020000002D", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.110.192", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.48.23)，对端地址=(other_process=10.200.94.39)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生【自定义监控】分布式缓存服务的CPU", "告警ID": "000A00020000004D", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.83.236", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.84.57)，对端地址=(other_process=10.200.200.11)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生【自定义监控】接口发送速率阈值", "告警ID": "0001000200010002", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.104.238", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.113.30)，对端地址=(other_monitor=10.200.102.211)", "可能原因": "未知"}, {"主题": "[提示信息]系统维护", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.33.240", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.213.151", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]System发生操作系统帐", "告警ID": "MOMaintenanceService_100100", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.198.102", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.129.228)，对端地址=(other_system=10.200.14.231)", "可能原因": "未知"}, {"主题": "[重要告警]System发生证书即将过", "告警ID": "MOMaintenanceService_100103", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.157.137", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.78.166)，对端地址=(other_system=10.200.9.134)", "可能原因": "未知"}, {"主题": "[重要告警]System发生证书即将过", "告警ID": "MOMaintenanceService_100106", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.35.86", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.15.151)，对端地址=(other_system=10.200.26.1)", "可能原因": "未知"}, {"主题": "[提示信息]服务监控", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.230.196", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.149.174", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]System发生节点", "告警ID": "servicemonitor_agent_heartbeat", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.48.173", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.52.32)，对端地址=(other_system=10.200.5.38)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "servicemonitor_os.disk.rd_rsp_ti", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.101.83", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.130.226)，对端地址=(other_unknown=10.200.182.70)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "servicemonitor_os.disk.wt_rsp_ti", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.210.20", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.239.36)，对端地址=(other_unknown=10.200.27.236)", "可能原因": "未知"}, {"主题": "[严重告警]Unknown发生", "告警ID": "servicemonitor_os.fs.inode_free", "告警级别": "严重", "告警源": "Unknown", "来源系统": "ServiceOM10.200.162.105", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.245.25)，对端地址=(other_unknown=10.200.211.171)", "可能原因": "未知"}, {"主题": "[重要告警]System发生硬盘使", "告警ID": "servicemonitor_os.fs.percent", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.183.182", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.88.153)，对端地址=(other_system=10.200.98.169)", "可能原因": "未知"}, {"主题": "[重要告警]Unknown发生", "告警ID": "servicemonitor_redis.dbcopyStatu", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.210.40", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.203.78)，对端地址=(other_unknown=10.200.245.40)", "可能原因": "未知"}, {"主题": "[次要告警]Unknown发生", "告警ID": "servicemonitor_redis.dbsvrStatus", "告警级别": "次要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.192.50", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.133.63)，对端地址=(other_unknown=10.200.113.52)", "可能原因": "未知"}, {"主题": "[重要告警]Unknown发生", "告警ID": "servicemonitor_redis.connectedCl", "告警级别": "重要", "告警源": "Unknown", "来源系统": "ServiceOM10.200.90.55", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.219.106)，对端地址=(other_unknown=10.200.204.19)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生服务监控节点心跳", "告警ID": "servicemonitor_heartbeat", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.48.104", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.11.118)，对端地址=(other_process=10.200.60.98)", "可能原因": "未知"}, {"主题": "[严重告警]System发生CPU使用率", "告警ID": "servicemonitor_cpu.percent", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.187.133", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.66.98)，对端地址=(other_system=10.200.66.98)", "可能原因": "未知"}, {"主题": "[次要告警]System发生物理内", "告警ID": "servicemonitor_memory.percent", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.80.156", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.208.140)，对端地址=(other_system=10.200.252.78)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "servicemonitor_os.nic.rx_dropped_", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.26.100", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.212.143)，对端地址=(other_unknown=10.200.189.126)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "servicemonitor_os.nic.rx_errors_p", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.16.208", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.124.248)，对端地址=(other_unknown=10.200.34.231)", "可能原因": "未知"}, {"主题": "[提示告警]Unknown发生", "告警ID": "servicemonitor_os.nic.tx_dropped", "告警级别": "提示", "告警源": "Unknown", "来源系统": "ServiceOM10.200.128.241", "定位信息": "区域=SH_CSVW，云服务=Unknown，节点类型=mgt", "附加信息": "云服务=Unknown，服务=mgt，本端地址=(10.200.60.239)，对端地址=(other_unknown=10.200.7.20)", "可能原因": "未知"}, {"主题": "[提示告警]System发生网", "告警ID": "servicemonitor_os.nic.tx_errors", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.191.68", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.144.33)，对端地址=(other_system=10.200.200.11)", "可能原因": "未知"}, {"主题": "[严重告警]System发生硬", "告警ID": "servicemonitor_os.disk.io_waite", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.81.184", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.190.222)，对端地址=(other_system=10.200.173.193)", "可能原因": "未知"}, {"主题": "[提示信息]统一证书", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.188.199", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生系统存在即将过期证书告警", "告警ID": "MOCertMgmt_100101", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.190.203", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.13.166)，对端地址=(other_monitor=10.200.171.84)", "可能原因": "未知"}, {"主题": "[提示信息]统一日志", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.238.144", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]System发生Elasticsearch集群心跳检测异常", "告警ID": "0001000300030001", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.102.30", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.129.7)，对端地址=(other_system=10.200.62.215)", "可能原因": "未知"}, {"主题": "[提示信息]备份恢复", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.59.140", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]System发生备份失败", "告警ID": "MOBackupService_100001", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.245.242", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.35.2)，对端地址=(other_system=10.200.113.56)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生未配置备份服务器", "告警ID": "MOBackupService_100002", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.192.125", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.133.203)，对端地址=(other_process=10.200.75.176)", "可能原因": "未知"}, {"主题": "[提示信息]IES管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.39.218", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]System发生产品数据定时备份失败", "告警ID": "101205", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.250.156", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.67.26)，对端地址=(other_system=10.200.180.128)", "可能原因": "未知"}, {"主题": "[提示信息]运维自动化", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.60.53", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]System发生ManageOne管理资源超过部署规格限制", "告警ID": "10000011", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.3.216", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.225.58)，对端地址=(other_system=10.200.75.110)", "可能原因": "未知"}, {"主题": "[提示信息]华为虚拟化资源池", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.204.52", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]System发生话单文件发送到计量中心文件服", "告警ID": "MOVCDRService_100091", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.145.10", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.6.162)，对端地址=(other_system=10.200.172.226)", "可能原因": "未知"}, {"主题": "[提示信息]部署面业务告警", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.63.91", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]ManageOne管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.180.191", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]System发生节点故障倒换", "告警ID": "101209", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.190.54", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.255.234)，对端地址=(other_system=10.200.167.12)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生NTP服务异常", "告警ID": "51023", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.213.183", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.7.48)，对端地址=(other_process=10.200.22.177)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生数据库实例故障倒换", "告警ID": "101211", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.237.113", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.159.24)，对端地址=(other_database=10.200.131.220)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生连接ZooKeeper失败", "告警ID": "101212", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.163.83", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.42.31)，对端地址=(other_network=10.200.65.36)", "可能原因": "未知"}, {"主题": "[提示信息]系统监控", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.126.187", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生CPU占用率过高告警", "告警ID": "151", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.126.250", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(***********6)，对端地址=(other_monitor=10.200.59.211)", "可能原因": "未知"}, {"主题": "[严重告警]Database发生数据库本地主备复制异常", "告警ID": "101210", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.148.220", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.137.241)，对端地址=(other_database=10.200.91.78)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生网管服务异常退出告警", "告警ID": "152", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.69.182", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.254.27)，对端地址=(other_process=10.200.84.49)", "可能原因": "未知"}, {"主题": "[提示告警]System发生节点状态异常", "告警ID": "101208", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.254.8", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.53.81)，对端地址=(other_system=10.200.234.245)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生内存占用率过高告警", "告警ID": "154", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.79.152", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.108.65)，对端地址=(other_monitor=10.200.128.93)", "可能原因": "未知"}, {"主题": "[严重告警]Storage发生磁盘占用率过高告警", "告警ID": "36", "告警级别": "严重", "告警源": "Storage", "来源系统": "ServiceOM10.200.36.197", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.129.245)，对端地址=(other_storage=10.200.212.44)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生数据库进程异常", "告警ID": "38", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.154.83", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.55.113)，对端地址=(other_database=10.200.175.40)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生服务内存占用过高告警", "告警ID": "47", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.248.53", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.117.119)，对端地址=(other_process=10.200.21.1)", "可能原因": "未知"}, {"主题": "[次要告警]System发生SSH管理通道故障", "告警ID": "101206", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.59.30", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.160.52)，对端地址=(other_system=10.200.163.65)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生部署面服务进程资源占用异常", "告警ID": "53080", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.179.97", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.136.128)，对端地址=(other_process=10.200.99.54)", "可能原因": "未知"}, {"主题": "[提示信息]安全管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.16.84", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]System发生证书即将过期", "告警ID": "51020", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.209.243", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.186.67)，对端地址=(other_system=10.200.204.20)", "可能原因": "未知"}, {"主题": "[重要告警]System发生证书已经过期", "告警ID": "51021", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.27.47", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.34.144)，对端地址=(other_system=10.200.140.215)", "可能原因": "未知"}, {"主题": "[提示告警]System发生证书更新失败", "告警ID": "51022", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.16.250", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(**************)，对端地址=(other_system=*************)", "可能原因": "未知"}, {"主题": "[提示信息]参考信息", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.251.172", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]如何查找节点对应的IP地址", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.121.155", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]IAM 告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.34.162", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Database发生IAM数据库资源使用异常或服务异常", "告警ID": "36064531474581504", "告警级别": "严重", "告警源": "Database", "来源系统": "ServiceOM10.200.25.135", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.34.32)，对端地址=(other_database=10.200.247.190)", "可能原因": "未知"}, {"主题": "[提示告警]System发生IAM鉴权失败", "告警ID": "0004000700010009", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.20.138", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.61.81)，对端地址=(other_system=10.200.60.126)", "可能原因": "未知"}, {"主题": "[提示信息]驱动管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.237.241", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Monitor发生系统连通性检测失败告警", "告警ID": "100502", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ServiceOM10.200.88.189", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.14.228)，对端地址=(other_monitor=10.200.147.12)", "可能原因": "未知"}, {"主题": "[次要告警]Monitor发生SNMP连通性检测失败告警", "告警ID": "100553", "告警级别": "次要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.46.190", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.199.65)，对端地址=(other_monitor=10.200.221.243)", "可能原因": "未知"}, {"主题": "[提示信息]驱动框架", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.212.90", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生LVS配置失败告警", "告警ID": "100550", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.1.117", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.13.205)，对端地址=(other_monitor=10.200.6.55)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生LVS中断服务告警", "告警ID": "100551", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.168.10", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.21.250)，对端地址=(other_process=10.200.152.96)", "可能原因": "未知"}, {"主题": "[提示信息]容量管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.70.45", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]System发生vCPU分配率超", "告警ID": "CloudCapacityMgmt_Base_1001", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.26.74", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.171.7)，对端地址=(other_system=10.200.233.117)", "可能原因": "未知"}, {"主题": "[严重告警]Compute发生vMemory分", "告警ID": "CloudCapacityMgmt_Base_1002", "告警级别": "严重", "告警源": "Compute", "来源系统": "ServiceOM10.200.245.54", "定位信息": "区域=SH_CSVW，云服务=Compute，节点类型=mgt", "附加信息": "云服务=Compute，服务=mgt，本端地址=(10.200.87.199)，对端地址=(other_compute=10.200.18.72)", "可能原因": "未知"}, {"主题": "[次要告警]Storage发生存储使用率超过阈", "告警ID": "CloudCapacityMgmt_Base_1003", "告警级别": "次要", "告警源": "Storage", "来源系统": "ServiceOM10.200.147.103", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(10.200.27.219)，对端地址=(other_storage=10.200.233.183)", "可能原因": "未知"}, {"主题": "[重要告警]Storage发生存储分配率超过阈", "告警ID": "CloudCapacityMgmt_Base_1004", "告警级别": "重要", "告警源": "Storage", "来源系统": "ServiceOM10.200.28.167", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(**************)，对端地址=(other_storage=************)", "可能原因": "未知"}, {"主题": "[次要告警]System发生弹性IP使用率超", "告警ID": "CloudCapacityMgmt_Base_1005", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.230.50", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(************)，对端地址=(other_system=**************)", "可能原因": "未知"}, {"主题": "[提示告警]Storage发生数据存储使用率超", "告警ID": "CloudCapacityMgmt_Base_1006", "告警级别": "提示", "告警源": "Storage", "来源系统": "ServiceOM10.200.146.133", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=mgt", "附加信息": "云服务=Storage，服务=mgt，本端地址=(**************)，对端地址=(other_storage=**************)", "可能原因": "未知"}, {"主题": "[提示信息]告警管理", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.22.118", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Monitor发生当前告警数量达到阈值", "告警ID": "157", "告警级别": "严重", "告警源": "Monitor", "来源系统": "ServiceOM10.200.140.117", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.10.160)，对端地址=(other_monitor=10.200.99.5)", "可能原因": "未知"}, {"主题": "[重要告警]Monitor发生同类告警数量超出门限", "告警ID": "832", "告警级别": "重要", "告警源": "Monitor", "来源系统": "ServiceOM10.200.232.43", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=mgt", "附加信息": "云服务=Monitor，服务=mgt，本端地址=(10.200.169.38)，对端地址=(other_monitor=10.200.129.48)", "可能原因": "未知"}, {"主题": "[提示信息]安全管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.186.148", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[严重告警]Network发生远端认证主服务器连接失败告警", "告警ID": "128", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.142.119", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.125.227)，对端地址=(other_network=10.200.152.10)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生远端认证备服务器连接失败告警", "告警ID": "160", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.186.172", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.66.104)，对端地址=(other_network=10.200.215.121)", "可能原因": "未知"}, {"主题": "[重要告警]System发生用户密码即将过期", "告警ID": "30004", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.134.225", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.175.142)，对端地址=(other_system=10.200.231.49)", "可能原因": "未知"}, {"主题": "[重要告警]System发生用户密码已过期", "告警ID": "30005", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.195.180", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.141.100)，对端地址=(other_system=10.200.106.5)", "可能原因": "未知"}, {"主题": "[提示告警]System发生登录尝试次数达到最大值", "告警ID": "505001106", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.27.125", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.124.247)，对端地址=(other_system=10.200.36.103)", "可能原因": "未知"}, {"主题": "[提示信息]日志转发", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.121.180", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生切换到备系统日志服务器告警", "告警ID": "126", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.101.183", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.234.147)，对端地址=(other_process=10.200.192.67)", "可能原因": "未知"}, {"主题": "[提示告警]Network发生连接主、备系统日志服务器均失败告警", "告警ID": "127", "告警级别": "提示", "告警源": "Network", "来源系统": "ServiceOM10.200.42.8", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.241.248)，对端地址=(other_network=10.200.229.56)", "可能原因": "未知"}, {"主题": "[提示信息]License管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.1.188", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]System发生License即将过期", "告警ID": "999999992", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.170.29", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.206.145)，对端地址=(other_system=10.200.179.254)", "可能原因": "未知"}, {"主题": "[重要告警]System发生License控制项或销售项进入宽限期", "告警ID": "999999999", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.188.126", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.37.111)，对端地址=(other_system=10.200.26.35)", "可能原因": "未知"}, {"主题": "[严重告警]System发生License已经过期", "告警ID": "999999993", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.127.170", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.126.207)，对端地址=(other_system=10.200.238.157)", "可能原因": "未知"}, {"主题": "[次要告警]System发生License不合法", "告警ID": "999999995", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.246.123", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.251.103)，对端地址=(other_system=10.200.208.194)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生软件服务年费即将过期", "告警ID": "999999989", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.156.72", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.18.123)，对端地址=(other_process=10.200.13.98)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生软件服务年费已经过期", "告警ID": "999999990", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.142.24", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.255.251)，对端地址=(other_process=10.200.214.57)", "可能原因": "未知"}, {"主题": "[严重告警]System发生License资源达到或超过阈值", "告警ID": "999999994", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.242.218", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.169.91)，对端地址=(other_system=10.200.135.7)", "可能原因": "未知"}, {"主题": "[次要告警]System发生License销售项达到或超过阈值", "告警ID": "999999996", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.168.242", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.242.239)，对端地址=(other_system=10.200.81.225)", "可能原因": "未知"}, {"主题": "[次要告警]System发生License资源达到或超过100%阈值", "告警ID": "999999997", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.130.31", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.225.163)，对端地址=(other_system=10.200.121.96)", "可能原因": "未知"}, {"主题": "[严重告警]System发生License销售项达到或超过100%阈值", "告警ID": "999999998", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.81.75", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.54.26)，对端地址=(other_system=10.200.208.2)", "可能原因": "未知"}, {"主题": "[提示信息]远程通知管理", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.225.126", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]System发生远程通知发送失败", "告警ID": "505001111", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.90.71", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.124.194)，对端地址=(other_system=10.200.32.252)", "可能原因": "未知"}, {"主题": "[提示信息]组合API", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.233.126", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]组合API", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.109.2", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生组合API节点NTP进程状态异常", "告警ID": "1150001", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.181.38", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.147.197)，对端地址=(other_process=10.200.208.200)", "可能原因": "未知"}, {"主题": "[严重告警]System发生组合API节点CPU使用率过高", "告警ID": "1150002", "告警级别": "严重", "告警源": "System", "来源系统": "ServiceOM10.200.163.215", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.109.123)，对端地址=(other_system=10.200.201.211)", "可能原因": "未知"}, {"主题": "[重要告警]System发生组合API节点内存使用率过高", "告警ID": "1150003", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.247.164", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.84.128)，对端地址=(other_system=10.200.173.208)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生组合API节点tomcat进程异常", "告警ID": "1150004", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.38.98", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.242.41)，对端地址=(other_process=10.200.229.115)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生组合API节点tomcat进程不存在", "告警ID": "1150017", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.241.90", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.238.28)，对端地址=(other_process=10.200.184.180)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生组合API节点tomcat存在多进程", "告警ID": "1150018", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.182.71", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.223.162)，对端地址=(other_process=10.200.7.59)", "可能原因": "未知"}, {"主题": "[提示信息]云硬盘", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.203.6", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]System发生evs周期性检测cinder连通性失败", "告警ID": "1060036", "告警级别": "次要", "告警源": "System", "来源系统": "SystemOM10.200.24.183", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=compute", "附加信息": "云服务=System，服务=compute，节点地址=(10.200.163.182)，错误代码=1060036", "可能原因": "未知"}, {"主题": "[提示信息]修改配置文件中对接其他组件或服务的帐户密码", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.211.83", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]典型Cinder问题定位指导", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.76.124", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]排查FusionSphere OpenStack", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.238.165", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查询对应卷信息", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.140.206", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查看cinder-api日志", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.114.159", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查看cinder-scheduler日志", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.183.135", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查询cinder-volume日志", "告警ID": "", "告警级别": "提示", "告警源": "Storage", "来源系统": "ManageOne10.200.71.130", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=management", "附加信息": "云服务=Storage，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]排查被级联层OpenStack", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.204.139", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查看被级联层cinder-api日志", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.40.75", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查询被级联层cinder-scheduler日志", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.28.54", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]查询被级联层cinder-volume日志", "告警ID": "", "告警级别": "提示", "告警源": "Storage", "来源系统": "ManageOne10.200.71.23", "定位信息": "区域=SH_CSVW，云服务=Storage，节点类型=management", "附加信息": "云服务=Storage，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]镜像服务", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.132.58", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]告警参考", "告警ID": "", "告警级别": "提示", "告警源": "Monitor", "来源系统": "ManageOne10.200.84.216", "定位信息": "区域=SH_CSVW，云服务=Monitor，节点类型=management", "附加信息": "云服务=Monitor，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Process发生ntp进程不存在", "告警ID": "1131007", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.68.122", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.146.153)，对端地址=(other_process=10.200.130.32)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生tomcat多进程", "告警ID": "1131009", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.30.124", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.12.90)，对端地址=(other_process=10.200.121.6)", "可能原因": "未知"}, {"主题": "[次要告警]Process发生tomcat进程不存在", "告警ID": "1131011", "告警级别": "次要", "告警源": "Process", "来源系统": "ServiceOM10.200.79.147", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.40.174)，对端地址=(other_process=10.200.3.50)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生tomcat进程down掉", "告警ID": "1131012", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.241.184", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.110.79)，对端地址=(other_process=10.200.146.11)", "可能原因": "未知"}, {"主题": "[提示信息]弹性云服务器", "告警ID": "", "告警级别": "提示", "告警源": "Process", "来源系统": "ManageOne10.200.196.46", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=management", "附加信息": "云服务=Process，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[次要告警]Database发生连接ECS数据库失败", "告警ID": "1101308", "告警级别": "次要", "告警源": "Database", "来源系统": "ServiceOM10.200.32.194", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(10.200.167.242)，对端地址=(other_database=10.200.237.213)", "可能原因": "未知"}, {"主题": "[严重告警]Network发生ECS连接FSP失败", "告警ID": "1101312", "告警级别": "严重", "告警源": "Network", "来源系统": "ServiceOM10.200.36.213", "定位信息": "区域=SH_CSVW，云服务=Network，节点类型=mgt", "附加信息": "云服务=Network，服务=mgt，本端地址=(10.200.40.226)，对端地址=(other_network=10.200.211.20)", "可能原因": "未知"}, {"主题": "[提示信息]登录FusionSphere OpenStack后台", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.237.9", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]导入环境变量", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.36.235", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]OBS Console", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.112.174", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Process发生OBS Console 的tomcat进程异常", "告警ID": "600000007", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.140.78", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.9.153)，对端地址=(other_process=10.200.86.226)", "可能原因": "未知"}, {"主题": "[提示告警]System发生OBS Console 的tomcat端口未监听", "告警ID": "600000008", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.148.27", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.40.132)，对端地址=(other_system=10.200.43.252)", "可能原因": "未知"}, {"主题": "[提示告警]System发生OBS Console tomcat证书异常", "告警ID": "600000100", "告警级别": "提示", "告警源": "System", "来源系统": "ServiceOM10.200.52.179", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.79.250)，对端地址=(other_system=10.200.182.9)", "可能原因": "未知"}, {"主题": "[提示信息]OBS LVS", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.21.13", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]System发生OBS LVS节点存在未连通网口", "告警ID": "600000201", "告警级别": "重要", "告警源": "System", "来源系统": "ServiceOM10.200.184.37", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.110.80)，对端地址=(other_system=10.200.5.133)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生keepalived进程未启动", "告警ID": "600000200", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.222.8", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.58.153)，对端地址=(other_process=10.200.85.208)", "可能原因": "未知"}, {"主题": "[提示信息]虚拟私有云", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.136.201", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示告警]Process发生ntp进程故障", "告警ID": "1200025", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.19.8", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(*************)，对端地址=(other_process=*************)", "可能原因": "未知"}, {"主题": "[次要告警]System发生公网IP地址不足", "告警ID": "1200054", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.52.131", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(*************)，对端地址=(other_system=**************)", "可能原因": "未知"}, {"主题": "[重要告警]Database发生VPC数据库访问失败", "告警ID": "1200074", "告警级别": "重要", "告警源": "Database", "来源系统": "ServiceOM10.200.253.101", "定位信息": "区域=SH_CSVW，云服务=Database，节点类型=mgt", "附加信息": "云服务=Database，服务=mgt，本端地址=(**************)，对端地址=(other_database=************)", "可能原因": "未知"}, {"主题": "[提示信息]附录", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.26.57", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[提示信息]导入环境变量", "告警ID": "", "告警级别": "提示", "告警源": "System", "来源系统": "ManageOne10.200.47.215", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=management", "附加信息": "云服务=System，服务=management，操作类型=配置", "可能原因": "未知"}, {"主题": "[重要告警]Process发生tomcat进程CPU占用率超过阈值", "告警ID": "1200027", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.107.58", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.22.219)，对端地址=(other_process=10.200.181.70)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生tomcat进程内存占用率超过阈值", "告警ID": "1200028", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.53.80", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.10.77)，对端地址=(other_process=10.200.172.83)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生tomcat进程无响应", "告警ID": "1200030", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.167.135", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.64.216)，对端地址=(other_process=10.200.142.110)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生zookeeper进程CPU占用率超过阈值", "告警ID": "1200032", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.79.134", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.227.140)，对端地址=(other_process=10.200.112.212)", "可能原因": "未知"}, {"主题": "[重要告警]Process发生zookeeper进程内存占用率超过阈值", "告警ID": "1200033", "告警级别": "重要", "告警源": "Process", "来源系统": "ServiceOM10.200.121.74", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.250.206)，对端地址=(other_process=10.200.105.138)", "可能原因": "未知"}, {"主题": "[严重告警]Process发生zookeeper进程重复运行", "告警ID": "1200034", "告警级别": "严重", "告警源": "Process", "来源系统": "ServiceOM10.200.157.44", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.91.174)，对端地址=(other_process=10.200.18.138)", "可能原因": "未知"}, {"主题": "[提示告警]Process发生zookeeper进程无响应", "告警ID": "1200035", "告警级别": "提示", "告警源": "Process", "来源系统": "ServiceOM10.200.242.78", "定位信息": "区域=SH_CSVW，云服务=Process，节点类型=mgt", "附加信息": "云服务=Process，服务=mgt，本端地址=(10.200.9.252)，对端地址=(other_process=10.200.231.27)", "可能原因": "未知"}, {"主题": "[次要告警]System发生Neutron接口调用失败", "告警ID": "1200053", "告警级别": "次要", "告警源": "System", "来源系统": "ServiceOM10.200.205.13", "定位信息": "区域=SH_CSVW，云服务=System，节点类型=mgt", "附加信息": "云服务=System，服务=mgt，本端地址=(10.200.62.104)，对端地址=(other_system=10.200.153.238)", "可能原因": "未知"}]