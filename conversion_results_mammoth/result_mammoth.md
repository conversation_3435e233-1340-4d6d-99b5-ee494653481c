# 5\.2\.3\.1\.1 ALM\-6008 上传日志到OBS服务失败

#### 告警解释

OpenStack周期（默认为300秒）检查上一次上传日志至OBS服务是否成功，当检查到上传失败时，产生此告警。

#### 告警属性

__告警ID__

__告警级别__

__可自动清除__

6008

重要

是

#### 告警参数

__参数名称__

__参数含义__

定位信息

主机ID：产生告警的主机ID。

附加信息

- 主机ID：产生告警的主机ID。
- 主机名：产生告警的主机名。

#### 对系统的影响

日志无法上传至OBS服务，如果未设置本地备份，则会导致该次日志丢失。

#### 可能原因

- 主机与OBS服务网络连接异常。
- Apacheproxy服务状态异常。
- 日志配置文件中OBS服务IP、端口未正确设置。
- OBS服务上指定的日志上传空间已满。

#### 处理步骤

1. 登录FusionSphere OpenStack安装部署界面。

具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。

1. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
2. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。

默认帐号：fsp，默认密码：\*\*\*\*\*。

系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。

1. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

__su \- root__

默认密码：“\*\*\*\*\*”。

1. 执行以下命令，防止系统超时退出。

__TMOUT=0__

1. 执行以下命令，导入环境变量。

__source set\_env__

回显如下类似信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  \(4\) openstack environment variable of cloud\_admin \(keystone v3\) 

  please choose:\[1|2|3|4\] 

1. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS\_USERNAME对应帐号的密码。

默认密码为：\*\*\*\*\*。

__Apacheproxy服务状态异常__

1. 执行如下操作查看apacheproxy服务是否正常。
	1. 执行如下命令，采用安全方式操作。

<a id="ALM-6008__li10336113792911"></a><a id="li10336113792911"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	1. 输入“1”，选择使用keystone v3鉴权。

<a id="ALM-6008__li1533673712917"></a><a id="li1533673712917"></a>显示如下信息：

Input command:

- 
	1. 执行如下命令，查看apacheproxy服务是否正常。

<a id="ALM-6008__li1333673702910"></a><a id="li1333673702910"></a>__cps template\-instance\-list \-\-service apacheproxy apacheproxy__

在apacheproxy主备部署的情况下，回显如下类似信息：

![图片加载失败]()

在apacheproxy单实例部署的情况下，回显如下类似信息：

![图片加载失败]()

- 
	1. 如服务状态显示为fault，执行9。
	2. 如未显示为fault，执行12。

1. <a id="ALM-6008__li7338133714291"></a><a id="li7338133714291"></a>执行如下操作停止apacheproxy服务。
	1. 执行如下命令，采用安全方式操作。

<a id="li14337113702914"></a><a id="ALM-6008__li14337113702914"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	1. 输入“1”，选择使用keystone v3鉴权。

<a id="ALM-6008__li1533753712296"></a><a id="li1533753712296"></a>显示如下信息：

Input command:

- 
	1. 运行如下命令停止apacheproxy服务。

<a id="li1733718372298"></a><a id="ALM-6008__li1733718372298"></a>__cps host\-template\-instance\-operate \-\-service apacheproxy apacheproxy \-\-action stop__

回显如下类似信息：

![图片加载失败]()

查看操作结果是否为success。

- 
	1. 是，执行10。
	2. 否，执行17。

1. <a id="ALM-6008__li12340237192915"></a><a id="li12340237192915"></a>执行如下操作启动apacheproxy服务。
	1. 执行如下命令，采用安全方式操作。

<a id="li33401337142911"></a><a id="ALM-6008__li33401337142911"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	1. 输入1，选择使用keystone v3鉴权。

<a id="ALM-6008__li1834013702916"></a><a id="li1834013702916"></a>显示如下信息：

Input command:

- 
	1. 执行如下命令启动apacheproxy服务。

<a id="li13404373292"></a><a id="ALM-6008__li13404373292"></a>__cps host\-template\-instance\-operate \-\-service apacheproxy apacheproxy \-\-action start__

回显如下类似信息：

![图片加载失败]()

查看操作结果是否为success。

- 
	1. 是，执行11。
	2. 否，执行17。

1. <a id="ALM-6008__li1334283718296"></a><a id="li1334283718296"></a>执行如下操作查看apacheproxy服务是否正常。
	1. 执行如下命令，采用安全方式操作。

<a id="li434113752918"></a><a id="ALM-6008__li434113752918"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	1. 输入“1”，选择使用keystone v3鉴权。

<a id="ALM-6008__li8341143711291"></a><a id="li8341143711291"></a>显示如下信息：

Input command:

- 
	1. 执行如下命令查看apacheproxy服务是否正常。

<a id="ALM-6008__li1734183782915"></a><a id="li1734183782915"></a>__cps template\-instance\-list \-\-service apacheproxy apacheproxy__

在apacheproxy主备部署的情况下，回显如下类似信息：

![图片加载失败]()

在apacheproxy单实例部署的情况下，回显如下类似信息：

![图片加载失败]()

- 
	1. 如未显示fault，执行12。
	2. 如仍显示为fault，执行17。

__日志配置文件中OBS服务IP、端口未正确设置__

1. <a id="ALM-6008__li153071552163016"></a><a id="li153071552163016"></a>执行以下命令，获取配置的OBS信息，确认OBS日志服务地址是否正确。

__log policy\-get__

42174775\-2DA8\-B93F\-CF47\-47902E7AA2B0:~ \# log policy\-get

\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+

| Property                    |                  Value          |

\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+

| policy\_gateway              |                                 |

| policy\_s3\_access\_key        |  92514a3e0b5a4acbb532aba9638    |

| policy\_s3\_export\_begin      |                   2             |

| policy\_s3\_export\_end        |                   3             |

| policy\_s3\_host              |  __s3\.dc1\.domainname\.com:5443__    |

| policy\_s3\_operate\_bucket    |                                 |

| policy\_s3\_operate\_lifecycle |                   4             |

| policy\_s3\_region            |                                 |

| policy\_s3\_run\_bucket        |                                 |

| policy\_s3\_run\_lifecycle     |                   90            |

| policy\_s3\_scheme            |                                 |

| policy\_s3\_secret\_key        |   3d99c09e38614b31930e349ec638  |

\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+

- 
	- 是，请执行14。
	- 否，请执行13。

1. <a id="ALM-6008__li1430913523305"></a><a id="li1430913523305"></a>执行如下命令重新设置OBS服务地址\(policy\_s3\_host\)，AK\(policy\_s3\_access\_key\)/SK\(policy\_s3\_secret\_key\)等信息。
	- 执行如下命令，采用安全方式操作。

<a id="ALM-6008__li10308452123013"></a><a id="li10308452123013"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	- 输入“1”，选择使用keystone v3鉴权。

<a id="li3308135273010"></a><a id="ALM-6008__li3308135273010"></a>显示如下信息：

Input command:

- 
	- 执行如下命令重新设置OBS服务地址\(policy\_s3\_host\)，AK\(policy\_s3\_access\_key\)/SK\(policy\_s3\_secret\_key\)等信息。

<a id="ALM-6008__li12308155213304"></a><a id="li12308155213304"></a>__log policy\-set \-\-parameter policy\_s3\_access\_key=__ACCESS\_KEY__ policy\_s3\_secret\_key=__SECRET\_KEY__ policy\_s3\_host=__S3\_HOST

查看是否回显如下类似信息：

\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+ 

  | Property             | Value                            | 

  \+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+ 

  | policy\_s3\_access\_key | 92514a3e0b5a4acbb532aba957a9d66f | 

  | policy\_s3\_host       | s3\.dc1\.domainname\.com:5443       | 

  | policy\_s3\_secret\_key | 3d99c09e38614b31930e349ec63842a3 | 

  \+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\+

- 
	- 是，执行14。
	- 否，执行17。

1. <a id="ALM-6008__li183101052123018"></a><a id="li183101052123018"></a>执行如下操作手动触发日志上传。
	- 执行如下命令，采用安全方式操作。

<a id="li8310135219305"></a><a id="ALM-6008__li8310135219305"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	- 输入“1”，选择使用keystone v3鉴权。

<a id="li7310145223014"></a><a id="ALM-6008__li7310145223014"></a>显示如下信息：

Input command:

- 
	- 执行如下命令手动触发日志上传。

<a id="li11310155215309"></a><a id="ALM-6008__li11310155215309"></a>__log log\-flush \-\-host __主机ID

1. <a id="ALM-6008__li43116526304"></a><a id="li43116526304"></a>等待5分钟，执行如下操作查看各组件的日志上传状态。
	- 执行如下命令，采用安全方式操作。

<a id="ALM-6008__li1031195218308"></a><a id="li1031195218308"></a>__cpssafe__

显示如下信息：

  please choose environment variable which you want to import: 

  \(1\) openstack environment variable \(keystone v3\) 

  \(2\) cps environment variable 

  \(3\) openstack environment variable legacy \(keystone v2\) 

  please choose:\[1|2|3\]

- 
	- 输入“1”，选择使用keystone v3鉴权。

<a id="ALM-6008__li03115526303"></a><a id="li03115526303"></a>显示如下信息：

Input command:

- 
	- 执行如下命令查看各组件的日志上传状态。

<a id="ALM-6008__li1631145203019"></a><a id="li1631145203019"></a>__log log\-state\-get \-\-host __主机ID

- 
	- 状态字段显示为上传时间，执行16。
	- 存在状态字段显示为“log flushing”，重新执行15。
	- 存在状态字段显示为“not flushed yet”，转17。

1. <a id="ALM-6008__li8313852103010"></a><a id="li8313852103010"></a>等待1分钟查看告警是否恢复。
	- 是，处理完毕。
	- 否，执行17。
2. <a id="li15313175212303"></a><a id="ALM-6008__li15313175212303"></a>请联系技术支持工程师协助解决。

#### 参考信息

无。

