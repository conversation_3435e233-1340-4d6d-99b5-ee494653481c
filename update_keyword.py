import requests
import json

# 配置参数
dataset_id = "06ea8a7b-17f1-48d5-9e9f-53c0c6caace8"
document_id = "215502cd-9dc8-4b02-b83b-4e6e9f8a39a2"
document_id = "0d586cdc-b1d2-4cd8-8aca-92321adfe2d1"
segment_id = "28729628-7e59-4e1a-b7aa-8a9af2025147"
base_url = f"http://localhost/v1/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
api_key = "dataset-dyT48T6t6lrkKq06EYv3qlKV"  # 替换为你的实际 API Key


# 请求头
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "segment": {
        "content": "*******.101 ALM-1200067 DHCP服务不可用",
        "keywords": ["1200067","DHCP服务不可用","ALM","*******.101"],
    }
}

try:
    # 发送 GET 请求
    response = requests.get(
        base_url.format(dataset_id=dataset_id, document_id=document_id),
        headers=headers
    )

    response.raise_for_status()  # 检查请求是否成功

    # 解析并美化打印响应结果
    result = response.json()
    print(json.dumps(result, indent=2, ensure_ascii=False))

except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
except json.JSONDecodeError as e:
    print(f"JSON 解析失败: {e}")
except Exception as e:
    print(f"发生未知错误: {e}")