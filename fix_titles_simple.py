#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版MD标题修复器
"""

import os
import glob

def fix_md_titles():
    """修复MD文件中的标题格式"""

    # 需要检查的关键词
    keywords = [
        "告警解释",
        "告警属性",
        "告警参数",
        "对系统的影响",
        "可能原因",
        "处理步骤",
        "参考信息"
    ]

    # 查找所有MD文件
    directory = "optimized_batch_results"
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return

    md_files = glob.glob(os.path.join(directory, "*.md"))

    if not md_files:
        print(f"⚠️  在目录 {directory} 中未找到MD文件")
        return

    print(f"🔍 找到 {len(md_files)} 个MD文件")

    modified_count = 0
    total_fixes = 0

    for file_path in md_files:
        filename = os.path.basename(file_path)
        print(f"\n📄 处理文件: {filename}")

        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            new_lines = []
            file_modified = False
            file_fixes = 0

            for i, line in enumerate(lines):
                line_modified = False

                # 检查是否包含关键词
                for keyword in keywords:
                    if keyword in line.strip():
                        # 检查是否已经是标题（以#开头）
                        if not line.strip().startswith('#'):
                            # 不是标题，改为五级标题
                            new_line = f"##### {line.strip()}\n"
                            new_lines.append(new_line)
                            print(f"  第{i+1}行: '{line.strip()}' -> '##### {line.strip()}'")
                            line_modified = True
                            file_modified = True
                            file_fixes += 1
                            total_fixes += 1
                            break

                if not line_modified:
                    # 没有修改，保持原样
                    new_lines.append(line)

            # 如果有修改，写回文件
            if file_modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                print(f"  ✅ 修复了 {file_fixes} 处标题，文件已保存")
                modified_count += 1
            else:
                print(f"  ✅ 文件无需修复")

        except Exception as e:
            print(f"  ❌ 处理文件时出错: {e}")

    # 输出总结
    print(f"\n" + "="*50)
    print(f"📊 处理完成总结")
    print(f"="*50)
    print(f"📁 处理目录: {directory}")
    print(f"📄 总文件数: {len(md_files)}")
    print(f"🔧 修复文件数: {modified_count}")
    print(f"🔧 总修复数: {total_fixes}")

    if modified_count > 0:
        print(f"\n🎉 已修复 {modified_count} 个文件的标题格式")
    else:
        print(f"\n✨ 所有文件的标题格式都正确，无需修复")

if __name__ == "__main__":
    fix_md_titles()
