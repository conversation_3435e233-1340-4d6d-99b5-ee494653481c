#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心文档转换器 - 提取自enhanced_doc_converter的核心逻辑
专门用于批量转换，确保与单独测试test.docx的输出效果一致
"""

from docx import Document
import os
import re
import zipfile
from PIL import Image
import base64
from io import BytesIO

try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False


class CoreDocConverter:
    """核心文档转换器类"""

    def __init__(self, docx_path, output_dir="output"):
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.image_dir = os.path.join(output_dir, "images")
        self.doc = Document(docx_path)
        self.image_counter = 0
        self.image_map = {}

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)

        # 初始化章节和步骤计数器
        self.current_section = ""
        self.step_counter = 0

    def convert_table_to_markdown(self, table):
        """将表格转换为Markdown格式"""
        if not table.rows:
            return ""

        markdown_table = []

        # 处理表头
        header_row = table.rows[0]
        headers = []
        for cell in header_row.cells:
            cell_text = self.clean_cell_text(cell.text)
            headers.append(cell_text)

        if headers:
            markdown_table.append("| " + " | ".join(headers) + " |")
            markdown_table.append("| " + " | ".join(["---"] * len(headers)) + " |")

        # 处理数据行
        for row in table.rows[1:]:
            row_data = []
            for cell in row.cells:
                cell_text = self.clean_cell_text(cell.text)
                row_data.append(cell_text)

            if row_data:
                # 确保行数据与表头列数一致
                while len(row_data) < len(headers):
                    row_data.append("")
                markdown_table.append("| " + " | ".join(row_data[:len(headers)]) + " |")

        return "\n".join(markdown_table)

    def clean_cell_text(self, text):
        """清理单元格文本"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())

        # 处理换行符，在表格中用<br>表示
        text = text.replace('\n', '<br>')

        # 不转义管道符，保持原样
        # text = text.replace('|', '\\|')

        return text

    def process_paragraph_text(self, paragraph):
        """处理段落文本，保持格式"""
        if not paragraph.text.strip():
            return ""

        text = paragraph.text

        # 清理多余的空白字符，但保留必要的换行
        text = re.sub(r'[ \t]+', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n', text)

        # 如果是表格数据，尝试格式化
        if '|' in text:
            text = self._format_table_text(text)

        return text.strip()

    def _format_table_text(self, text):
        """格式化表格文本"""
        lines = text.split('\n')
        formatted_lines = []

        for line in lines:
            if '|' in line:
                parts = line.split('|')
                cleaned_parts = []

                for part in parts:
                    part = part.strip()
                    if part and len(part) > 1:
                        cleaned_parts.append(part)

                if len(cleaned_parts) >= 2:
                    formatted_lines.append(' | '.join(cleaned_parts))
            else:
                if line.strip():
                    formatted_lines.append(line.strip())

        return '\n'.join(formatted_lines)

    def get_paragraph_style_info(self, paragraph):
        """获取段落的详细样式信息"""
        style_info = {
            'is_heading': False,
            'heading_level': 0,
            'is_list_item': False,
            'is_ordered_list': False,
            'list_level': 0,
            'is_bold': False,
            'is_italic': False,
            'alignment': 'left',
            'indent_level': 0,
            'section_context': self._get_section_context(paragraph)
        }

        # 检查是否为标题
        if paragraph.style.name.startswith('Heading'):
            style_info['is_heading'] = True
            try:
                style_info['heading_level'] = int(paragraph.style.name.split()[-1])
            except:
                style_info['heading_level'] = 1

        # 检查是否为列表项
        try:
            numPr_elements = paragraph._p.xpath('.//w:numPr')
            if numPr_elements:
                style_info['is_list_item'] = True

                # 获取列表级别
                ilvl_elements = paragraph._p.xpath('.//w:ilvl/@w:val')
                if ilvl_elements:
                    style_info['list_level'] = int(ilvl_elements[0])

                # 检查是否为有序列表
                numId_elements = paragraph._p.xpath('.//w:numId/@w:val')
                if numId_elements:
                    style_info['is_ordered_list'] = self._should_use_ordered_list(paragraph, style_info)
            else:
                # 通过文本内容检测列表
                text = paragraph.text.strip()
                if text:
                    if text.startswith(('•', '-', '*', '●', '◦', '⁃')):
                        style_info['is_list_item'] = True
                        style_info['is_ordered_list'] = False
                    elif text.startswith(tuple(f'{i}.' for i in range(1, 21))):
                        style_info['is_list_item'] = True
                        style_info['is_ordered_list'] = True
        except:
            pass

        # 检查缩进级别
        try:
            if paragraph.paragraph_format.left_indent:
                indent_pt = paragraph.paragraph_format.left_indent.pt
                style_info['indent_level'] = max(0, int(indent_pt / 36))
        except:
            pass

        # 检查文本格式
        for run in paragraph.runs:
            if run.bold:
                style_info['is_bold'] = True
            if run.italic:
                style_info['is_italic'] = True

        return style_info

    def _get_section_context(self, paragraph):
        """获取段落所在的章节上下文"""
        doc_elements = list(self.doc.element.body)
        current_element = paragraph._element

        try:
            current_index = doc_elements.index(current_element)

            # 向上查找最近的标题
            for i in range(current_index - 1, -1, -1):
                element = doc_elements[i]
                if element.tag.endswith('p'):
                    for para in self.doc.paragraphs:
                        if para._element == element:
                            if para.style.name.startswith('Heading'):
                                return para.text.strip()
                            break
        except:
            pass

        return ""

    def _should_use_ordered_list(self, paragraph, style_info):
        """根据上下文决定是否使用有序列表"""
        section_context = style_info['section_context']
        list_level = style_info['list_level']

        # 可能原因章节使用无序列表
        if '可能原因' in section_context:
            return False

        # 处理步骤章节：最外层用有序，内层用无序
        if '处理步骤' in section_context:
            return list_level == 0

        # 其他情况默认使用有序列表
        return True

    def _is_subsection_title(self, paragraph, style_info):
        """检查是否为小标题"""
        text = paragraph.text.strip()
        section_context = style_info['section_context']

        # 只在处理步骤章节中检查
        if '处理步骤' not in section_context:
            return False

        # 如果已经被识别为列表项，则不是小标题
        if style_info['is_list_item']:
            return False

        # 检查精确的小标题模式
        exact_subsection_patterns = [
            'Apacheproxy服务状态异常',
            '日志配置文件中OBS服务IP、端口未正确设置'
        ]

        for pattern in exact_subsection_patterns:
            if text == pattern or text.strip() == pattern:
                return True

        return False

    def _should_indent(self, paragraph, markdown_content):
        """检查段落是否应该缩进"""
        if not markdown_content:
            return False

        text = paragraph.text.strip()

        # 检查最近的几行是否是列表项或代码块
        for i in range(min(10, len(markdown_content))):
            recent_line = markdown_content[-(i+1)].strip()
            if (recent_line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')) or
                recent_line.startswith(tuple(f'{j}. ' for j in range(10, 100))) or
                recent_line.startswith('- ') or
                recent_line == '```'):
                return True

        # 检查段落内容是否像是列表项的补充说明
        if text.startswith(('具体请参见', '默认', '系统同时支持', '显示如下', '回显如下')):
            return True

        # 检查是否是表格内容
        if text.startswith('|') or '+---' in text or text.startswith('+'):
            return True

        # 检查是否是命令输出或特殊格式
        if any(keyword in text for keyword in ['policy_', 'Property', 'Value', '42174775-']):
            return True

        return False

    def _calculate_context_indent(self, markdown_content):
        """根据上下文计算合适的缩进级别"""
        if not markdown_content:
            return 0

        # 查找最近的列表项或代码块
        for i in range(min(15, len(markdown_content))):
            recent_line = markdown_content[-(i+1)]
            stripped_line = recent_line.strip()

            # 检查是否是有序列表项
            if (stripped_line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')) or
                stripped_line.startswith(tuple(f'{j}. ' for j in range(10, 100)))):
                leading_spaces = len(recent_line) - len(recent_line.lstrip())
                return (leading_spaces // 3) + 1

            # 检查是否是无序列表项
            elif stripped_line.startswith('- '):
                leading_spaces = len(recent_line) - len(recent_line.lstrip())
                return (leading_spaces // 3) + 1

            # 检查是否是代码块开始
            elif stripped_line == '```':
                for j in range(i+1, min(i+10, len(markdown_content))):
                    prev_line = markdown_content[-(j+1)]
                    prev_stripped = prev_line.strip()
                    if (prev_stripped.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')) or
                        prev_stripped.startswith(tuple(f'{k}. ' for k in range(10, 100))) or
                        prev_stripped.startswith('- ')):
                        leading_spaces = len(prev_line) - len(prev_line.lstrip())
                        return (leading_spaces // 3) + 1
                break

        return 1

    def _extract_original_number(self, formatted_text):
        """从原始文本中提取编号"""
        match = re.match(r'^(\d+)\.', formatted_text.strip())
        if match:
            return match.group(1)
        return None

    def _is_code_block(self, text):
        """检查是否为代码块"""
        code_indicators = [
            'su - root',
            'TMOUT=0',
            'source set_env',
            'cpssafe',
            'cps template-instance-list',
            'cps host-template-instance-operate',
            'log policy-get',
            'log policy-set',
            'log log-flush',
            'log log-state-get'
        ]

        for indicator in code_indicators:
            if indicator in text:
                return True

        # 检查是否以命令开头
        if text.startswith(('$', '#', '>', 'Input command:')):
            return True

        return False

    def _is_echo_content(self, text):
        """检查是否为回显信息内容"""
        echo_indicators = [
            'please choose environment variable',
            'openstack environment variable',
            'cps environment variable',
            'please choose:[',
            'Input command:',
            '回显如下类似信息',
            '显示如下信息'
        ]

        for indicator in echo_indicators:
            if indicator in text:
                return True

        # 检查是否包含多行选项格式
        if '(1)' in text and '(2)' in text:
            return True

        return False

    def _process_paragraph(self, paragraph, markdown_content):
        """处理段落"""
        text = paragraph.text.strip()
        if not text:
            return

        style_info = self.get_paragraph_style_info(paragraph)
        formatted_text = self.process_paragraph_text(paragraph)

        # 更新当前章节
        if style_info['is_heading']:
            self.current_section = formatted_text
            # 如果进入处理步骤章节，重置步骤计数器
            if '处理步骤' in formatted_text:
                self.step_counter = 0

        # 处理标题
        if style_info['is_heading']:
            level = style_info['heading_level']
            markdown_content.append(f"{'#' * level} {formatted_text}")
            markdown_content.append("")

        # 处理小标题
        elif self._is_subsection_title(paragraph, style_info):
            markdown_content.append("")
            markdown_content.append(f"**{formatted_text}**")
            markdown_content.append("")

        # 处理列表项
        elif style_info['is_list_item']:
            # 提取原始编号
            original_number = self._extract_original_number(formatted_text)

            # 清理文本开头的列表标记
            clean_text = formatted_text
            if clean_text.startswith(('•', '-', '*', '●', '◦', '⁃')):
                clean_text = clean_text[1:].strip()
            elif clean_text.startswith(tuple(f'{i}.' for i in range(1, 100))):
                clean_text = clean_text[clean_text.find('.')+1:].strip()

            indent = '   ' * style_info['list_level']

            if style_info['is_ordered_list']:
                # 有序列表
                if '处理步骤' in self.current_section and style_info['list_level'] == 0:
                    # 处理步骤中的最外层列表使用连续编号
                    self.step_counter += 1
                    markdown_content.append(f"{indent}{self.step_counter}. {clean_text}")
                elif original_number:
                    # 其他情况保持原文档的编号
                    markdown_content.append(f"{indent}{original_number}. {clean_text}")
                else:
                    # 如果没有提取到原始编号，使用默认编号
                    markdown_content.append(f"{indent}1. {clean_text}")
            else:
                # 无序列表 - 统一使用短横线
                markdown_content.append(f"{indent}- {clean_text}")

        # 处理有缩进但不是列表的段落
        elif style_info['indent_level'] > 0 or self._should_indent(paragraph, markdown_content):
            # 计算缩进级别
            indent_level = max(style_info['indent_level'], self._calculate_context_indent(markdown_content))
            indent = '   ' * indent_level

            # 检查是否是代码块
            if self._is_code_block(formatted_text):
                markdown_content.append(f"{indent}```")
                markdown_content.append(f"{indent}{formatted_text}")
                markdown_content.append(f"{indent}```")
            elif self._is_echo_content(formatted_text):
                # 处理回显信息，每行都加上缩进
                for line in formatted_text.split('\n'):
                    if line.strip():
                        markdown_content.append(f"{indent}{line.strip()}")
                    else:
                        markdown_content.append("")
            else:
                # 普通缩进段落
                markdown_content.append(f"{indent}{formatted_text}")

        # 处理普通段落
        else:
            # 检查是否是代码块或特殊格式
            if self._is_code_block(formatted_text):
                markdown_content.append(f"```")
                markdown_content.append(formatted_text)
                markdown_content.append(f"```")
            else:
                markdown_content.append(formatted_text)
            markdown_content.append("")  # 空行

    def _process_table(self, table, markdown_content):
        """处理表格"""
        table_md = self.convert_table_to_markdown(table)
        if table_md:
            markdown_content.append(table_md)
            markdown_content.append("")

    def convert_to_markdown(self, output_filename="document.md"):
        """主转换函数 - 核心逻辑"""
        markdown_content = []
        markdown_content.append("# 文档转换结果\n")

        # 初始化章节和步骤计数器
        self.current_section = ""
        self.step_counter = 0

        # 获取所有文档元素
        elements = []

        for element in self.doc.element.body:
            if element.tag.endswith('p'):  # 段落
                for para in self.doc.paragraphs:
                    if para._element == element:
                        elements.append(('paragraph', para))
                        break
            elif element.tag.endswith('tbl'):  # 表格
                for table in self.doc.tables:
                    if table._tbl == element:
                        elements.append(('table', table))
                        break

        # 处理每个元素
        for elem_type, elem in elements:
            if elem_type == 'paragraph':
                self._process_paragraph(elem, markdown_content)
            elif elem_type == 'table':
                self._process_table(elem, markdown_content)

        # 写入文件
        output_path = os.path.join(self.output_dir, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

        return output_path


def convert_docx_to_markdown(docx_path, output_dir, output_filename="document.md"):
    """
    核心转换函数 - 供批量调用使用

    Args:
        docx_path: 输入的docx文件路径
        output_dir: 输出目录
        output_filename: 输出文件名

    Returns:
        转换后的markdown文件路径
    """
    converter = CoreDocConverter(docx_path, output_dir)
    return converter.convert_to_markdown(output_filename)


# 测试函数 - 验证与原版输出一致性
def test_consistency():
    """测试核心转换器与原版的一致性"""
    input_file = "test.docx"
    output_dir = "core_test_output"

    if not os.path.exists(input_file):
        print(f"错误：找不到测试文件 {input_file}")
        return

    try:
        output_file = convert_docx_to_markdown(input_file, output_dir, "test_core_converted.md")
        print(f"✅ 核心转换器测试成功！")
        print(f"📄 输出文件: {output_file}")

        # 读取并显示前几行内容进行对比
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"\n📊 转换结果预览（前10行）:")
            for i, line in enumerate(lines[:10], 1):
                print(f"{i:2d}: {line.rstrip()}")

        return output_file

    except Exception as e:
        print(f"❌ 核心转换器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    test_consistency()
