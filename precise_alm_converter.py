#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确版ALM告警文档批量转换器
只提取*******.x格式的ALM告警章节，避免处理父级模块
"""

import os
import re
import time
from datetime import datetime
from docx import Document
from enhanced_doc_converter import EnhancedDocxToMarkdown
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('alm_conversion_precise.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class PreciseALMConverter:
    def __init__(self, source_docx, output_dir="alm_markdown_output"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        self.temp_dir = os.path.join(output_dir, "temp_docx")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        logger.info(f"初始化精确版ALM批量转换器")
        logger.info(f"源文档: {source_docx}")
        logger.info(f"输出目录: {output_dir}")
    
    def find_alm_chapters_precise(self):
        """精确查找ALM章节（只匹配*******.x格式）"""
        logger.info("🔍 开始精确查找ALM章节...")
        
        chapters = []
        doc = Document(self.source_docx)
        
        for i, paragraph in enumerate(doc.paragraphs):
            # 只检查标题样式的段落
            if not paragraph.style.name.startswith('Heading 1'):
                continue
                
            text = paragraph.text.strip()
            
            # 精确匹配：必须是*******.x格式且包含ALM-
            # 使用正则表达式确保格式正确
            pattern = r'^5\.2\.3\.1\.\d+\s+ALM-\d+'
            if re.match(pattern, text):
                # 提取ALM代码
                alm_match = re.search(r'ALM-\d+', text)
                if alm_match:
                    alm_code = alm_match.group()
                    
                    # 提取告警名称
                    title_match = re.search(r'ALM-\d+\s+(.+)', text)
                    alarm_name = title_match.group(1) if title_match else "未知告警"
                    
                    chapters.append({
                        'title': text,
                        'alm_code': alm_code,
                        'alarm_name': alarm_name,
                        'start_para': i
                    })
                    
                    logger.info(f"  找到: {alm_code} - {alarm_name}")
                    
                    if len(chapters) % 10 == 0:
                        logger.info(f"  已找到 {len(chapters)} 个ALM章节...")
            
            # 如果遇到*******开头的标题，说明*******的章节已经结束
            elif text.startswith('*******'):
                logger.info(f"  遇到下一个主要章节: {text[:50]}...")
                logger.info(f"  停止搜索，*******章节已全部找到")
                break
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                # 最后一个章节的结束位置需要特殊处理
                # 查找下一个主要章节（*******）的位置
                end_para = len(doc.paragraphs)
                for j in range(chapter['start_para'] + 1, len(doc.paragraphs)):
                    para_text = doc.paragraphs[j].text.strip()
                    if para_text.startswith('*******'):
                        end_para = j
                        break
                chapter['end_para'] = end_para
        
        logger.info(f"✅ 精确找到 {len(chapters)} 个ALM告警章节")
        return chapters
    
    def create_chapter_docx(self, chapter):
        """创建单个章节的DOCX文件"""
        try:
            # 重新打开文档（避免内存问题）
            doc = Document(self.source_docx)
            new_doc = Document()
            
            start_para = chapter['start_para']
            end_para = chapter['end_para']
            
            logger.info(f"     提取段落范围: {start_para} - {end_para}")
            
            # 复制段落
            for i in range(start_para, min(end_para, len(doc.paragraphs))):
                para = doc.paragraphs[i]
                new_para = new_doc.add_paragraph()
                new_para.text = para.text
                
                # 尝试复制样式
                try:
                    new_para.style = para.style
                except:
                    pass
            
            # 生成文件名
            safe_name = self.generate_safe_filename(chapter)
            docx_path = os.path.join(self.temp_dir, f"{safe_name}.docx")
            
            # 保存文档
            new_doc.save(docx_path)
            
            return docx_path, safe_name
            
        except Exception as e:
            logger.error(f"创建章节文档时出错: {e}")
            return None, None
    
    def generate_safe_filename(self, chapter):
        """生成安全的文件名"""
        alm_code = chapter['alm_code']
        alarm_name = chapter['alarm_name']
        
        # 清理文件名中的非法字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', alarm_name)
        safe_name = safe_name.strip()
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]
        
        return f"{alm_code}_{safe_name}"
    
    def convert_chapter_to_markdown(self, docx_path, safe_name):
        """将单个章节转换为Markdown"""
        try:
            # 创建转换器
            converter = EnhancedDocxToMarkdown(
                docx_path, 
                os.path.join(self.output_dir, f"{safe_name}_output")
            )
            
            # 执行转换
            md_filename = f"{safe_name}.md"
            output_path = converter.convert_to_markdown(md_filename)
            
            return output_path
            
        except Exception as e:
            logger.error(f"转换章节 {safe_name} 时出错: {e}")
            return None
    
    def process_chapters_batch(self, chapters, batch_size=5):
        """批量处理章节（减少内存使用）"""
        logger.info(f"🚀 开始批量处理 {len(chapters)} 个ALM章节...")
        
        successful_conversions = []
        failed_conversions = []
        
        # 分批处理
        for batch_start in range(0, len(chapters), batch_size):
            batch_end = min(batch_start + batch_size, len(chapters))
            batch_chapters = chapters[batch_start:batch_end]
            
            logger.info(f"\n📦 处理批次 {batch_start//batch_size + 1}: 章节 {batch_start+1}-{batch_end}")
            
            for i, chapter in enumerate(batch_chapters):
                chapter_idx = batch_start + i + 1
                chapter_start_time = time.time()
                
                logger.info(f"\n📄 处理第 {chapter_idx}/{len(chapters)} 章: {chapter['alm_code']}")
                logger.info(f"   标题: {chapter['alarm_name']}")
                
                try:
                    # 1. 创建章节文档
                    logger.info("   📝 创建章节文档...")
                    docx_path, safe_name = self.create_chapter_docx(chapter)
                    
                    if not docx_path:
                        logger.error("   ❌ 创建文档失败")
                        failed_conversions.append({
                            'chapter': chapter,
                            'error': '创建文档失败'
                        })
                        continue
                    
                    # 2. 转换为Markdown
                    logger.info("   🔄 转换为Markdown...")
                    md_path = self.convert_chapter_to_markdown(docx_path, safe_name)
                    
                    if md_path:
                        chapter_time = time.time() - chapter_start_time
                        logger.info(f"   ✅ 转换成功! 耗时: {chapter_time:.2f}秒")
                        logger.info(f"   📁 输出: {md_path}")
                        
                        successful_conversions.append({
                            'chapter': chapter,
                            'md_path': md_path,
                            'docx_path': docx_path,
                            'time': chapter_time
                        })
                    else:
                        logger.error(f"   ❌ 转换失败")
                        failed_conversions.append({
                            'chapter': chapter,
                            'error': '转换失败'
                        })
                    
                    # 清理临时文件（可选）
                    # try:
                    #     os.remove(docx_path)
                    # except:
                    #     pass
                    
                except Exception as e:
                    logger.error(f"   ❌ 处理章节时出错: {e}")
                    failed_conversions.append({
                        'chapter': chapter,
                        'error': str(e)
                    })
                    continue
        
        return successful_conversions, failed_conversions
    
    def process_all_chapters(self):
        """处理所有章节"""
        start_time = time.time()
        
        # 查找章节
        chapters = self.find_alm_chapters_precise()
        
        if not chapters:
            logger.error("❌ 未找到ALM章节")
            return []
        
        # 批量处理
        successful_conversions, failed_conversions = self.process_chapters_batch(chapters)
        
        # 输出总结
        total_time = time.time() - start_time
        self.print_summary(successful_conversions, failed_conversions, total_time)
        
        return successful_conversions
    
    def print_summary(self, successful, failed, total_time):
        """打印处理总结"""
        logger.info("\n" + "="*80)
        logger.info("📊 批量转换完成总结")
        logger.info("="*80)
        
        logger.info(f"⏱️  总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        logger.info(f"✅ 成功转换: {len(successful)} 个章节")
        logger.info(f"❌ 转换失败: {len(failed)} 个章节")
        
        if len(successful) + len(failed) > 0:
            logger.info(f"📈 成功率: {len(successful)/(len(successful)+len(failed))*100:.1f}%")
        
        if successful:
            logger.info(f"\n📁 成功转换的文件:")
            for i, item in enumerate(successful[:10], 1):  # 只显示前10个
                chapter = item['chapter']
                logger.info(f"  {i:2d}. {chapter['alm_code']} - {chapter['alarm_name'][:40]}...")
            
            if len(successful) > 10:
                logger.info(f"  ... 还有 {len(successful) - 10} 个文件")
        
        if failed:
            logger.info(f"\n❌ 转换失败的章节:")
            for i, item in enumerate(failed, 1):
                chapter = item['chapter']
                error = item['error']
                logger.info(f"  {i:2d}. {chapter['alm_code']} - {error}")
        
        logger.info(f"\n📂 输出目录: {self.output_dir}")
        logger.info("="*80)


def create_index_file(successful_conversions, output_dir):
    """创建索引文件"""
    try:
        index_path = os.path.join(output_dir, "README.md")
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write("# 华为云Stack告警处理参考 - ALM章节索引\n\n")
            f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"总计: {len(successful_conversions)} 个ALM告警章节\n\n")
            f.write("## 章节列表\n\n")
            
            for i, item in enumerate(successful_conversions, 1):
                chapter = item['chapter']
                md_filename = os.path.basename(item['md_path'])
                
                f.write(f"{i:3d}. [{chapter['alm_code']} {chapter['alarm_name']}]({md_filename})\n")
        
        logger.info(f"📋 已生成索引文件: {index_path}")
        
    except Exception as e:
        logger.warning(f"生成索引文件时出错: {e}")


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "alm_markdown_results_precise"
    
    if not os.path.exists(source_file):
        logger.error(f"❌ 错误：找不到源文件 {source_file}")
        return
    
    # 创建转换器
    converter = PreciseALMConverter(source_file, output_dir)
    
    # 执行批量转换
    try:
        successful_conversions = converter.process_all_chapters()
        
        if successful_conversions:
            logger.info(f"\n🎉 批量转换完成!")
            logger.info(f"📄 成功转换 {len(successful_conversions)} 个ALM告警章节")
            logger.info(f"📁 输出目录: {output_dir}")
            
            # 生成索引文件
            create_index_file(successful_conversions, output_dir)
        else:
            logger.error("❌ 没有成功转换任何章节")
            
    except Exception as e:
        logger.error(f"❌ 批量转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
