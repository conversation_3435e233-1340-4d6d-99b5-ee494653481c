# ALM-6028 本地NTP客户端与本地NTP服务器时间差超过60秒

## 5.2.3.1.17 ALM-6028 本地NTP客户端与本地NTP服务器时间差超过60秒

## 告警解释

ntp-client（NTP客户端）会周期性（默认为2min）检查本节点与主ntp-server（NTP服务器）所在节点的时间差，当时间差超过60s时，产生此告警。当时间差修复后，告警消除。

## 告警属性

## 告警参数

## 对系统的影响

如果ntp-client所在节点与本地主ntp-server所在节点时间差超过60s，则不进行时间同步。

可能导致合法的token未超过有效期不可用或超过有效期却可用。

可能导致时间不同步节点服务不可用。

可能导致系统数据采样不正确。

可能导致数据丢失。

## 可能原因

本地主ntp-server所在节点时间被修改。

ntp-client所在节点时间被修改。

备ntp-server所在节点与主ntp-server长期网络不通时间未同步，且ntp-client所在节点与本地主ntp-server时间同步，此时触发主备倒换。

ntp-client所在节点与主ntp-server长期网络不通后恢复网络。

## 处理步骤

登录FusionSphere OpenStack安装部署界面。

具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。

选择“运维”，进入“日常维护”。


强制时间同步是高危操作，操作前请慎重考虑，操作时需先停止系统服务，然后进行时间同步，同步成功后再重启业务，该动作建议在业务部署前或者业务稳态时执行，否则可能会严重影响业务。该命令不支持远程使用。

稍等2分钟，在“时间同步”标签下，提示由“系统时间状态查询中”变为“系统时间异常”，如图所示。


按提示，单击右侧红色图标，弹出提示窗，稍等2分钟,提示窗列出时钟异常单板。


选择需要同步时间的单板，在提示窗中单击“同步修复”。


强制时间同步过程中管理面会中断10-15分钟，业务发放会失败，已发放业务不会受影响。请根据实际业务判断是否要执行该操作。


强制时间同步是高危操作，操作前请慎重考虑，该动作建议在业务部署前或者业务稳态时执行，否则可能会严重影响业务。强制时间同步过程中会重启主机上除了ntp-server，ntp-client，dns-server，cps-monitor，haproxy服务外的所有服务，因此该命令不支持远程登录使用。

成功对接外部时钟源且时间已同步一致之后，后续如果没有人为修改主ntp-server所在节点时间，产生了此告警，需要排查外部时钟源时间跳变根因，时间强制同步只是规避手段。例如存在多个外部时钟源，且外部时钟源间存在60s以上时差，则需要移除不准确的外部时钟源，单纯通过强制时间同步无法消除告警。

等待5分钟～10分钟，在“时间同步”标签下，提示变为“系统时间正常”，表示时间同步成功。

若提示“同步时间异常”，表示主机强制时间同步异常。

按提示，单击右侧红色图标，弹出提示窗，稍等2分钟,提示窗列出同步时钟失败单板。如图所示。


是否有主机强制时间同步异常。

是，参考如何处理强制时间同步失败进行处理。

否，执行7。

等待5分钟～10分钟，查看告警是否清除。

是，处理完毕。

否，执行8。

请联系技术支持工程师协助解决。

## 参考信息

无。

父主题： FusionSphere OpenStack告警参考

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 >


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6008 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6010 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>Region：产生告警的region名。<br>Object：产生告警的服务。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：NTP客户端ip。<br>对端地址：外部时钟源ip。<br>阈值：ntp-server和外部时钟源间可缓慢同步的最大时差。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6014 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务名称 |
| 附加信息 | 区域名：产生告警的域名<br>对端地址：产生告警的DNS服务器的IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6015 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>Region：产生告警的region名。<br>Object：产生告警的服务。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：NTP客户端ip。<br>对端地址：外部时钟源ip。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6016 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6017 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：告警发送节点的地址。<br>对端地址：产生告警的主机地址。<br>故障原因：产生故障的原因。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6018 | 重要/次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 告警阈值：Minor：80%-90%，Major：>=90%，表示告警阈值。<br>主机名：产生告警的主机名。<br>已使用：当前主机CPU占用率。<br>进程列表：部分占用率较高的pid。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6019 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 告警阈值：Minor：99%-100%，表示告警阈值。<br>主机名：产生告警的主机名。<br>内存总量：主机内存的大小。<br>已使用：当前主机内存占用率。<br>进程列表：部分占用率较高的pid。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6020 | 重要/次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>逻辑磁盘：告警主机上产生告警的逻辑磁盘名称。 |
| 附加信息 | 告警阈值：Minor：85%-95%，Major：>=95%，表示告警阈值。<br>主机名：产生告警的主机名。<br>逻辑磁盘：产生告警的逻辑磁盘名称。<br>总容量：告警的逻辑磁盘的大小。<br>逻辑磁盘路径：告警的逻辑磁盘的挂载点。<br>已使用：当前告警逻辑磁盘使用率。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6021 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机：产生告警的主机ID<br>网口名： 产生告警的网卡名称 |
| 附加信息 | 主机ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>BMC_IP：产生告警的主机BMC IP地址<br>聚合模式：产生告警网卡的聚合模式<br>聚合名：产生告警网卡的聚合名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6022 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：主机管理ip。<br>对端地址：ntp-server浮动ip。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6023 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：存储链路异常的主机ID |
| 附加信息 | 主机ID：存储链路异常的主机ID<br>主机名：存储链路异常的主机名<br>异常ip：故障的存储链路IP |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6024 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：存储链路异常的主机ID<br>对象：产生告警的具体对象 |
| 附加信息 | 主机ID：存储链路异常的主机ID<br>主机名：存储链路异常的主机名 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6025 | 紧急/重要/次要/提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>阈值：存储使用率阈值<br>已使用容量：产生告警的后端存储的容量使用率<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6026 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>异常详情：故障信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6027 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6028 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：主机管理ip。<br>对端地址：ntp-server浮动ip。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6029 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的服务。 |
| 附加信息 | 云服务：产生告警的云服务。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6030 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 产生告警的主机ID。 |
| 附加信息 | 异常信息：<br>ip：冲突ip。<br>port：冲突端口。<br>mac：冲突mac。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6031 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6033 | 重要/次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | host：产生告警的主机ID。 |
| 附加信息 | 告警阈值：告警阈值信息<br>主机名：产生告警的主机名。<br>已使用：当前I层服务CPU占用率。<br>Cpu占用详情：当前占用率最高的前3个组件信息。 |


| 表1 Region Type I级联层 | 表1 Region Type I级联层 | 表1 Region Type I级联层 |
| --- | --- | --- |
| 档位 | FusionSphere OpenStack隔离的vCPU个数 | FusionSphere OpenStack隔离的内存(GB) |
| 50PM, 500VM | 10 | 43 |
| 100PM, 1000VM | 12 | 69 |
| 200PM, 2000VM | 14 | 69 |
| 500PM, 5000VM | 18 | 69 |
| 1000PM, 10000VM | 24 | 69 |
| 2000PM, 20000VM（及更大档位） | 24 | 69 |


| 表2 Region Type II和Region Type III | 表2 Region Type II和Region Type III | 表2 Region Type II和Region Type III |
| --- | --- | --- |
| 档位 | FusionSphere OpenStack隔离的vCPU个数 | FusionSphere OpenStack隔离的内存(GB) |
| 50PM, 500VM | 14 | 53 |
| 100PM, 1000VM | 22 | 78 |
| 200PM, 2000VM | 30 | 135 |
| 500PM, 5000VM | 30 | 135 |
| 1000PM, 10000VM | 34 | 135 |
| 2000PM, 20000VM（及更大档位） | 34 | 135 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6034 | 重要/次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | host：产生告警的主机ID。 |
| 附加信息 | 告警阈值：告警阈值信息。<br>主机名：产生告警的主机名。<br>内存总量：当前I层服务内存的总大小。<br>已使用：当前I层服务内存占用率。<br>进程列表：当前内存占用前20的进程详情。 |


| 表1 Region Type I级联层 | 表1 Region Type I级联层 | 表1 Region Type I级联层 |
| --- | --- | --- |
| 档位 | FusionSphere OpenStack隔离的vCPU个数 | FusionSphere OpenStack隔离的内存(GB) |
| 50PM, 500VM | 10 | 43 |
| 100PM, 1000VM | 12 | 69 |
| 200PM, 2000VM | 14 | 69 |
| 500PM, 5000VM | 18 | 69 |
| 1000PM, 10000VM | 24 | 69 |
| 2000PM, 20000VM（及更大档位） | 24 | 69 |


| 表2 Region Type II和Region Type III | 表2 Region Type II和Region Type III | 表2 Region Type II和Region Type III |
| --- | --- | --- |
| 档位 | FusionSphere OpenStack隔离的vCPU个数 | FusionSphere OpenStack隔离的内存(GB) |
| 50PM, 500VM | 14 | 53 |
| 100PM, 1000VM | 22 | 78 |
| 200PM, 2000VM | 30 | 135 |
| 500PM, 5000VM | 30 | 135 |
| 1000PM, 10000VM | 34 | 135 |
| 2000PM, 20000VM（及更大档位） | 34 | 135 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6036 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID。 |
| 附加信息 | 虚拟机名称：产生告警的虚拟机名称。<br>使用率：虚拟机当前cpu使用率。<br>阈值：当前阈值信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6037 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID。 |
| 附加信息 | 虚拟机名称：产生告警的虚拟机名称。<br>使用率：虚拟机当前内存/Swap分区占用率信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6038 | 紧急/重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | host_id：产生告警的主机ID。 |
| 附加信息 | 告警阈值：Major：85~95%，Critical：>= 95%，表示告警阈值。<br>主机名：产生告警的主机名。<br>已使用：当前磁盘占用率。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6039 | 紧急/重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机 ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>复用内存总量：主机的内存总量<br>复用内存已使用量：主机的内存使用量<br>阈值：主机的内存告警阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70100 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务名称<br>服务：产生告警的服务名称 |
| 附加信息 | 详细信息：告警的详细信息 |
| IP地址/URL/域名 | 产生告警的主机IP地址或者URL地址或者域名 |


| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| audit_orphan_vms | orphan_vm.csv |
| audit_invalid_vms | invalid_vm.csv |
| audit_host_changed_vms | host_changed_vm.csv |
| audit_stucking_vms | stucking_vm.csv |
| audit_diff_state_vms | diff_state_vm.csv |
| audit_stucking_migrations | cold_stuck.csv |
| audit_host_invalid_migrations | host_invalid_migration.csv |
| audit_diff_property_vms | diff_property_vm.csv |
| audit_nova_service_cleaned | nova_service_cleaned.csv |
| nova_idle_transaction | nova_idle_transactions.csv |
| audit_nova_vcpus | nova_quota_vcpus.csv |
| audit_nova_memory_mb | nova_quota_memory_mb.csv |
| audit_nova_quota_instance | nova_quota_instance.csv |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70101 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>租户ID：产生告警虚拟机所属租户 |
| 附加信息 | 事件ID：告警的事件ID<br>可用分区名：告警虚拟机的可用分区<br>租户名：告警虚拟机所属租户<br>虚拟机名：告警虚拟机的名称<br>主机名：告警虚拟机的主机名<br>主机ID：告警虚拟机的主机ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70102 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>租户ID：产生告警虚拟机所属租户 |
| 附加信息 | 可用分区名：告警虚拟机的可用分区<br>租户名：告警虚拟机所属租户<br>虚拟机名：告警虚拟机的名称<br>主机名：告警虚拟机的主机名<br>主机ID：告警虚拟机的主机ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70103 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机名：产生告警的主机名<br>集群名：产生告警主机所在集群名<br>域：产生告警主机所在域 |
| 附加信息 | 集群总vcpu数：告警主机所在集群vcpu总数<br>集群已经使用的vcpu数：告警主机所在集群已经使用的vcpu数<br>主机名：告警的主机名 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70104 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务名：产生告警的服务名 |
| 附加信息 | 集群总内存大小：告警主机所在集群总内存大小<br>集群已经分配的内存大小：告警主机所在集群已经分配的内存大小<br>主机名：告警主机名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70105 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>卷ID：产生告警的虚拟机使用的卷ID<br>bdm数据ID：产生告警的卷的bdm数据ID |
| 附加信息 | 详细信息：告警的详细信息<br>主机名：产生告警的主机名称<br>主机ID：产生告警的主机ID<br>可用分区：产生告警的可用分区 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70106 | 重要 | 由nova-api的配置项instance_rebuild_timeout_clear决定，默认为True，表示会自动清除。 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>租户ID：产生告警虚拟机所属租户ID |
| 附加信息 | 可用分区名：告警虚拟机所在可用分区的名称<br>虚拟机名：产生告警的虚拟机名称<br>主机名：告警虚拟机所在主机的名称<br>主机ID：告警虚拟机所在主机的ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70108 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 详细信息：告警的详细信息<br>主机名：告警所在主机的名称<br>主机ID：告警所在主机的ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70109 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务名<br>服务：产生告警的服务名 |
| 附加信息 | 详细信息：告警的详细信息 |


| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| audit_invalid_bdms | invalid_bdms.csv |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70111 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>pci地址：本地盘pci地址 |
| 附加信息 | 主机ID：产生告警的主机ID<br>本地盘：产生告警的本地盘<br>虚拟机id：产生告警的虚拟机id<br>主机名：产生告警的主机名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70112 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>vendorID：产生告警的网卡供应商编码，默认1b53<br>productID：产生告警的网卡产品编码，默认1014 |
| 附加信息 | 服务名：产生告警的服务名称，默认为neutron<br>微服务名：产生告警的微服务名称，默认为VPC<br>虚拟机ID：产生告警的虚拟机ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70113 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>pci地址：产生告警的SSD卡的pci地址<br>异常类型：告警的异常类型 |
| 附加信息 | 主机ID：告警虚拟机所在主机ID<br>本地NvmeSSD盘：告警虚拟机使用的NvmeSSD盘<br>虚拟机id：产生告警的虚拟机ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70126 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 主机ID：告警虚拟机所在主机ID<br>虚拟机在vmware上的名称：告警虚拟机在vmware上的名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70127 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：NVMe SSD大盘所在主机组ID<br>SSD卡类型：NVMe SSD大盘类型 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>总量：NVMe SSD大盘总量<br>使用量：NVMe SSD大盘使用量<br>阈值：NVMe SSD大盘阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70128 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：NVMe SSD小盘所在主机组ID<br>SSD卡类型：产生告警的SSD卡类型 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>总量：NVMe SSD小盘总量<br>使用量：NVMe SSD小盘使用量<br>阈值：NVMe SSD小盘阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70129 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：gpu所在主机组ID<br>gpu类型：gpu类型 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机组名称：gpu所在的主机组名称<br>gpu总量：gpu的总个数<br>gpu使用量：已使用的gpu个数<br>阈值：gpu个数阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70130 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>租户ID：告警虚拟机所在租户ID |
| 附加信息 | 可用分区名：告警虚拟机所在可用分区名称<br>虚拟机名：告警虚拟机的名称<br>主机名：告警虚拟机所在主机名称<br>主机ID：告警虚拟机所在主机ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70131 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：产生告警的主机组ID |
| 附加信息 | 云服务：产生告警的云服务名称<br>服务：产生告警的服务名称<br>主机组名称：产生告警的主机组名称<br>cpu总量：产生告警的主机组cpu总量<br>cpu使用量：产生告警的主机组cpu使用量<br>cpu复用比：产生告警的主机组cpu复用比<br>cpu门限量：产生告警的主机组cpu门限量 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70132 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：产生告警的主机组ID |
| 附加信息 | 云服务：产生告警的云服务名称<br>服务：产生告警的服务名称<br>主机组名称：产生告警的主机组名称<br>内存总量：产生告警的主机组内存总量<br>内存使用量：产生告警的主机组内存使用量<br>内存复用比：产生告警的主机组内存复用比<br>内存门限量：产生告警的主机组内存门限量 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70135 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机 ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>复用内存总量：主机的复用内存总量<br>复用内存已使用量：主机的复用内存已使用量<br>内存复用率：主机的内存复用率<br>阈值：主机的内存复用告警阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70201 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>云服务：服务类型，固定值为VPC |
| 附加信息 | 异常信息：Networks Exceeds Limitation on a DHCP agent |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70203 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。<br>端口信息：产生告警的组件名/触发动作/端口类型。 |
| 附加信息 | 端口列表：产生告警的虚拟端口列表。<br>失败原因：Virtual Interfaces Fail to Be Brought Online，表示虚拟端口上线失败。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70251 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>聚合网口名：产生告警的bond名称 |
| 附加信息 | 详细信息：<br>聚合网口名：产生告警的bond名称<br>主机ID：产生告警的主机ID<br>主机名：产生告警的主机名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70300 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务名：产生告警的服务名 |
| 附加信息 | 详细信息： 卷审计产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70310 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务名：产生告警的服务名 |
| 附加信息 | 详细信息： 快照审计产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70400 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 云服务：产生告警的云服务<br>详细信息：告警的详细信息 |


| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| audit_stucking_images | stucking_images.csv |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70401 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 镜像uuid：产生告警的镜像uuid |
| 附加信息 | 镜像名称：产生告警的镜像名称<br>证书uuid：镜像对应的证书uuid<br>证书名称：镜像对应的证书名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70402 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 云服务：产生告警的云服务<br>详细信息：告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73008 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>审计名称：<br>info-collect-server：产生告警的审计项为dhcp名空间资源冗余<br>neutron：产生告警的审计项为存在野port资源<br>audit_neutron_port：产生告警的审计项为port资源冗余 |
| 附加信息 | 详细信息：<br>redundant_dhcpns_count xxx：产生告警的冗余dhcp名空间个数<br>Wild_ports xxx：产生告警的野port<br>stale_ports xxx：产生告警的冗余port |


| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| stale_ports | stale_ports.csv |
| redundant_dhcp_namespaces（且结果不为0） | redundant_namespaces.csv |
| redundant_router_namespaces（且结果不为0） | redundant_namespaces.csv |
| wild_ports | neutron_wild_ports.csv |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73010 | 重要 | 系统分区重启后可自动清除，其余需要手动清除。 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>磁盘：异常文件的名称。 |
| 附加信息 | 异常信息：告警异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73011 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>异常进程名称：异常进程的名称。 |
| 附加信息 | 异常信息：告警异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73012 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>异常磁盘分区目录：异常磁盘名称。 |
| 附加信息 | 异常信息：告警的异常信息。<br>说明： <br>disk partition=%s：表示为磁盘分区挂载目录，如/var/log等。<br>inode usage=%d：表示为磁盘当前inode使用率。<br>alarm: %d%：表示告警阈值，值为90%。<br>resume: %d%：表示恢复阈值，值为80%。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73013 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>磁盘名称：异常磁盘的名称。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73014 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73015 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 异常信息：大页内存不足。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73016 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID ：产生告警的主机ID。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73017 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>用户名：fsp。 |
| 附加信息 | 详细信息：The host OS password will expire。<br>密码过期时间：所有主机密码中最近的过期时间。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73018 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。<br>磁盘：异常磁盘的名称。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73019 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73102 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73104 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73107 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID。 |
| 附加信息 | 异常信息：产生告警的异常信息。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名称。<br>虚拟机名：产生告警的虚拟机名称。<br>主机IP：产生告警的主机IP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73108 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：异常虚拟机的UUID。 |
| 附加信息 | 异常信息：表示虚拟机异常的具体信息，包括虚拟机名称和uuid。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>虚拟机名：异常虚拟机在UVP层的名称。<br>主机IP：产生告警的主机IP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73109 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73110 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73111 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73112 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的虚拟机所在主机ID<br>虚拟机网卡：产生告警的虚拟机网卡名称 |
| 附加信息 | 异常信息：产生告警的虚拟机网卡异常信息<br>主机 ID：产生告警的虚拟机所在主机ID<br>主机名：产生告警的虚拟机所在主机名称<br>主机IP：产生告警的虚拟机所在主机IP |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73201 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务故障：HAProxy Backend Services Fault<br>云服务：服务类型，固定值为VPC |
| 附加信息 | 服务名：产生告警的服务名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73203 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 详细信息：<br>component fault：组件故障 。<br>host：产生告警的主机ID。<br>components：产生告警的组件名称。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73204 | 重要 | 不同场景不同，删除节点场景需要手动清除 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 产生告警的主机ID。 |
| 附加信息 | 第一个参数：<br>alarmInfo：告警详情。<br>hostosversion：产生告警的主机的os版本。<br>hosttimetag：产生告警的主机时间戳。<br>sysosversion：产生告警的主机上系统的os版本。<br>systimetag：产生告警的主机上系统的时间戳。<br>第二个参数：产生告警的主机ID。<br>第三个参数：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73205 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机名：产生告警的主机名称。 |
| 附加信息 | 第一个参数：产生告警的主机ID。<br>第二个参数：产生告警的主机名。<br>第三个参数：资源配置不一样故障详情。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73207 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>重复主机ID列表：重复的主机名列表。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73208 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 本端地址：告警发送节点的地址。<br>对端地址：产生告警的主机地址。<br>错误信息：告警相关的错误信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73209 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的服务名称。 |
| 附加信息 | 故障zookeeper的ip列表：产生告警的zookeeper的ip列表。<br>详细信息：<br>XX has diff data with leader XX : zookeeper的数据不一致。<br>XX has diff path with leader XX ：zookeeper的路径数据不一致。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73210 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。 |
| 附加信息 | 云服务：产生告警的云服务。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73301 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>对端地址：vCenter服务器的IP地址 |
| 附加信息 | 详细信息：Failed to connect to VMware vCenter Server，表示连接vCenter服务器失败 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73302 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 物理网络平面：产生告警的物理网络平面<br>云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 异常信息：告警的异常信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73303 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>对端地址：VRM服务器的IP地址 |
| 附加信息 | 详细信息：Failed to connect to VMware vCenter Server，表示连接VRM服务器失败 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73401 | 紧急/重要 | 紧急可自动清除，重要需要手动清除 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>实例名：产生告警的服务所在的实例名。 |
| 附加信息 | 主机名：产生告警的主机名。<br>主机ID：产生告警的主机ID。<br>详情：告警的详细信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73403 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。 |
| 附加信息 | 详细信息：sync_abnormal<br>本端地址：本端同步IP地址<br>对端地址：对端同步IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73404 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的服务。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 详细信息：告警的详细信息。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73405 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。 |
| 附加信息 | 详细信息：告警的详细信息。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>阈值：连接数的使用阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73410 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73411 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 主机名：产生告警的主机名。<br>详情：告警的详细信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73412 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 详细信息：告警的详细信息。<br>主机名：产生告警的主机名。<br>云服务：产生告警的云服务。<br>微服务：产生告警的组件名称 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060047 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务名<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称<br>阈值：存储虚拟容量使用率阈值<br>使用率：产生告警的后端存储的实际虚拟容量使用率 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060049 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务名<br>虚拟机名：产生告警的虚拟机名<br>被挂载的双活目标LUN：产生告警的双活目标LUN ID |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060050 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>可用分区：产生告警的后端存储所属可用分区<br>后端存储管理地址：产生告警的后端存储设备管理地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101315 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：产生告警的主机组ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机组名称：产生告警的主机组名称<br>总量：主机组内本地直通盘的总量<br>使用量：主机组内本地直通盘使用数量<br>阈值：主机组内本地直通盘的告警阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101320 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：npu所在主机组ID<br>npu类型：npu类型 |
| 附加信息 | 服务：产生告警的服务<br>微服务：产生告警的微服务<br>主机组名称：npu所在的主机组名称<br>npu总量：npu的总个数<br>npu使用量：已使用的npu个数<br>阈值：npu个数阈值 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101321 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 可用分区名：产生告警的可用分区名称<br>主机名：产生告警的虚拟机所在主机名称<br>主机ID：产生告警的虚拟机所在主机ID<br>虚拟机名：产生告警的虚拟机名称<br>LUN_WWN：未完全恢复的存储磁盘WWN编号 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1126000 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 裸金属服务器节点ID：产生告警的裸金属服务器节点ID |
| 附加信息 | 主机IP：产生告警的主机IP<br>裸金属服务器BMC地址：产生告警的裸金属服务器BMC IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1126001 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 裸金属服务器节点ID：产生告警的裸金属服务器节点ID |
| 附加信息 | 主机IP：产生告警的主机IP<br>裸金属服务器BMC地址：产生告警的裸金属服务器BMC IP地址<br>当前管理状态：产生告警的裸金属服务器节点的管理状态 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1126002 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务名称，默认为OpenStack<br>服务：产生告警的服务名称，默认为ironic |
| 附加信息 | 云服务：产生告警的微服务名称，默认为ironic<br>详细信息：裸金属服务器审计产生告警的详细信息 |


| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| invalid_ironic_instances | invalid_ironic_instances.csv |
| invalid_ironic_nodes | invalid_ironic_nodes.csv |
| stucking_ironic_instances | stucking_ironic_instances.csv |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200067 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 区域名：产生告警的区域。<br>云服务：服务类型，固定值为VPC。<br>服务：产生告警的服务名称，默认为DHCP。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 云服务：服务类型，固定值为VPC。<br>服务：产生告警的服务名称，默认为DHCP。<br>主机名：产生告警的主机名称。<br>异常信息：产生告警的原因。<br>分区名：产生告警的分区。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200075 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>端口名称：产生告警的trunk名称 |
| 附加信息 | 主机名：产生告警的主机名称<br>错包率：产生告警的错包率<br>阈值：产生告警的阈值<br>每秒包数：产生告警时的每秒收发包数 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200076 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>端口名称：产生告警的端口名称 |
| 附加信息 | 主机名：产生告警的主机名称<br>丢包率：产生告警的丢包率<br>阈值：产生告警的阈值<br>每秒包数：产生告警时的每秒收发包数 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200077 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223017 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>负载均衡器ID：弹性负载均衡的ID。 |
| 附加信息 | 弹性负载均衡器名称：弹性负载均衡的名称。<br>租户ID：项目的ID。<br>离线后端实例：健康检查为离线的后端服务器的信息，包括名称、IP地址。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1240001 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 云服务：产生告警的云服务<br>主机ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>本端地址：产生告警的主机地址信息<br>对端地址：产生告警的主机对接的Agile Controller-DCN的地址信息<br>详细信息：产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1240002 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>主机名：产生告警的主机名称<br>本端地址：产生告警的主机地址信息<br>对端地址：产生告警的主机对接的Agile Controller-DCN的地址信息<br>详细信息：产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1240003 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>主机名：产生告警的主机名称<br>本端地址：产生告警的主机地址信息<br>对端地址：产生告警的主机对接的Agile Controller-DCN的地址信息<br>详细信息：产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1301021 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316000 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。 |
| 附加信息 | 云服务：产生告警的云服务。<br>错误信息：告警相关的错误信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316001 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机名：产生告警的主机名。<br>错误信息：告警相关的错误信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316002 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生该告警的主机ID。 |
| 附加信息 | 服务名：产生该告警的服务名。<br>微服务名：产生该告警的微服务名。<br>主机名：产生该告警的主机名。<br>错误信息：该告警相关的错误信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316003 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生该告警的主机ID。<br>虚拟机ID：产生该告警的虚拟机ID。 |
| 附加信息 | 服务名：产生该告警的服务名。<br>微服务名：产生该告警的微服务名。<br>主机名：产生该告警的主机名。<br>虚拟机名：产生该告警的虚拟机名。<br>错误信息：该告警相关的错误信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1507002 | 紧急/重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>实例名：产生告警的服务所在的实例名。 |
| 附加信息 | 告警信息：告警的类型及具体的参数。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>云服务：产生告警的云服务。<br>微服务：产生告警的微服务名称。<br>阈值：资源使用率阈值。 |


| 表1 RabbitMQ template名称与分库情况对应关系 | 表1 RabbitMQ template名称与分库情况对应关系 |
| --- | --- |
| RabbitMQ分库情况 | RabbitMQ template名称 |
| 未分库 | rabbitmq |
| nova分库 | rabbitmq_nova |
| neutron分库 | rabbitmq_neutron |


| 表2 RabbitMQ参数配置规格 | 表2 RabbitMQ参数配置规格 | 表2 RabbitMQ参数配置规格 |
| --- | --- | --- |
| 规模 | 内存水位线（GB） | 磁盘分区大小（GB） |
| 100PM/1000VM | 32 | 16 |
| 256PM/2000VM | 32 | 16 |
| 512PM/5000VM | 50 | 25 |
| 1024PM/10000VM | 50 | 25 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9002 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>SNMP管理站ID：SNMP管理站的ID |
| 附加信息 | SNMP管理站名称：产生告警的SNMP管理站名称<br>本端地址：ServiceOM的浮动IP地址<br>对端地址：SNMP管理站中配置的IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9201 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM的浮动IP地址<br>对端地址：上级时间服务器的地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9203 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 阈值：固定为“1 minute” |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9204 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM的浮动IP地址<br>对端地址：上级时间服务器的地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9206 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | FTP地址：FTP的地址，IP或者域名。<br>错误码：FTP备份或者上传文件失败的错误码<br>错误信息：错误码对应的错误描述信息 |


| 序号 | 错误码 | 错误描述 | 处理方案 |
| --- | --- | --- | --- |
| 1 | 10409101 | 本地自动备份失败，原因为内部错误。 | 执行处理故障8 |
| 2 | 10409106 | 本地自动备份失败，原因为数据库状态异常。 | 执行处理故障1 |
| 3 | 10409108 | 本地自动备份失败，原因为本地主机备份空间不足。 | 执行处理故障2 |
| 4 | 10409109 | 本地自动备份失败，原因为本地主机备份文件序号超过上限。 | 执行处理故障3 |
| 5 | 10409301 | 上传备份文件到第三方服务器失败，内部错误。 | 执行处理故障8 |
| 6 | 10409309 | 上传备份文件到第三方服务器失败，原因为本地主机备份文件序号超过上限。 | 执行处理故障4 |
| 8 | 10409311 | 上传备份文件到第三方服务器失败，原因为无法连接第三方服务器。 | 执行处理故障6 |
| 9 | 10409316 | 上传备份文件到第三方服务器失败，原因为本地主机的备份文件数超过上限。 | 执行处理故障7 |
| 10 | 10409327 | 备份操作超时。 | 执行处理故障8 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9207 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 过期时间：License文件的过期时间 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9208 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 过期时间：License的过期时间 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9209 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 资源类型：License界面显示的资源使用值大于License授权许可值的License的英文授权名称列表 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9210 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 错误原因：License失效的错误原因 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9215 | 紧急/重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM的浮动IP地址 |
| 附加信息 | 阈值：重要告警阈值为70%，紧急告警阈值为90%<br>使用量：超过阈值的磁盘分区和磁盘分区对应的使用率信息列表 |


| 表1 分区占满的影响 | 表1 分区占满的影响 |
| --- | --- |
| 分区名称 | 分区占满的影响 |
| /opt/goku/data/db | 数据库无法正常运行，Service OM不能正常提供服务 |
| /opt/goku/data | Service OM业务不能正常运行 |
| 其他 | OS和Service OM不能正常运行 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9216 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 过期时间：License软件订阅与保障年费的过期时间 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9217 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 过期时间：License软件订阅与保障年费的过期时间 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9226 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 浮动IP：ServiceOM虚拟机的浮动IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9801 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM虚拟机主节点的IP地址 |
| 附加信息 | 组件：异常组件信息列表<br>节点名称：ServiceOM虚拟机主节点名称 |


| 表1 资源异常影响 | 表1 资源异常影响 |
| --- | --- |
| 资源名 | 资源异常影响 |
| irm | 资源管理将不可用。 |
| uportal | Service OM界面无法登录，所有的操作界面都会异常。<br>北向接口不可用。 |
| primarydb | Service OM的操作界面所有操作可能无法直接生效。 |
| csm | 使用RPC服务的模块会受到影响，如：irm、connector等。<br>软件调测功能、License管理功能、时间管理功能不可用，后台自动定时备份将无法执行。 |
| uhm | 硬件相关的资源管理不可用。 |
| exfloatip | Service OM所有操作将无法进行。 |
| omfloatip | SNMP告警将无法提供。 |
| standbydb | Service OM的操作界面所有操作可能无法同步到备机，当主Service OM节点掉电可能导致数据丢失。 |
| arcontrol | Service OM资源管理不可用。 |
| ardata | 报表和监控将不可用。 |
| nginx | 整个系统将不可用。 |
| connector | Service OM资源池管理不可用。 |
| orchestrator | Service OM部分资源不可用。 |
| fault | 告警功能不可用。 |
| HA | Service OM主备节点数据库及文件系统无法保证同步。<br>当Service OM主节点故障时，系统可能无法倒换到备节点。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9803 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>对端地址：异常组件的地址信息 |
| 附加信息 | 部件名称：异常组件的连接器名称<br>连接类型：异常组件的连接器类型<br>本端地址：ServiceOM的浮动IP地址<br>对端地址：异常组件的地址信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9901 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 参数：Service OM双机倒换。 |
| 附加信息 | 原主机：原主节点虚拟机名称。<br>原备机：原备节点虚拟机名称。<br>原主机IP：原主节点虚拟机IP。<br>原备机IP：原备节点虚拟机IP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9902 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM主节点IP地址<br>对端地址：ServiceOM备节点IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9903 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM主节点IP地址<br>对端地址：ServiceOM备节点IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9911 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>对端地址：License Server的IP地址 |
| 附加信息 | 本端地址：ServiceOM的浮动IP地址<br>对端地址：License Server的IP地址 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9912 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 部件名称：对接的部件信息列表 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9913 | 提示 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>证书类型：默认证书的类型 |
| 附加信息 | 无 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9915 | 紧急/重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>证书类型：默认证书的类型 |
| 附加信息 | 开始时间：证书开始时间<br>结束时间：证书结束时间<br>详细信息：证书即将过期或者还未生效或者已经过期的时间说明 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9916 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>服务：固定为omm-server<br>POD：OMM-Server所在的POD |
| 附加信息 | 云服务：固定为ServiceOM<br>故障类型：OMM-Server的故障类型<br>详细信息：OMM-Server故障的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9917 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM虚拟机的固定IP地址 |
| 附加信息 | 云服务：固定为ServiceOM<br>阈值：告警上报阈值<br>使用率：CPU使用率 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9918 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM虚拟机的固定IP地址 |
| 附加信息 | 云服务：固定为ServiceOM<br>阈值：告警上报阈值<br>使用率：内存使用率 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100010002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100010003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100010004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100020003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100020004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000100020005 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 网络设备 | 设备IP | 网络设备的IP地址。 |
| 网络设备 | 设备名称 | 网络设备的名称。 |
| 网络设备 | 租户名称 | 网络设备的租户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200010002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200010003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200010004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020005 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020006 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020007 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020008 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000200020009 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000100020002000A | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000100020002000B | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000100020002000C | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000100020002000D | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000100020002000E | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 接口 | 接口IP | 接口的IP地址。 |
| 接口 | 网络设备 | 网络设备名称。 |
| 接口 | 接口名称 | 接口的名称。 |
| 接口 | 父资源名称 | 父资源名称。 |
| 接口 | 接口别名 | 接口的别名。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000300010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性负载均衡 | ID | ID |
| 弹性负载均衡 | 负载均衡器名称 | 弹性负载均衡的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000300020003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性负载均衡 | ID | ID |
| 弹性负载均衡 | 负载均衡器名称 | 弹性负载均衡的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000300020004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性负载均衡 | ID | ID |
| 弹性负载均衡 | 负载均衡器名称 | 弹性负载均衡的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000400010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 单板 | 单板名称 | 单板的名称。 |
| 单板 | 网络设备 | 网络设备名称。 |
| 单板 | 父资源名称 | 父资源名称。 |
| 单板 | 单板描述 | 单板的描述信息。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000400010002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 单板 | 单板名称 | 单板的名称。 |
| 单板 | 网络设备 | 网络设备名称。 |
| 单板 | 父资源名称 | 父资源名称。 |
| 单板 | 单板描述 | 单板的描述信息。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000500010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 光口 | 光口IP | 光口的IP地址。 |
| 光口 | 光口名称 | 光口的名称。 |
| 光口 | 父资源名称 | 光口的父资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000500010002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 光口 | 光口IP | 光口的IP地址。 |
| 光口 | 光口名称 | 光口的名称。 |
| 光口 | 父资源名称 | 光口的父资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0003000200010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 电源 | 电源名称 | 电源的名称。 |
| 电源 | 服务器 | 服务器名称。 |
| 电源 | 父资源名称 | 电源的父资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000100010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 宿主机 | 虚拟化类型 | 宿主机的虚拟化类型。 |
| 宿主机 | 主机组 | 主机组名称。 |
| 宿主机 | 管理IP地址 | 宿主机的管理IP地址。 |
| 宿主机 | 宿主机名称 | 宿主机的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000100020008 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 宿主机 | 虚拟化类型 | 宿主机的虚拟化类型。 |
| 宿主机 | 主机组 | 主机组名称。 |
| 宿主机 | 管理IP地址 | 宿主机的管理IP地址。 |
| 宿主机 | 宿主机名称 | 宿主机的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000500010003000B | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 宿主机 | 虚拟化类型 | 宿主机的虚拟化类型。 |
| 宿主机 | 主机组 | 主机组名称。 |
| 宿主机 | 管理IP地址 | 宿主机的管理IP地址。 |
| 宿主机 | 宿主机名称 | 宿主机的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000500010003000C | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 宿主机 | 虚拟化类型 | 宿主机的虚拟化类型。 |
| 宿主机 | 主机组 | 主机组名称。 |
| 宿主机 | 管理IP地址 | 宿主机的管理IP地址。 |
| 宿主机 | 宿主机名称 | 宿主机的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000100040014 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 宿主机 | 虚拟化类型 | 宿主机的虚拟化类型。 |
| 宿主机 | 主机组 | 主机组名称。 |
| 宿主机 | 管理IP地址 | 宿主机的管理IP地址。 |
| 宿主机 | 宿主机名称 | 宿主机的名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000200010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性云服务器 | 虚拟化类型 | 弹性云服务器的虚拟化类型。 |
| 弹性云服务器 | 宿主机 | 宿主机名称。 |
| 弹性云服务器 | 名称 | 弹性云服务器的名称。 |
| 弹性云服务器 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000200020003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性云服务器 | 虚拟化类型 | 弹性云服务器的虚拟化类型。 |
| 弹性云服务器 | 宿主机 | 宿主机名称。 |
| 弹性云服务器 | 名称 | 弹性云服务器的名称。 |
| 弹性云服务器 | ID | ID。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000200040004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性云服务器 | 虚拟化类型 | 弹性云服务器的虚拟化类型。 |
| 弹性云服务器 | 宿主机 | 宿主机名称。 |
| 弹性云服务器 | 名称 | 弹性云服务器的名称。 |
| 弹性云服务器 | ID | ID。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000200010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 虚拟机 | 名称 | 虚拟机的名称。 |
| 虚拟机 | 宿主机 | 宿主机名称。 |
| 虚拟机 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000200020003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 虚拟机 | 名称 | 虚拟机的名称。 |
| 虚拟机 | 宿主机 | 宿主机名称。 |
| 虚拟机 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000200040004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 虚拟机 | 名称 | 虚拟机的名称。 |
| 虚拟机 | 宿主机 | 宿主机名称。 |
| 虚拟机 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000300010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 主机组 | 虚拟化类型 | 集群的虚拟化类型。 |
| 主机组 | 主机组名称 | 主机组的名称。 |
| 主机组 | 来源系统 | 集群的来源系统。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000300020007 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 主机组 | 虚拟化类型 | 集群的虚拟化类型。 |
| 主机组 | 主机组名称 | 主机组的名称。 |
| 主机组 | 来源系统 | 集群的来源系统。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000300030010 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 主机组 | 虚拟化类型 | 集群的虚拟化类型。 |
| 主机组 | 主机组名称 | 主机组的名称。 |
| 主机组 | 来源系统 | 集群的来源系统。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0005000300030011 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 主机组 | 虚拟化类型 | 集群的虚拟化类型。 |
| 主机组 | 主机组名称 | 主机组的名称。 |
| 主机组 | 来源系统 | 集群的来源系统。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010005 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010006 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010007 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010008 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010009 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000700010001000A | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000700010001000B | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000700010001000C | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000700010001000D | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000700010001000E | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000700010001000F | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0007000100010010 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 端口 | FC交换机端口名称 | FC交换机的端口名称。 |
| 端口 | FC交换机 | FC交换机名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0008000100010001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 大数据主机组 | 主机组名称 | 大数据主机组的名称。 |
| 大数据主机组 | 版本 | 大数据主机组的版本。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0008000100010002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 大数据主机组 | 主机组名称 | 大数据主机组的名称。 |
| 大数据主机组 | 版本 | 大数据主机组的版本。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0008000100010003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 大数据主机组 | 主机组名称 | 大数据主机组的名称。 |
| 大数据主机组 | 版本 | 大数据主机组的版本。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000500000001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性IP | 名称 | 弹性IP的资源名称。 |
| 弹性IP | IP地址 | 弹性IP的IP地址。 |
| 弹性IP | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000500000003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 弹性IP | 名称 | 弹性IP的资源名称。 |
| 弹性IP | IP地址 | 弹性IP的IP地址。 |
| 弹性IP | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000A00000001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 裸金属服务器 | 名称 | 裸金属服务器的资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000A0000000D | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 裸金属服务器 | 名称 | 裸金属服务器的资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000A00000014 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 裸金属服务器 | 名称 | 裸金属服务器的资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000001 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000002 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000200070000002A | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000036 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000800000003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| Oracle CDB关系型数据库 | 名称 | Oracle CDB关系型数据库的资源名称。 |
| Oracle CDB关系型数据库 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000800000004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| Oracle CDB关系型数据库 | 名称 | Oracle CDB关系型数据库的资源名称。 |
| Oracle CDB关系型数据库 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000900000003 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| Oracle PDB关系型数据库 | 名称 | Oracle PDB关系型数据库的资源名称。 |
| Oracle PDB关系型数据库 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000900000004 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| Oracle PDB关系型数据库 | 名称 | Oracle PDB关系型数据库的资源名称。 |
| Oracle PDB关系型数据库 | ID | ID |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000048 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000049 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0003000200010005 | 紧急/重要/次要/提示 | 业务质量告警 |


| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 电源 | 电源名称 | 电源的名称。 |
| 电源 | 服务器 | 服务器名称。 |
| 电源 | 父资源名称 | 电源的父资源名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000A000200000010 | 紧急/重要/次要/提示 | 业务质量告警 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000A000200000021 | 紧急/重要/次要/提示 | 业务质量告警 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000A00020000002D | 紧急/重要/次要/提示 | 业务质量告警 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 000A00020000004D | 紧急/重要/次要/提示 | 业务质量告警 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100502 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 系统连接性测试失败的云服务名称。 |
| 服务 | 系统连通性测试失败的服务名称。 |
| 产品类型 | 系统连通性测试失败的对接系统所属的产品类型。 |
| 系统名称 | 系统连通性测试失败的对接系统的名称。 |
| 对接地址 | 系统连通性测试失败的对接系统的IP地址。 |
| 产品 | 系统连通性测试失败的对接系统所属的产品。 |


| 表1 告警可能原因及处理操作 | 表1 告警可能原因及处理操作 |
| --- | --- |
| 告警可能原因 | 操作 |
| 对接系统IP地址或端口信息错误 | 请联系对接系统的系统管理员获取并重新填写正确的IP地址和端口信息。<br>请联系对接系统的系统管理员获取正确的IP地址和端口信息。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“系统类型”中单击告警详情页面定位信息中的系统名称。<br>选择“系统名称”，在搜索框中输入对接系统的系统名称，单击后显示匹配的对象。<br>说明： <br>也可选择“IP地址/URL/域名”、“部署区域”、“版本信息”和“厂商”，在搜索框中输入对应的值，单击后显示匹配的对象。<br>单击需要修改的对接系统所在列的，进入修改对接系统页面。<br>说明： <br>对于无法修改的对接系统，请联系技术工程师协助解决。<br>在“IP地址/域名”右侧输入框中输入正确的IP地址。<br>在“端口”右侧输入框中输入正确的端口信息。<br>单击“确定”。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行10。<br>请联系技术支持工程师协助解决。 |
| 对接系统参数错误，如用户名或密码等错误 | 请联系对接系统的系统管理员获取并重新填写正确的用户名和密码。<br>请联系对接系统的系统管理员获取正确的用户名和密码。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“系统类型”中单击告警详情页面定位信息中的系统名称。<br>选择“系统名称”，在搜索框中输入对接系统的系统名称，单击后显示匹配的对象。<br>说明： <br>也可选择“IP地址/URL/域名”、“部署区域”、“版本信息”和“厂商”，在搜索框中输入对应的值，单击后显示匹配的对象。<br>单击需要修改的对接系统所在列的，进入修改对接系统页面。<br>说明： <br>对于无法修改的对接系统，请联系技术工程师协助解决。<br>在“用户名”右侧输入框中输入正确的用户名。<br>在“密码”右侧输入框中输入正确的密码，单击“确定”。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行9。<br>请联系技术支持工程师协助解决。 |
| 网络故障 | 使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的节点IP地址。<br>sopuser用户的默认密码为“*****”。<br>执行如下命令，切换到ossadm帐号。<br>su - ossadm<br>ossadm用户的默认密码为“*****”。<br>执行如下命令，是否能ping通告警参数中的“对接地址”。<br>ping IP地址<br>是，表明SNMP对接系统故障，请参考“对接系统故障”中的1~10处理。<br>否，执行4。<br>请联系技术支持工程师协助解决。 |
| 对接系统故障 | 在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“系统类型”页面中单击告警详情页面的定位信息中的系统名称。<br>单击选择“系统名称”，在搜索框中输入对接系统的系统名称，单击后显示匹配的对象。<br>说明： <br>也可选择“IP地址/URL/域名”、“部署区域”、“版本信息”和“厂商”，在搜索框中输入对应的值，单击后显示匹配的对象。<br>单击对接系统“操作”列的进行连通性测试。<br>查看弹框中连通性测试返回结果。<br>对端系统接口返回500错误，则表明对接系统出现内部错误，执行7。<br>对接系统网络不通，参考网络故障中的1~3处理。<br>对接系统证书错误，执行6。<br>进入“证书管理 > 信任证书”页面，查看是否有该对接系统的证书信息<br>否，参考运维帮助中心的“系统运维 > 接入管理 > 创建对接系统”章节，根据实际对接的情况，在对应的对接系统的章节，获取上传证书的方法。<br>是，执行7。<br>查看证书的有效期，看证书是否已过期。<br>是，参考《华为云Stack 6.5.1 安全管理指南》的“证书管理”章节，根据实际对接情况，找到对应的证书替换章节替换对接系统证书。<br>否，执行10。<br>证书上传成功后，执行9。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行10。<br>请联系技术工程师协助解决。 |
| 驱动服务异常 | 在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在“驱动生命周期管理 > 驱动管理”中单击告警详情页面中定位信息中的系统名称。<br>查看该驱动实例的部署类型，如果是“inner”，即是ManageOne内置驱动，则执行4，如果是“outer”，则执行7，参考运维帮助中心的“系统运维 > 接入管理 > 简介”章节中的“驱动类型和对接系统关系表”。<br>查看服务状态。<br>使用PuTTY工具以sopuser用户分别登录regionAlias-ManageOne-Service01和regionAlias-ManageOne-Service02节点查看驱动服务状态。请在参考信息中查询节点对应的IP地址。<br>默认账号：sopuser。<br>sopuser用户的默认密码为“*****”。<br>执行以命令，切换到ossadm账号。<br>su - ossadm<br>ossadm用户的默认密码为“*****”。<br>参考表2查看驱动名称和驱动服务的对应关系，执行以下命令查看服务状态是否正常，此处以MOFSPPMDriverService微服务为例。<br>. /opt/oss/manager/bin/engr_profile.sh<br>/opt/oss/manager/agent/bin/ipmc_adm -cmd statusapp -app MOFSPPMDriverService<br>回显信息：<br>Process Name               Process Type           App Name    Tenant Name   ProcessMode  IP              PID     Status  <br>mofsppmdriverservice-3-0   mofsppmdriverservice  MOFSPPMDriverService  Product       cluster      **************  26037   STOPPED<br>RUNNING表示正常，其他表示异常。<br>是，执行7。<br>否，执行5<br>执行以下命令，重启微服务并查看服务状态是否正常，此处以MOFSPPMDriverService微服务为例。<br>重启微服务：<br>. /opt/oss/manager/bin/engr_profile.sh<br>/opt/oss/manager/agent/bin/ipmc_adm -cmd restartapp -app MOFSPPMDriverService<br>回显信息：<br>success<br>查看微服务状态是否正常：<br>/opt/oss/manager/agent/bin/ipmc_adm -cmd statusapp -app MOFSPPMDriverService<br>回显信息：<br>Process Name               Process Type           App Name    Tenant Name   ProcessMode  IP              PID     Status  <br>mofsppmdriverservice-3-0   mofsppmdriverservice  MOFSPPMDriverService  Product       cluster      **************  26037   RUNNING<br>是，执行6。<br>否，执行7。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行7。<br>请联系技术工程师协助解决。 |


| 表2 驱动服务与驱动名称的对应关系 | 表2 驱动服务与驱动名称的对应关系 |
| --- | --- |
| 驱动服务 | 驱动名称 |
| MOFSPPMDriverService | plugin_driver_hw_fusionsphereopenstack_pm |
| MOFSPFMDriverService | plugin_driver_hw_fusionsphereopenstack_fm |
| MOFSPRMDriverService | plugin_driver_hw_fusionsphereopenstack_rm |
| MOFSPAccessService | plugin_driver_hw_fusionsphereopenstack_access |
| MOCommonDriverService | plugin_driver_hw_cs |
| MOCommonDriverService | plugin_driver_hw_external |
| MOCommonDriverService | plugin_driver_hw_common |
| MOVRMDriverService | plugin_driver_hw_fusioncompute |
| MORCSAPService | RestConnectorService |
| MORCSAPService | SnmpConnectorService |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100553 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | SNMP认证失败的云服务名称。 |
| 服务 | SNMP连通性检测失败的服务名称。 |
| 对接系统类型 | SNMP连通性检测失败的对接系统类型。 |
| 系统名称 | SNMP连通性检测失败的对接系统的名称。 |
| 对接地址 | SNMP连通性检测失败的对接系统的IP地址或域名。 |


| 告警可能原因 | 操作 |
| --- | --- |
| SNMP对接的IP地址或端口信息错误 | 请联系对接系统的系统管理员获取并重新填写正确的IP地址和端口信息。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在左侧导航树中选择“驱动生命周期管理 > 配置管理”。<br>查看“本端IP地址”，根据部署区域信息单击对应对接系统的卡片查看“本端端口”，并重新填写正确的“本端IP地址”和“本端端口”。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行5<br>请联系技术工程师协助解决。 |
| SNMP对接参数信息错误，如用户名和密码等错误 | 请联系对接系统的系统管理员获取并重新填写正确的系统信息。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在左侧导航树中选择“驱动生命周期管理 > 配置管理”。<br>根据部署区域信息单击对应对接系统的卡片，在对应对接系统的“系统信息”右上方单击，并在页面中单击重新添加正确的系统信息。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行5<br>请联系技术工程师协助解决。 |
| 网络故障 | 使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP。<br>sopuser用户的默认密码为“*****”。<br>执行如下命令，切换到ossadm帐号。<br>su - ossadm<br>默认密码：*****<br>执行如下命令，是否能ping通对接系统的IP地址即告警参数中的“对接地址”。<br>ping IP地址<br>是，则表明SNMP对接系统故障，参考“SNMP对接系统故障”处理中的1~2处理。<br>否，请联系技术支持工程师协助解决。 |
| SNMP对接系统故障 | 根据告警定位信息的中系统名称和对接地址信息，按如下步骤排查对接系统的后台节点和ManageOne的后台节点时间是否一致。<br>使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP。<br>sopuser用户的默认密码为“*****”。<br>使用以下命令查询ManageOne系统时间。<br>date<br>回显信息：<br>Wed Aug 28 16:29:24 CST 2019<br>获取对接系统的节点IP地址。<br>对接系统的节点IP地址：生产中心安装时导出的参数信息汇总文件《xxx_export_all_CN.xlsm》的“2.1 工具生成的IP参数 ”页签中的查找对应的对接系统的后台节点IP地址，如eSight：ManageOne-eSight-01-Ip、ManageOne-eSight-02-Ip。<br>使用PuTTY工具以sopuser用户通过1.c中的获取到的IP地址登录对接系统的后台节点。<br>sopuser用户的默认密码为“*****”。<br>使用以下命令查询系统时间。<br>date<br>回显信息：<br>Wed Aug 28 16:29:24 CST 2019<br>对接系统和ManageOne的系统时间一致，执行2。<br>对接系统和ManageOne的系统时间不一致，执行1.f。<br>请联系技术工程师协助解决。<br>排查LVS是否故障。<br>按照如下步骤排查LVS是否异常。<br>使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01/Service02的IP地址分别登录service01和service02节点。请在参考信息中查询节点对应的IP。<br>sopuser用户的默认密码为“*****”。<br>执行以下命令切换到root用户<br>sudo su root<br>root用户的默认密码为“*****”。<br>执行以下命令查看LVS转发状态。<br>/opt/envs/RCLVSService/lvs_install/bin/ipvsadm -ln --stats<br>主节点回显信息：<br>IP Virtual Server version 1.2.1 (size=4096)<br>Prot LocalAddress:Port               Conns   InPkts  OutPkts  InBytes OutBytes<br>  -> RemoteAddress:Port<br>TCP  **************:27337               52    28479        0 17457284        0<br>  -> *************:27337                26    16714        0 10657454        0<br>  -> *************:27337                26    11766        0  6799882        0<br>TCP  **************:27400                0        0        0        0        0<br>  -> **************:27400                0        0        0        0        0<br>  -> **************:27400                0        0        0        0        0<br>TCP  **************:27402                0        0        0        0        0<br>  -> **************:27402                0        0        0        0        0<br>  -> **************:27402                0        0        0        0        0<br>UDP  **************:27367                1       64        0    21876        0<br>  -> *************:27367                 0       61        0    19398        0<br>  -> *************:27367                 1        3        0     2478        0<br>UDP  **************:27401                0        0        0        0        0<br>  -> **************:27401                0        0        0        0        0<br>  -> **************:27401                0        0        0        0        0<br>备节点回显信息：<br>IP Virtual Server version 1.2.1 (size=4096)<br>Prot LocalAddress:Port               Conns   InPkts  OutPkts  InBytes OutBytes<br>主节点、备节点回显信息如上所示不一致则表示LVS转发状态正常，主节点、备节点回显信息一致则表示LVS转发状态异常。<br>LVS转发状态正常，执行2.e。<br>LVS转发状态异常，执行2.d。<br>执行以下命令重启进程。<br>/opt/envs/RCLVSService/lvs_install/script/mgr_keepalived.sh restart<br>回显信息：<br>Shutting down Keepalived for LVS:                          [  OK  ]<br>Starting Keepalived for LVS:                               [  OK  ]<br>重启进程成功后，单击对接系统“操作”列的进行连通性测试。<br>连通性测试失败，执行2.e。<br>连通性测试成功，结束。<br>请联系技术工程师协助解决。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100550 | 紧急 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 发生告警的云服务名称。 |
| 服务 | 发生告警的服务名称。 |
| 节点名称 | 发生告警的节点名称。 |
| IP地址 | 发生告警的服务所在服务器的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100551 | 紧急 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 发生告警的云服务名称。 |
| 服务 | 发生告警的服务名称。 |
| 节点名称 | 发生告警的服务名称 |
| IP地址 | 发生告警的服务所在服务器的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1001 | 提示/次要/重要/紧急 | 业务质量告警 |


| 容量指标 | 紧急阈值(%) |
| --- | --- |
| vCPU分配率 | 90 |


| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 集群 | 上报的告警对应的集群名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1002 | 提示/次要/重要/紧急 | 业务质量告警 |


| 容量指标 | 紧急阈值(%) |
| --- | --- |
| vMemory分配率 | 90 |


| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 集群 | 上报的告警对应的集群名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1003 | 提示/次要/重要/紧急 | 业务质量告警 |


| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 存储使用率 | 90 |


| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 存储类型 | 上报的告警对应的存储类型。 |
| 存储池 | 上报的告警对应的存储池名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1004 | 提示/次要/重要/紧急 | 业务质量告警 |


| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 存储池分配率 | 90 |


| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 存储类型 | 上报的告警对应的存储类型。 |
| 存储池 | 上报的告警对应的存储池名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1005 | 提示/次要/重要/紧急 | 业务质量告警 |


| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 弹性IP使用率 | 90 |


| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 用途 | 上报此告警对应的弹性IP的用途。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1006 | 提示/次要/重要/紧急 | 业务质量告警 |


| 容量指标 | 紧急阈值(%) |
| --- | --- |
| 数据存储使用率 | 90 |


| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 数据存储类型 | 上报的告警对应的数据存储类型。 |
| 数据存储 | 上报的告警对应的数据存储名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 157 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警阈值 | 用户在配置“当前告警阈值提示”中，设置的该告警的产生阈值。<br>该参数显示在“定位信息”中。 |
| 当前占用量 | 当前告警的数量与总容量的占比。<br>该参数显示在“定位信息”中。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 832 | 重要 | 时间域告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 规则ID | 告警匹配的汇聚规则ID。<br>该参数显示在“定位信息”中。 |
| 源告警信息 | 产生汇聚告警的源告警信息，包含源告警的名称、源告警的告警源名称和汇聚规则中设置的关键参数。<br>该参数显示在“定位信息”中。 |
| 汇聚次数 | 上报源告警的次数。<br>该参数显示在“附加信息”中。 |
| IP地址 | 源告警的IP地址。<br>该参数显示在“附加信息”中。 |
| 保留字段 | 源告警的告警参数，当存在多个源告警参数时以“&”符号分隔。<br>该参数的值在汇聚规则的“定位信息保留字段”中设置，如果未设置则不显示该参数。<br>该参数显示在“附加信息”中。 |
| 汇聚类型 | 取值为告警，表示该同类告警数量超出门限告警是由源告警汇聚触发。<br>该参数显示在“附加信息”中。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 128 | 紧急 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP | 主LDAP服务器IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 160 | 紧急 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP | 备LDAP服务器IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 30004 | 重要 | 时间域告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 用户名 | 密码即将过期的用户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 30005 | 紧急 | 时间域告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 用户名 | 密码过期的用户名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 505001106 | 紧急 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 用户名 | 登录失败的用户名称。 |
| 终端 | 登录失败的IP地址。 |
| 限定时间段长度 | 限定时间段内连续输入错误密码次数达到最大值产生该告警。例如，参数“限定时间段长度”为10分钟，“限定时间段内连续输入错误密码次数”为5，表示10分钟内连续输入错误密码5次产生该告警。 |
| 限定时间段内连续输入错误密码次数 | 限定时间段内连续输入错误密码次数达到最大值产生该告警。例如，参数“限定时间段长度”为10分钟，“限定时间段内连续输入错误密码次数”为5，表示10分钟内连续输入错误密码5次产生该告警。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 126 | 重要 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主服务器设备IP | 日志转发主服务器IP地址。 |
| 主服务器端口号 | 日志转发主服务器端口。 |
| 备服务器设备IP | 日志转发备服务器IP地址。 |
| 备服务器端口号 | 日志转发备服务器端口。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 127 | 重要 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主服务器设备IP | 日志转发主服务器IP地址。 |
| 主服务器端口号 | 日志转发主服务器端口。 |
| 备服务器设备IP | 日志转发备服务器IP地址。 |
| 备服务器端口号 | 日志转发备服务器端口。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999992 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| LSN | License序列号。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999993 | 紧急 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| LSN | License序列号。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999995 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| LSN | License序列号。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999989 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 产品名称 | 产品名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999990 | 紧急 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 产品名称 | 产品名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999994 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| RESOURCE | 资源项ID。 |
| 阈值 | 资源项的容量阈值。 |
| 容量 | License限定资源项可使用的最大数量。 |
| 使用量 | 当前已消耗的资源数量。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999996 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| SALE | 销售项ID。 |
| 阈值 | 销售项的容量阈值。 |
| 容量 | License限定销售项可使用的最大数量。 |
| 使用量 | 当前已消耗的销售项数量。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999997 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| RESOURCE | 资源项ID。 |
| 阈值 | 资源项的容量阈值，此处阈值=100%。 |
| 容量 | License限定资源项可使用的最大数量。 |
| 使用量 | 当前已消耗的资源数量。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999998 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| SALE | 销售项ID。 |
| 阈值 | 销售项的容量阈值，此处阈值=100%。 |
| 容量 | License限定销售项可使用的最大数量。 |
| 使用量 | 当前已消耗的销售项数量。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999999 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 产品名称 | 产品名称。 |
| SALE | 销售项ID。 |
| RESOURCE | 资源项ID。 |
| FUCTION | 功能项ID。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 505001111 | 重要 | 通信告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 通知方式 | 发送远程通知的方式。 |
| 通知内容 | 发送失败的远程通知内容。 |
| 通知失败接收用户 | 发送远程通知失败的接收用户，取部分用户并做匿名化处理。 |


| 短消息发送方式 | 测试方法 | 测试成功 | 连接失败 |
| --- | --- | --- | --- |
| 短信网关 | 在左侧导航树中选择“短消息设置 > 短信网关设置”、“短消息设置 > 协议参数设置”，确认短信网关设置和协议参数设置是否配置正确，并根据步骤5获取附加信息的短消息接收人，在短信网关设置页面设置接收短消息号码，单击“测试”，测试连接短消息服务器是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
| SMN | 在左侧导航树中选择“SMN设置”，查看SMN设置是否正常，并根据步骤5获取附加信息的短消息接收人，设置接收短消息号码，单击“测试”，测试连接SMN服务器是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |
| 短信猫 | 在左侧导航树中选择“短信猫设置”，并根据步骤5获取附加信息的短消息接收人，设置接收短消息号码，单击“测试”，测试连接短信猫是否正常。 | 操作结束 | 收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100100 | 紧急/重要 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测操作系统帐号所在节点的IP地址。 |
| 帐号名称 | 被检测的操作系统帐号名称。 |
| 帐号密码即将过期天数 | 被检测帐号密码即将过期的天数。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100103 | 紧急/重要 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测证书所在节点的IP地址。 |
| 证书服务名称 | 被检测的证书的服务名称。 |
| 证书即将过期天数 | 被检测的证书即将过期的天数。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100106 | 紧急/重要 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测证书所在节点的IP地址。 |
| 证书服务名称 | 被检测的证书服务名称。 |
| 证书即将过期天数 | 被检测的证书即将过期的天数。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_agent_heartbeat | 次要 | 通信告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 被监控节点IP | 通讯异常的被监控节点的IP地址。 |
| 监控节点IP | MOICAgentMgmtService服务的节点IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_heartbeat | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务名称 | 产生告警的服务名称。 |
| 组件名称 | 产生告警的IP地址或虚拟机名称。 |
| 组件类型 | 产生告警的实例类型。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_cpu.percent | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_memory.percent | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.rx_dropped_ps | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.rx_errors_ps | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.tx_dropped | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.tx_errors | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.disk.io_waite | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.disk.rd_rsp_time | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.disk.wt_rsp_time | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.fs.inode_free | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.fs.percent | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| Servicemonitor_redis.dbcopyStatus | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_redis.dbsvrStatus | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_redis.connectedClientsRate | 次要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址（一般为数据库节点）。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOCertMgmt_100101 | 紧急/重要 | 业务质量 |


| 参数名称 | 参数含义 |
| --- | --- |
| 证书名称 | 即将过期的证书名称以及距离过期的时间 |
| 所属部件 | 证书所属的部件名称 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000300030001 | 紧急 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | ManageOne |
| 服务 | MOLog |
| 组件 | Elasticsearch集群 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOBackupService_100001 | 重要 | 操作告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 业务类型 | 被备份的业务类型。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOBackupService_100002 | 重要 | 操作告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 备份服务器类型 | 备份服务器的类型，可以为SFTP。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101205 | 紧急 | 备份状态 |


| 参数名称 | 参数含义 |
| --- | --- |
| 产品别名 | 产品别名。 |
| 站点名称 | 产生告警的站点名称。 |


| 表1 备份失败原因操作指导 | 表1 备份失败原因操作指导 |
| --- | --- |
| 原因 | 操作指导 |
| 未配置备份参数。 | 具体操作请参见•配置备份参数。 |
| 备份数据无法存储到备份服务器。 | 具体操作请参见•检查备份服务器的连通性和存储空间。 |
| 产品数据备份失败。 | 具体操作请参见•查看任务信息详情。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 10000011 | 紧急 | 环境告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 当前等效网元数 | 当前环境管理设备的等效网元数。 |
| 最大支持网元数 | 最大支持的等效网元数。 |
| 当前Region总数 | 当前环境的Region总数。 |
| 最大支持Region总数 | 最大支持的Region总数。 |
| 当前私有云Region数 | 当前环境完整的私有云Region数量，不包括通过HiCloud、IAAS-E、IAAS-V接入的Region。 |
| 最大支持私有云Region数 | 最大支持的私有云Region数。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOVCDRService_100091 | 重要 | 通信告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 计量中心文件服务器 | 计量中心文件服务器的地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101209 | 紧急 | 保护倒换 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点名称 | 倒换后的主节点名称。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51023 | 紧急 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点名称 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
| NTP地址 | 出现异常的NTP服务器IP地址。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101211 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 数据库故障实例的主机名。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 数据库类型 | 数据库实例类型。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101212 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 异常ZooKeeper所在节点的主机名 |
| 服务名 | 服务名称 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 151 | 重要 | 越限 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 152 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务器名 | 产生告警的节点名称 |
| 服务代理 | 产生告警的进程名称 |
| 服务名 | 产生告警的服务实例名称 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101208 | 重要 | 通信告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |


| 表1 产品节点故障排查 | 表1 产品节点故障排查 | 表1 产品节点故障排查 | 表1 产品节点故障排查 |
| --- | --- | --- | --- |
| 序号 | 检查项 | 检查方法 | 故障排除方法 |
| 1 | 网络连接 | 联系管理员检查网络是否异常。 | 请联系管理员修复网络。 |
| 2 | 虚拟机运行状态 | 联系管理员检查虚拟机是否异常，例如虚拟机是否被下电或者被删除。 | 请联系管理员重启并修复虚拟机。 |
| 3 | 操作系统运行状态 | 重启虚拟机，尝试使用PuTTY工具以sopuser用户通过SSH方式是否能登录故障节点。 | 如果不能正常登录或无响应，则说明故障节点的操作系统异常，请联系技术支持工程师处理。 |
| 4 | ProductMonitorAgent进程运行状态 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行如下命令，检查ProductMonitorAgent进程是否运行正常。<br>> ps -ef |grep ProductMonitorAgent<br>系统提示如下类似回显信息表示ProductMonitorAgent进程正在运行：<br>ossadm    21501      1  2 16:47 ?        00:01:18 /opt/oss/envs/ProductMonitorAgent/service/rtsp/python/bin/python /opt/oss/envs/ProductMonitorAgent/service/tools/pyscript/icAgent.pyc -DNFW=productmonitoragent-0-0 | 如果ProductMonitorAgent进程未运行，执行以下命令启动进程。<br>> . /opt/oss/manager/bin/engr_profile.sh<br>> ipmc_adm -cmd startapp -app ProductMonitorAgent -tenant manager<br>系统提示如下回显信息表示ProductMonitorAgent进程启动成功，否则请联系华为技术支持工程师处理。<br>Starting process productmonitoragent-0-0 ... success |
| 5 | IR证书 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，查看IR证书有效期。<br>> cd /opt/oss/manager/etc/ssl/internal<br>> openssl x509 -in server.cer -noout -dates<br>回显以下类似信息，“notAfter”后所显示的时间即为IR证书的到期时间。<br>notBefore=Oct 18 00:00:00 2018 GMT<br>notAfter=Oct 13 00:00:00 2038 GMT<br>如果IR证书已过期，请更新CA证书。<br>如果IR证书没有过期，则表示不是证书过期导致该故障。 | 更新CA证书，具体操作请参见《华为云Stack 6.5.1 安全管理指南》中的“更新CA证书”章节。 |
| 6 | 数据库复制状态 | 请参见ALM-101210 数据库本地主备复制异常。 | 请参见ALM-101210 数据库本地主备复制异常。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 154 | 重要 | 越限 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 36 | 重要 | 越限 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 磁盘 | 产生告警的服务器磁盘名称。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 38 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 47 | 重要 | 越限 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 服务名 | 产生告警的服务进程名称。 |
| 站点名称 | 产生告警的站点名称。 |
| 门限值 | 告警产生门限和清除门限。 |
| 占用值 | 服务使用内存的数值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101206 | 紧急 | 处理出错告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 53080 | 重要 | 越限 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 进程名称 | 产生告警的进程名称。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101210 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 数据库类型 | 产生告警的数据库类型。 |
| 站点名称 | 产生告警的站点名称。 |


| 表1 数据库复制状态错误码参考 | 表1 数据库复制状态错误码参考 | 表1 数据库复制状态错误码参考 | 表1 数据库复制状态错误码参考 |
| --- | --- | --- | --- |
| 错误码 | 说明 | 可能原因 | 处理建议 |
| 101 | 数据库实例所在节点停止，或该数据库实例停止。 | 该数据库实例所在节点未启动。<br>该数据库实例未启动。<br>该数据库实例所在节点磁盘存储空间已满。<br>主备数据库实例所在节点的网络通信异常。 | 使用浏览器登录ManageOne部署面。<br>登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。<br>默认帐号：admin，默认密码：*****<br>在部署面主菜单中选择“产品 > 系统监控”。<br>在“节点”页签中查看所有节点的“连接状态”和“数据库状态”是否正常。<br>是，观察2分钟，如果故障仍未恢复，请联系华为技术支持工程师。<br>否，选择停止的节点，单击界面右侧的“启动”，启动停止的节点。 |
| 102 | 数据库实例角色错误，出现双主。 | 人为将该数据库实例所在节点设置为忽略节点。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令确认是否设置了节点为忽略节点：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd get-ignore-nodes<br>回显类似如下信息：<br>ignore-nodes:2; dbtype:Gauss<br>其中，“ignore-node”的值，表示忽略的节点ID，为“None”时表示没有设置忽略节点，“dbtype”的值表示忽略的数据库类型。<br>如果是人为对主备实例所在节点设置忽略节点，请执行以下命令取消：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd del-ignore-nodes<br>如果未显示Successful表示执行失败，请联系华为技术支持工程师处理。 |
| 103 | 数据库实例角色错误，出现双备。 | 人为将该数据库实例所在节点设置为忽略节点。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令确认是否设置了节点为忽略节点：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd get-ignore-nodes<br>回显类似如下信息：<br>ignore-nodes:2; dbtype:Gauss<br>其中，“ignore-node”的值，表示忽略的节点ID，为“None”时表示没有设置忽略节点，“dbtype”的值表示忽略的数据库类型。<br>如果是人为对主备实例所在节点设置忽略节点，请执行以下命令取消：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd del-ignore-nodes<br>如果未显示Successful表示执行失败，请联系华为技术支持工程师处理。 |
| 104 | 数据库实例角色错误，角色与分布式管理服务上的记录不一致。 | 人为将该数据库实例所在节点设置为忽略节点。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令确认是否设置了节点为忽略节点：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd get-ignore-nodes<br>回显类似如下信息：<br>ignore-nodes:2; dbtype:Gauss<br>其中，“ignore-node”的值，表示忽略的节点ID，为“None”时表示没有设置忽略节点，“dbtype”的值表示忽略的数据库类型。<br>如果是人为对主备实例所在节点设置忽略节点，请执行以下命令取消：<br>> cd /opt/oss/manager/apps/DBHASwitchService/bin<br>> ./switchtool.sh -cmd del-ignore-nodes<br>如果未显示Successful表示执行失败，请联系华为技术支持工程师处理。 |
| 200 | 主备数据库实例网络通信异常。 | 备从数据库实例和主数据库实例的IO通信异常，IO线程“Slave_IO_Running”异常。 | 使用浏览器登录ManageOne部署面。<br>登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。<br>默认帐号：admin，默认密码：*****<br>在部署面主菜单中选择“产品 > 系统监控”。<br>查看所有数据库页签中，数据库实例的“复制状态”是否正常。<br>是，观察2分钟，如果故障仍未恢复，请联系华为技术支持工程师。<br>否，选择“节点”页签，选择异常节点。在界面右侧单击“停止”，等节点停止成功后，再单击“启动”，重启故障节点。 |
| 201 | 备数据库实例GTID复制延迟。<br>说明： <br>每一个GTID代表一个数据库事务。 | 短时间有大量数据库写操作，导致主备数据库复制处理延迟。<br>备数据库GTID落后主数据库实例1000及以上。 | 观察2分钟，如果该告警还未恢复，或经常出现复制延迟，请联系华为技术支持工程师。 |
| 210 | 备数据库实例的数据库线程异常。 | 备数据库数据库进程“Slave_SQL_Running”异常。<br>人为使用dbuser用户对备实例违规进行写操作。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 211 | 备数据库实例的GTID比主数据库实例多。 | 人为使用dbuser用户对备实例违规进行写操作。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 212 | 双主模式，GTID有数据冲突。 | 故障倒换前有部分数据未复制到备数据库实例，倒换后原来主数据库实例出现数据冲突。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 213 | 主备倒换时出现异常，导致数据冲突。 | 故障倒换前数据还没有完全写入主数据库即发生主备倒换，导致数据冲突。 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。登录产生告警的节点。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，恢复异常数据库实例。<br>> cd /opt/oss/manager/apps/UniEPService/tools/DB_Recovery<br>> bash DBSlaveInstance_Recovery.sh -instid 数据库实例名称 -tenant 产品名称<br>系统提示如下类似回显信息时，表示该数据库实例恢复成功；否则请联系华为技术支持工程师。<br>Recovery DB-Instance Success. |
| 301 | 备实例复制延迟。 | 短时间有大量数据库写操作，导致主备数据库复制处理延迟。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 302 | 备实例正在启动。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 303 | 备数据库需要手工重建。 | 该数据库实例所在节点未启动。<br>该数据库实例未启动。<br>主备节点网络通信异常。<br>主备数据库版本不一致。<br>主备实例连接异常。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 304 | 主实例正在降备。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 305 | 备实例正在降为级联备实例。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 306 | 备实例正在升主。 | 中间状态。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 307 | 未知错误。 | 未知错误。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 310 | 备数据库需要重建，系统自动修复。 | 备数据库实例待同步的数据在主数据库实例上不存在。<br>主备数据库实例的数据库目录不是同一个数据库创建。<br>主备数据库实例中数据的时间不匹配。 | 观察2分钟，如果该告警还未恢复，请联系华为技术支持工程师。 |
| 401 | 备实例复制延迟。 | 短时间有大量数据库写操作，导致主备数据库复制处理延迟。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 402 | 备实例正在启动。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 403 | 备数据库需要重建，系统自动修复。 | 备数据库实例待同步的数据在主机上不存在。<br>主备数据库实例的数据库目录不是同一个数据库创建。<br>主备数据库实例中数据的时间不匹配。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 404 | 主实例正在降备。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 405 | 备实例正在降备。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 406 | 手工倒换后，备实例正在升主。 | 手工倒换后，备实例正在升主。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 407 | 故障倒换后，备实例正在升主。 | 故障倒换后，备实例正在升主。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 408 | 备数据库正在重建。 | 中间状态。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 409 | 无处理。 | 无处理。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系华为技术支持工程师。 |
| 410 | 备实例与主实例断开。 | 该数据库主实例所在节点未启动。<br>该数据库主实例未启动。<br>主备节点网络通信异常。 | 使用浏览器登录ManageOne部署面。<br>登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。<br>默认帐号：admin，默认密码：*****<br>在部署面主菜单中选择“产品 > 系统监控”。<br>在“节点”页签中查看所有节点的“连接状态”和“数据库状态”是否正常。<br>是，观察2分钟，如果故障仍未恢复，请联系华为技术支持工程师。<br>否，选择停止的节点，单击界面右侧的“启动”，启动停止的节点。 |
| 411 | 未知错误。 | 未知错误。 | 等待2-3分钟会自动修复，如果过一段时间还未修复成功，请联系DB管理员定位。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51020 | 重要 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点IP地址。 |
| 证书类型 | ER证书、CA证书或者IR证书。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51021 | 紧急 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点IP地址。 |
| 证书类型 | ER证书。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51022 | 紧急 | 处理错误告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 证书类型 | ER证书或者IR证书。 |
| 站点名称 | 产生告警的站点名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 36064531474581504 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测到异常IAM服务所在服务器节点IP。 |
| 定位信息 | 包含规则ID和事件名称。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0004000700010009 | 重要 | 业务质量告警 |


| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测IAM鉴权失败所在服务器节点IP。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150001 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150002 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150003 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150004 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150017 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150018 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 微服务 | 产生告警的微服务名 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060036 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警ID | 告警ID | 告警ID |
| 通知类型 | 通知类型 | 告警 |
| 告警级别 | 告警级别 | 告警的严重级别：<br>1 紧急<br>2 重要<br>3 次要<br>4 提示 |
| 最近发生时间 | 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务 | 云服务名 |
| 定位信息 | 主机地址 | 发送告警的主机地址 |
| 附加信息 | 云服务 | 云服务名 |
| 附加信息 | 服务 | 服务名 |
| 附加信息 | Cinder URL | 无法连通的Cinder节点的URL |
| 附加信息 | 错误信息 | 错误信息 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131007 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 产生告警信息的监控系统名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131009 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131011 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131012 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101308 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源设备名称 | 来源设备名称 | 产生告警信息的设备名称 |
| 监控系统名称 | 监控系统名称 | 对接系统的类型 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 发生时间 | 发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | HostIP | 组合 API 主机 IP |
| 附加信息 | CloudService | 云服务名称 |
| 附加信息 | Service | 服务名称 |
| 附加信息 | DatabaseIP | 组合 API 数据库 IP |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101312 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源设备名称 | 来源设备名称 | 产生告警信息的设备名称 |
| 监控系统名称 | 监控系统名称 | 对接系统的类型 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 发生时间 | 发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | Service | 产生此告警的服务名 |
| 附加信息 | 云服务 | 云服务名 |
| 附加信息 | 服务 | 服务名 |
| 附加信息 | Fsp组件 | 组件名 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000007 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000008 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000100 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000201 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000200 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200025 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200027 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200028 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200030 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200032 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200033 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200034 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200035 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200053 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200054 | 重要 | 否 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 来源系统 | 告警来源。 |
| IP地址 | IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 网络ID | 产生告警信息的外部网络ID。 |
| 定位信息 | 子网ID | 产生告警信息的子网ID。 |
| 附加信息 | 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200074 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警的定位信息。 |
| 附加信息 | 告警附加信息。 |


| 表1 环境变量参数说明 | 表1 环境变量参数说明 |
| --- | --- |
| 参数 | 含义 |
| OS_AUTH_URL | 鉴权地址，对应Keystone服务的endpoint。<br>使用publicURL的endpoint。<br>需修改的部分包括AZ名、DC名、以及域名后缀。 |
| OS_USERNAME | 执行操作时使用的DC管理员的帐号。DC管理员在安装完成后自动创建，格式为dcname_admin。例如dcname为dc1，则DC管理员用户名为dc1_admin。 |
| OS_TENANT_NAME | 执行操作的DC管理员所属租户信息。租户信息在安装完成后自动创建，格式为dc_system_dcname。 |
| OS_REGION_NAME | AZ的域名，如az1.dc1。 |
| OS_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| NOVA_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| CINDER_ENDPOINT_TYPE | endpoint的类型，在使用OpenStack的命令时需要导入，请使用“internalURL”。 |
| OS_VOLUME_API_VERSION | 使用的VOLUME版本，需要输入2，表示使用V2版本。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223005 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223006 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223013 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223014 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223016 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40000 | 紧急 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40001 | 紧急 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40002 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| ExpirationTime | 试用期过期时间。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40003 | 紧急 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000F40000 | 紧急 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000F40001 | 紧急 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40004 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| ExpirationTime | 试用期过期时间。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40005 | 重要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40007 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| res_name | 资源项名称。 |
| res_capacity | 资源项的授权容量。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40008 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| res_name | 资源项名称。 |
| res_capacity | 资源项的授权容量。 |
| threshold_value | 告警阈值。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F4000C | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40013 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40014 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40016 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40017 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40018 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000310000 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Threshold_size | 允许转储目录使用空间的阈值。 |
| Max_size | 允许转储目录使用的最大空间。 |
| Dump_dir | 事件转储路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000A | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | 告警服务器的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000C | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310010 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip | 告警服务器管理IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310015 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000101 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000100 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840001 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840002 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |
| Date | 证书过期日期。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000200 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | LDAP服务器的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800860001 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| backupId | 备份ID |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x21000000090E | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| snapshotId | 快照ID |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x21000000090F | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | eBackup节点IP |
| Module_Type | 对接组件类型 |
| IP_Address | 组件地址 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000901 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | eBackup备份节点IP |
| MicroService_Name | eBackup微服务名称 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01D0005 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Module | OpenStack组件名 |
| IP_Address | OpenStack组件的IP |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1010E01A0018 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| Machine_name | 受保护对象名称。 |
| Disk_id | 磁盘ID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000C90003 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip | eBackup服务器IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000C90004 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Server_id | eBackup服务器IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90002 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| DBBackup_path | 系统数据库备份共享存储路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90035 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90003 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| DBBackup_path | 系统数据库备份共享存储路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90005 | 重要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90006 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90032 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Ftp_path | FTP服务器路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90033 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| User | FTP用户。 |
| Ftp_path | FTP服务器路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90034 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| User | FTP用户。 |
| Ftp_path | FTP服务器路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90004 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| DBBackup_path | 系统数据库备份共享存储路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E0007 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Brick_name | 存储单元名称。 |
| Brick_path | 存储单元的路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E000A | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Path | 存储单元的路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E0006 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Brick_name | 存储单元名称。 |
| Brick_path | 存储单元的路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01D0006 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | eBackup管理平面的IP。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90000 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip1 | eBackup服务器IP地址。 |
| Server_ip2 | eBackup服务器IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00C0000 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| Namespace_name | 存储库的名称。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0000 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000760001 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | eBackup服务器的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0027 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | ebackup服务器IP地址。 |
| Process_Name | 异常进程列表。 |
| Service_Name | 服务名称。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00E0000 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_IP | 备份代理的IP地址。 |
| Brick_path | 存储单元的路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0029 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
| IP | 浮动IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0001 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | Workflow（Proxy）IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0002 | 紧急 | 是 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0003 | 紧急 | 是 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0004 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
| Time | keepalive配置的心跳中断时间。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0005 | 紧急 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name_P | HA主节点IP地址。 |
| Node_Name_S | HA备节点IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0009 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
| Gatway_IP | 网关IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000B | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000E | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000F | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0010 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0011 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0028 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00E0001 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_IP | 备份代理的IP地址。 |
| Brick_path | 存储单元的路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E0140001 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Protected_Env_name | 受保护环境名称。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E0140000 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Protected_Env_IP | 受保护环境IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E014000C | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | 受保护环境的IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00D0000 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Storagepool_name | 存储池名称。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90002 | 次要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90025 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr1 | NTP服务器IP地址。 |
| IP_addr2 | eBackup服务器IP地址。 |
| Time_diff | NTP服务器与eBackup服务器之间的时间差值。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90024 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr1 | eBackup服务器IP地址。 |
| IP_addr2 | NTP服务器IP地址。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90009 | 重要 | 否 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790001 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| SFtp_path | SFTP服务器路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790002 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| User | SFTP用户。 |
| SFtp_path | SFTP服务器路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790003 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| User | SFTP用户。 |
| SFtp_path | SFTP服务器路径。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0010 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 备份对象名称。 |
| Machine_UUID | 备份对象UUID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0011 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 虚拟机名称。 |
| Machine_UUID | 虚拟机UUID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0019 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 任务ID。 |
| Snap_id | 备份副本ID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A0008 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 受保护对象名称。 |
| Machine_UUID | 受保护对象UUID。 |
| Task_id | 任务ID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A000E | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C9000A | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理IP。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000D00 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| ipAddr | 本端备份节点IP。 |
| vppServerIP | 远端vpp服务器IP。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000C90021 | 次要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 任务ID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A001D | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| TaskID | 复制任务ID。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0014 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| DSWare_IP | FusionStorageManager地址。 |
| Vol_name | 卷名称。 |
| errMsg | 错误信息。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0015 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| DSWare_IP | FusionStorageManager地址。 |
| Vol_name | 卷名称。 |
| errMsg | 错误信息。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0017 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| Vol_ID | 卷ID。 |
| Device_IP | DeviceManager管理IP。 |
| Lungroup_name | LUN组名称。 |
| errorDes | 错误信息。 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6300740001 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| Task_id | 备份任务ID |
| Current_Snap_id | 当前备份副本ID |
| Disk_id | 磁盘ID |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840003 | 重要 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| IPAddress | ManageOne的IP地址 |


| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800740001 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | 备份代理IP地址。 |
| Service_Name | 微服务名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020799 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云服务器ID | 复制失败的源副本对应的云服务器ID。 |
| 源副本ID | 复制失败的源副本ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云服务器的策略名称。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020796 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 配额类型 | 消耗到阈值的配额类型。backup_capacity代表备份配额，copy_capacity代表复制配额。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 配额的总量。 |
| 已使用 | 配额的已使用量。 |
| 阈值 | 产生告警需要超过配额已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于配额已使用/总量的百分比。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020791 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020790 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020788 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云服务器ID | 备份失败的云服务器ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云服务器的策略名称。 |
| 副本ID | 本次生成状态为“错误”的副本ID。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020783 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020782 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020779 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云硬盘ID | 备份失败的云硬盘ID。 |
| 源副本ID | 复制失败的源副本ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云硬盘的策略名称。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020776 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 配额类型 | 消耗到阈值的配额类型。volume_backup_capacity代表备份配额，volume_copy_capacity代表复制配额。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 配额的总量。 |
| 已使用 | 配额的已使用量。 |
| 阈值 | 产生告警需要超过配额已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于配额已使用/总量的百分比。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020771 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020770 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020768 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云硬盘ID | 备份失败的云硬盘ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云硬盘的策略名称。 |
| 副本ID | 本次生成状态为“错误”的副本ID。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020762 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 校验失败的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020761 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 校验失败的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023299 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023298 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 组件 | 异常的组件。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023296 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| NTP服务器IP | 同步异常的NTP服务器IP。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023295 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023282 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023279 | 重要->紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 即将过期的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |
| 过期时间 | 证书过期时间。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023278 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 证书全路径 | 已过期的证书全路径。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023277 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023276 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名称 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023099 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
| 已使用 | CPU已使用率。 |
| 阈值 | 产生告警需要超过的CPU已使用率。 |
| 清除阈值 | 清除告警需要低于的CPU已使用率。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023098 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 内存总量。 |
| 已使用 | 内存已使用量。 |
| 阈值 | 产生告警需要超过内存已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于内存已使用/总量的百分比。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023097 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 分区名 | 使用率超过阈值的磁盘分区名。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 磁盘分区总量。 |
| 已使用 | 磁盘分区已使用量。 |
| 阈值 | 产生告警需要超过磁盘分区已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于磁盘分区已使用/总量的百分比。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020800 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 执行失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 执行失败的策略名称。 |
| 目标区域名称 | 复制的目标区域名称。 |
| 目标项目名称 | 复制的目标项目名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020803 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 执行失败的策略ID。 |
| 目标区域ID | 复制的目标区域ID。 |
| 目标项目ID | 复制的目标项目ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 自动调度失败的策略名称。 |
| 目标区域名称 | 复制的目标区域名称。 |
| 目标项目名称 | 复制的目标项目名称。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020759 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| SC地址 | ManageOne运营平台接口地址。 |
| 节点IP | 产生告警的节点IP。 |
| HTTP返回码 | 请求响应的HTTP返回码。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020758 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点IP | 产生告警的节点IP。 |
| 错误码 | 故障对应的错误码。 |
| Meter地址 | 计量服务接口地址。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023093 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 设备名称 | 用于网络通信的网络设备名称。 |
| 目标IP | 与当前节点通信异常的目标节点IP。 |
| 节点IP | 产生告警的节点IP。 |
| 故障原因 | 导致上报告警的网络状态检测结果。 |
| 额外信息 | 网络状态检测结果的说明信息。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020756 | 次要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点IP | 产生告警的节点IP。 |
| 统一证书管理服务URL | 统一证书管理服务URL。 |
| HTTP返回码 | 请求响应的HTTP返回码。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230014 | 警告 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP地址 | 备份配置数据的服务器IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230024 | 警告 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 网元IP地址 | 网元证书所在节点的IP地址 |
| 服务器IP地址 | 服务器的IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230025 | 警告 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 网元IP地址 | 网元证书所在节点的IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002C | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中的生产虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002D | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中的生产虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323002F | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 一致性组ID | 服务实例对应存储的一致性组ID |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230030 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 备节点名称 | 备节点的名称 |
| 备节点IP地址 | 备节点的IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230031 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 备节点名称 | 备节点的名称 |
| 备节点IP地址 | 备节点的IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230033 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点名称 |
| 主节点IP地址 | 主节点IP地址 |
| 主节点端口 | 主节点端口 |
| 备节点名称 | 备节点名称 |
| 备节点IP地址 | 备节点IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230034 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 主节点角色 | 主节点的角色名称 |
| 网关 | 网关的IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230036 | 紧急 | 是 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230037 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230038 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003A | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 虚拟机名称 | 服务实例中不满足保护要求的虚拟机名称 |
| 服务实例名称 | 服务实例的名称 |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003B | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003C | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中未创建容灾保护的卷对应的虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003D | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中已经被移除的虚拟机名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003E | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003F | 警告 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| IAM IP地址或者域名地址 | IAM服务器的IP地址或者域名 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230041 | 警告 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 上报失败的Project名称。如果是多个Project，用逗号隔开。 | 计量信息上报失败的项目名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230042 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 日志类型 | 上报的日志类型 |
| 服务器IP地址 | 上报日志的服务器IP地址 |
| 日志服务器IP地址或域名 | 日志服务器的IP地址或域名 |
| 端口 | 日志服务器的监听端口 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230043 | 紧急 | 是 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230046 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中缺少共享卷关联的虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230047 | 警告 | 否 |


| 参数名称 | 参数含义 |
| --- | --- |
| 订单ID | 上报配额失败的订单ID |
| 管理平台IP或域名 | 管理平台IP或域名 |
| 管理平台端口 | 管理平台端口 |
| 服务器IP | 服务器的IP地址 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230048 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 受保护的VHA服务器数量 | VHA服务实例已保护的服务器总数量 |
| 受保护的CSHA服务器数量 | CSHA服务实例已保护的服务器总数量 |
| 受保护的CSDR服务器数量 | CSDR服务实例已保护的服务器总数量 |
| 授权的VHA服务器数量 | VHA服务实例能够保护的服务器总数量 |
| 授权的CSHA服务器数量 | CSHA服务实例能够保护的服务器总数量 |
| 授权的CSDR服务器数量 | CSDR服务实例能够保护的服务器总数量 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230049 | 重要 | 是 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323005C | 警告/紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 过期有效剩余天数（30、7） | 证书过期前的有效剩余天数 |
| 组件名称 | 证书的名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323005D | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 证书已过期天数 | 证书已经过期后的天数 |
| 组件名称 | 证书的名称 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230064 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 服务器IP | 服务器的IP地址 |
| 微服务类型 | 微服务类型（证书或者统一备份） |
| 管理平台IP或域名 | 管理平台IP或域名 |
| 管理平台端口 | 管理平台端口 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000401 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | tomcat进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000420 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | NS进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000421 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000456 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000469 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000488 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000489 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000460 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000462 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000463 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000471 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000493 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1160001 | 紧急 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 最后发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1160003 | 重要 | 是 |


| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 最后发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 8000888 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | dmk进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510000 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510002 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510003 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 表1 变量说明 | 表1 变量说明 | 表1 变量说明 |
| --- | --- | --- |
| 变量名 | 变量说明 | 默认证书中的值 |
| CERT_PATH | 表示新证书上传到环境上的临时路径，本文档以/home/<USER>/certs 为例。需要保证目录有上传文件的权限。 | 以实际规划为准 |
| CERT_CN | 表示证书生成时的组织信息。 | www.huawei.com |
| CERT_CLIENT_KEY_PWD | 表示客户端证书私钥的保护密码，可以为空。 | ***** |
| CERT_SERVER_KEY_PWD | 表示服务端证书私钥的保护密码，可以为空。 | ***** |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510005 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510006 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2001101 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2001002 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2001006 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 8000021 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 7700073 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 7700071 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000904 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000906 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000908 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000909 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000722 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000724 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000301 | 紧急 | 否 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域。 |
| 定位信息 | 云服务 | 产生告警信息的云服务。 |
| 附加信息 | 云服务 | 产生告警信息的云服务。 |
| 附加信息 | 失败话单总数 | 生成话单失败的告警次数。 |
| 附加信息 | 失败资源类型 | 生成话单失败的资源类型。 |
| 附加信息 | 失败原因 | 生成告警的原因，例如“Query data from ceilometer error”。 |
| 附加信息 | 开始时间 | 开始时间对应的时间戳。 |
| 附加信息 | 结束时间 | 结束时间对应的时间戳。 |
| 附加信息 | 虚拟机名称 | 产生告警信息的虚拟机名称。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000317 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000327 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000328 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1320004 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1320019 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000266 | 紧急 | 否 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2001106 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2002101 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2002302 | 重要 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2002501 | 紧急 | 是 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2001107 | 重要 | 否 |


| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48101 | 紧急 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48102 | 次要 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48103 | 重要 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48104 | 重要 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48107 | 重要 | 通信告警 |


| 分类 | 参数名称↵ | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48108 | 重要 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48110 | 重要 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48111 | 紧急 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48117 | 紧急 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48118 | 重要 | 通信告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48303 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 定位信息 | File_Name | 无法访问的文件名 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48304 | 次要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48305 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48306 | 紧急 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48316 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Node_Rate_Limit | 流控阀值 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48317 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 告警源组件 |
| 定位信息 | Node | 告警源节点IP地址 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48318 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 回退失败证书所属组件名称 |
| 定位信息 | File_Name | 回退失败证书名称 |
| 定位信息 | Node | 告警源节点IP地址 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48319 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 未生效证书所属组件名称 |
| 定位信息 | File_Name | 未生效证书名称 |
| 定位信息 | Node | 告警源节点IP地址 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48320 | 重要 | 处理错误告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48401 | 紧急 | 设备告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 故障组件名称 |
| 定位信息 | Node | 故障节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |


| 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 |
| --- | --- | --- |
| 组件 | 用户名 | 命令 |
| gaussdb | apigw_db | su - apigw_db |
| adminportal | apigw_portal | su - apigw_portal |
| apigmgr | apigw_apimgr | su - apigw_apimgr |
| cassandra | apigw_scdb | su - apigw_scdb |
| 其他组件（除gaussdb、adminportal、apigmgr、cassandra以外的组件） | apigateway | su - apigateway |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48402 | 紧急 | 设备告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 告警源组件 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Port | 端口 |


| 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 |
| --- | --- | --- |
| 组件 | 用户名 | 命令 |
| gaussdb | apigw_db | su - apigw_db |
| adminportal | apigw_portal | su - apigw_portal |
| apigmgr | apigw_apimgr | su - apigw_apimgr |
| cassandra | apigw_scdb | su - apigw_scdb |
| 其他组件（除gaussdb、adminportal、apigmgr、cassandra以外的组件） | apigateway | su - apigateway |


| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48409 | 次要 | 设备告警 |


| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 故障节点IP地址 |
| 定位信息 | User_Name | 用户名 |
| 附加信 | Alarm_Reason | 告警原因 |
