#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ALM告警文档批量转换器
将华为云Stack告警处理参考文档按ALM章节拆分并转换为Markdown
"""

import os
import re
import time
from datetime import datetime
from docx import Document
from enhanced_doc_converter import EnhancedDocxToMarkdown
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('alm_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ALMBatchConverter:
    def __init__(self, source_docx, output_dir="alm_markdown_output"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        self.doc = Document(source_docx)
        self.temp_dir = os.path.join(output_dir, "temp_docx")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        logger.info(f"初始化ALM批量转换器")
        logger.info(f"源文档: {source_docx}")
        logger.info(f"输出目录: {output_dir}")
    
    def find_alm_chapters(self):
        """查找所有ALM章节"""
        logger.info("🔍 开始查找ALM章节...")
        
        chapters = []
        
        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()
            
            # 检查是否是ALM告警章节标题
            if (paragraph.style.name.startswith('Heading 1') and 
                'ALM-' in text and 
                text.startswith('*******.')):
                
                # 提取ALM代码
                alm_match = re.search(r'ALM-\d+', text)
                if alm_match:
                    alm_code = alm_match.group()
                    
                    # 提取告警名称
                    title_match = re.search(r'ALM-\d+\s+(.+)', text)
                    alarm_name = title_match.group(1) if title_match else "未知告警"
                    
                    chapters.append({
                        'title': text,
                        'alm_code': alm_code,
                        'alarm_name': alarm_name,
                        'start_para': i,
                        'paragraph': paragraph
                    })
                    
                    if len(chapters) % 10 == 0:
                        logger.info(f"  已找到 {len(chapters)} 个ALM章节...")
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                chapter['end_para'] = len(self.doc.paragraphs)
        
        logger.info(f"✅ 找到 {len(chapters)} 个ALM告警章节")
        return chapters
    
    def extract_chapter_content(self, start_para, end_para):
        """提取章节内容"""
        content_elements = []
        
        # 获取所有body元素
        body_elements = list(self.doc.element.body)
        
        # 找到段落对应的XML元素索引
        start_element_idx = None
        end_element_idx = None
        
        for i, element in enumerate(body_elements):
            if element.tag.endswith('p'):
                # 找到对应的段落对象
                for j, para in enumerate(self.doc.paragraphs):
                    if para._element == element:
                        if j == start_para:
                            start_element_idx = i
                        elif j == end_para:
                            end_element_idx = i
                        break
        
        if start_element_idx is None:
            return content_elements
        
        if end_element_idx is None:
            end_element_idx = len(body_elements)
        
        # 提取指定范围的元素
        for i in range(start_element_idx, end_element_idx):
            element = body_elements[i]
            
            if element.tag.endswith('p'):  # 段落
                for para in self.doc.paragraphs:
                    if para._element == element:
                        content_elements.append(('paragraph', para))
                        break
            elif element.tag.endswith('tbl'):  # 表格
                for table in self.doc.tables:
                    if table._tbl == element:
                        content_elements.append(('table', table))
                        break
        
        return content_elements
    
    def create_chapter_document(self, chapter, content_elements):
        """创建章节文档"""
        new_doc = Document()
        
        # 复制样式（简化版本）
        try:
            for style in self.doc.styles:
                if style.name not in [s.name for s in new_doc.styles]:
                    try:
                        new_doc.styles.add_style(style.name, style.type)
                    except:
                        pass
        except:
            pass
        
        # 添加内容
        for elem_type, elem in content_elements:
            if elem_type == 'paragraph':
                # 复制段落
                new_para = new_doc.add_paragraph()
                new_para.text = elem.text
                try:
                    new_para.style = elem.style
                except:
                    pass
            elif elem_type == 'table':
                # 复制表格
                try:
                    new_table = new_doc.add_table(rows=len(elem.rows), cols=len(elem.columns))
                    for i, row in enumerate(elem.rows):
                        for j, cell in enumerate(row.cells):
                            if i < len(new_table.rows) and j < len(new_table.columns):
                                new_table.cell(i, j).text = cell.text
                except Exception as e:
                    logger.warning(f"复制表格时出错: {e}")
        
        return new_doc
    
    def generate_safe_filename(self, chapter):
        """生成安全的文件名"""
        alm_code = chapter['alm_code']
        alarm_name = chapter['alarm_name']
        
        # 清理文件名中的非法字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', alarm_name)
        safe_name = safe_name.strip()
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]
        
        return f"{alm_code}_{safe_name}"
    
    def convert_chapter_to_markdown(self, docx_path, chapter_info):
        """将单个章节转换为Markdown"""
        try:
            # 创建转换器
            converter = EnhancedDocxToMarkdown(
                docx_path, 
                os.path.join(self.output_dir, f"{chapter_info['safe_name']}_output")
            )
            
            # 执行转换
            md_filename = f"{chapter_info['safe_name']}.md"
            output_path = converter.convert_to_markdown(md_filename)
            
            return output_path
            
        except Exception as e:
            logger.error(f"转换章节 {chapter_info['alm_code']} 时出错: {e}")
            return None
    
    def process_all_chapters(self):
        """处理所有章节"""
        start_time = time.time()
        logger.info("🚀 开始批量处理ALM章节...")
        
        # 查找章节
        chapters = self.find_alm_chapters()
        
        if not chapters:
            logger.error("❌ 未找到ALM章节")
            return []
        
        successful_conversions = []
        failed_conversions = []
        
        # 处理每个章节
        for i, chapter in enumerate(chapters):
            chapter_start_time = time.time()
            
            logger.info(f"\n📄 处理第 {i+1}/{len(chapters)} 章: {chapter['alm_code']}")
            logger.info(f"   标题: {chapter['alarm_name']}")
            
            try:
                # 1. 提取章节内容
                logger.info("   📋 提取章节内容...")
                content_elements = self.extract_chapter_content(
                    chapter['start_para'], 
                    chapter['end_para']
                )
                
                if not content_elements:
                    logger.warning("   ⚠️  章节内容为空，跳过")
                    failed_conversions.append({
                        'chapter': chapter,
                        'error': '章节内容为空'
                    })
                    continue
                
                # 2. 创建临时文档
                logger.info("   📝 创建临时文档...")
                new_doc = self.create_chapter_document(chapter, content_elements)
                
                # 3. 保存临时文档
                safe_name = self.generate_safe_filename(chapter)
                temp_docx_path = os.path.join(self.temp_dir, f"{safe_name}.docx")
                new_doc.save(temp_docx_path)
                
                # 4. 转换为Markdown
                logger.info("   🔄 转换为Markdown...")
                chapter_info = {
                    'alm_code': chapter['alm_code'],
                    'safe_name': safe_name
                }
                
                md_path = self.convert_chapter_to_markdown(temp_docx_path, chapter_info)
                
                if md_path:
                    chapter_time = time.time() - chapter_start_time
                    logger.info(f"   ✅ 转换成功! 耗时: {chapter_time:.2f}秒")
                    logger.info(f"   📁 输出: {md_path}")
                    
                    successful_conversions.append({
                        'chapter': chapter,
                        'md_path': md_path,
                        'docx_path': temp_docx_path,
                        'time': chapter_time
                    })
                else:
                    logger.error(f"   ❌ 转换失败")
                    failed_conversions.append({
                        'chapter': chapter,
                        'error': '转换失败'
                    })
                
                # 清理临时文件（可选）
                # os.remove(temp_docx_path)
                
            except Exception as e:
                logger.error(f"   ❌ 处理章节时出错: {e}")
                failed_conversions.append({
                    'chapter': chapter,
                    'error': str(e)
                })
                continue
        
        # 输出总结
        total_time = time.time() - start_time
        self.print_summary(successful_conversions, failed_conversions, total_time)
        
        return successful_conversions
    
    def print_summary(self, successful, failed, total_time):
        """打印处理总结"""
        logger.info("\n" + "="*80)
        logger.info("📊 批量转换完成总结")
        logger.info("="*80)
        
        logger.info(f"⏱️  总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        logger.info(f"✅ 成功转换: {len(successful)} 个章节")
        logger.info(f"❌ 转换失败: {len(failed)} 个章节")
        logger.info(f"📈 成功率: {len(successful)/(len(successful)+len(failed))*100:.1f}%")
        
        if successful:
            logger.info(f"\n📁 成功转换的文件:")
            for i, item in enumerate(successful[:10], 1):  # 只显示前10个
                chapter = item['chapter']
                logger.info(f"  {i:2d}. {chapter['alm_code']} - {chapter['alarm_name'][:40]}...")
            
            if len(successful) > 10:
                logger.info(f"  ... 还有 {len(successful) - 10} 个文件")
        
        if failed:
            logger.info(f"\n❌ 转换失败的章节:")
            for i, item in enumerate(failed, 1):
                chapter = item['chapter']
                error = item['error']
                logger.info(f"  {i:2d}. {chapter['alm_code']} - {error}")
        
        logger.info(f"\n📂 输出目录: {self.output_dir}")
        logger.info("="*80)


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "alm_markdown_results"
    
    if not os.path.exists(source_file):
        logger.error(f"❌ 错误：找不到源文件 {source_file}")
        return
    
    # 创建转换器
    converter = ALMBatchConverter(source_file, output_dir)
    
    # 执行批量转换
    try:
        successful_conversions = converter.process_all_chapters()
        
        if successful_conversions:
            logger.info(f"\n🎉 批量转换完成!")
            logger.info(f"📄 成功转换 {len(successful_conversions)} 个ALM告警章节")
            logger.info(f"📁 输出目录: {output_dir}")
            
            # 生成索引文件
            create_index_file(successful_conversions, output_dir)
        else:
            logger.error("❌ 没有成功转换任何章节")
            
    except Exception as e:
        logger.error(f"❌ 批量转换失败: {e}")
        import traceback
        traceback.print_exc()


def create_index_file(successful_conversions, output_dir):
    """创建索引文件"""
    try:
        index_path = os.path.join(output_dir, "README.md")
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write("# 华为云Stack告警处理参考 - ALM章节索引\n\n")
            f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"总计: {len(successful_conversions)} 个ALM告警章节\n\n")
            f.write("## 章节列表\n\n")
            
            for i, item in enumerate(successful_conversions, 1):
                chapter = item['chapter']
                md_filename = os.path.basename(item['md_path'])
                
                f.write(f"{i:3d}. [{chapter['alm_code']} {chapter['alarm_name']}]({md_filename})\n")
        
        logger.info(f"📋 已生成索引文件: {index_path}")
        
    except Exception as e:
        logger.warning(f"生成索引文件时出错: {e}")


if __name__ == "__main__":
    main()
