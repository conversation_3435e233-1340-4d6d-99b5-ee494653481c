#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档结构分析器
分析Word文档的章节结构，为拆分做准备
"""

from docx import Document
import os


class DocumentAnalyzer:
    def __init__(self, docx_path):
        self.docx_path = docx_path
        self.doc = Document(docx_path)
    
    def analyze_structure(self):
        """分析文档结构"""
        print(f"分析文档: {self.docx_path}")
        print("=" * 60)
        
        # 统计信息
        total_paragraphs = len(self.doc.paragraphs)
        total_tables = len(self.doc.tables)
        
        print(f"总段落数: {total_paragraphs}")
        print(f"总表格数: {total_tables}")
        print()
        
        # 分析标题结构
        print("文档标题结构:")
        print("-" * 40)
        
        headings = []
        for i, paragraph in enumerate(self.doc.paragraphs):
            if paragraph.style.name.startswith('Heading'):
                level = self._get_heading_level(paragraph.style.name)
                text = paragraph.text.strip()
                if text:  # 只记录非空标题
                    headings.append({
                        'level': level,
                        'text': text,
                        'paragraph_index': i,
                        'style': paragraph.style.name
                    })
                    
                    # 显示标题层级
                    indent = "  " * (level - 1)
                    print(f"{indent}H{level}: {text} (段落 {i})")
        
        print()
        print(f"找到 {len(headings)} 个标题")
        
        # 分析章节结构
        self._analyze_chapters(headings)
        
        return headings
    
    def _get_heading_level(self, style_name):
        """获取标题级别"""
        try:
            return int(style_name.split()[-1])
        except:
            return 1
    
    def _analyze_chapters(self, headings):
        """分析章节结构"""
        print("\n章节分析:")
        print("-" * 40)
        
        # 找到一级标题（章节）
        chapters = [h for h in headings if h['level'] == 1]
        
        if not chapters:
            print("未找到一级标题，尝试查找二级标题作为章节...")
            chapters = [h for h in headings if h['level'] == 2]
        
        if not chapters:
            print("未找到明确的章节结构")
            return
        
        print(f"找到 {len(chapters)} 个主要章节:")
        
        for i, chapter in enumerate(chapters):
            print(f"\n第 {i+1} 章: {chapter['text']}")
            
            # 计算章节内容范围
            start_para = chapter['paragraph_index']
            
            # 找到下一章的开始位置
            if i + 1 < len(chapters):
                end_para = chapters[i + 1]['paragraph_index']
            else:
                end_para = len(self.doc.paragraphs)
            
            para_count = end_para - start_para
            print(f"  段落范围: {start_para} - {end_para-1} (共 {para_count} 段)")
            
            # 统计该章节的子标题
            chapter_headings = [h for h in headings 
                              if start_para <= h['paragraph_index'] < end_para 
                              and h['level'] > chapter['level']]
            
            if chapter_headings:
                print(f"  子标题数: {len(chapter_headings)}")
                for sub_heading in chapter_headings[:5]:  # 只显示前5个
                    sub_indent = "    " + "  " * (sub_heading['level'] - chapter['level'] - 1)
                    print(f"{sub_indent}H{sub_heading['level']}: {sub_heading['text']}")
                
                if len(chapter_headings) > 5:
                    print(f"    ... 还有 {len(chapter_headings) - 5} 个子标题")


def main():
    """主函数"""
    input_file = "华为云Stack告警处理参考.docx"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    analyzer = DocumentAnalyzer(input_file)
    headings = analyzer.analyze_structure()
    
    # 保存分析结果
    with open("document_structure.txt", "w", encoding="utf-8") as f:
        f.write(f"文档结构分析: {input_file}\n")
        f.write("=" * 60 + "\n\n")
        
        for heading in headings:
            indent = "  " * (heading['level'] - 1)
            f.write(f"{indent}H{heading['level']}: {heading['text']} (段落 {heading['paragraph_index']})\n")
    
    print(f"\n分析结果已保存到: document_structure.txt")


if __name__ == "__main__":
    main()
