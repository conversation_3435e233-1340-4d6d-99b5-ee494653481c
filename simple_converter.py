#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Word到Markdown转换器
专门针对告警文档的转换需求
"""

from docx import Document
import os
import zipfile
import shutil
import re


class SimpleDocxConverter:
    def __init__(self, docx_path, output_dir="output"):
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.image_dir = os.path.join(output_dir, "images")
        self.doc = Document(docx_path)
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)
    
    def extract_images(self):
        """提取文档中的图片"""
        image_count = 0
        try:
            with zipfile.ZipFile(self.docx_path, 'r') as docx_zip:
                for file_info in docx_zip.filelist:
                    if file_info.filename.startswith('word/media/'):
                        image_count += 1
                        # 获取文件扩展名
                        _, ext = os.path.splitext(file_info.filename)
                        if not ext:
                            ext = '.png'
                        
                        # 生成新文件名
                        new_filename = f"image_{image_count:03d}{ext}"
                        image_path = os.path.join(self.image_dir, new_filename)
                        
                        # 提取并保存图片
                        with docx_zip.open(file_info.filename) as source:
                            with open(image_path, 'wb') as target:
                                shutil.copyfileobj(source, target)
                        
                        print(f"提取图片: {new_filename}")
        except Exception as e:
            print(f"提取图片时出错: {e}")
        
        return image_count
    
    def convert_table_to_markdown(self, table):
        """将表格转换为Markdown格式"""
        if not table.rows:
            return ""
        
        markdown_lines = []
        
        for row_idx, row in enumerate(table.rows):
            cells = []
            for cell in row.cells:
                # 处理单元格内容
                cell_text = cell.text.replace('\n', '<br>').replace('|', '\\|').strip()
                cells.append(cell_text)
            
            # 添加表格行
            markdown_lines.append(f"| {' | '.join(cells)} |")
            
            # 在第一行后添加分隔符
            if row_idx == 0:
                separator = ['---'] * len(cells)
                markdown_lines.append(f"| {' | '.join(separator)} |")
        
        return '\n'.join(markdown_lines)
    
    def process_paragraph(self, paragraph):
        """处理段落，返回Markdown格式的文本"""
        text = paragraph.text.strip()
        if not text:
            return ""
        
        # 处理标题
        style_name = paragraph.style.name
        if style_name.startswith('Heading'):
            try:
                level = int(style_name.split()[-1])
                return f"{'#' * level} {text}"
            except:
                return f"## {text}"
        
        # 处理列表项（简单检测）
        if text.startswith('•') or text.startswith('-') or text.startswith('*'):
            return f"- {text.lstrip('•-* ')}"
        
        # 检查是否为编号列表
        if re.match(r'^\d+\.', text):
            return f"1. {text[text.find('.')+1:].strip()}"
        
        # 处理普通段落
        return text
    
    def convert_to_markdown(self, output_filename="document.md"):
        """转换为Markdown"""
        print("开始转换文档...")
        
        # 提取图片
        image_count = self.extract_images()
        print(f"提取了 {image_count} 张图片")
        
        markdown_content = []
        
        # 添加文档标题
        doc_name = os.path.splitext(os.path.basename(self.docx_path))[0]
        markdown_content.append(f"# {doc_name}")
        markdown_content.append("")
        
        # 处理文档内容
        current_image_index = 0
        
        for element in self.doc.element.body:
            if element.tag.endswith('p'):  # 段落
                # 找到对应的段落对象
                for para in self.doc.paragraphs:
                    if para._element == element:
                        processed_text = self.process_paragraph(para)
                        if processed_text:
                            markdown_content.append(processed_text)
                            markdown_content.append("")
                        break
            
            elif element.tag.endswith('tbl'):  # 表格
                # 找到对应的表格对象
                for table in self.doc.tables:
                    if table._tbl == element:
                        table_md = self.convert_table_to_markdown(table)
                        if table_md:
                            markdown_content.append(table_md)
                            markdown_content.append("")
                        break
        
        # 如果有图片，在文档末尾添加图片引用
        if image_count > 0:
            markdown_content.append("## 相关图片")
            markdown_content.append("")
            for i in range(1, image_count + 1):
                image_name = f"image_{i:03d}"
                # 尝试不同的扩展名
                for ext in ['.png', '.jpg', '.jpeg', '.gif']:
                    if os.path.exists(os.path.join(self.image_dir, image_name + ext)):
                        markdown_content.append(f"![图片{i}](images/{image_name}{ext})")
                        break
            markdown_content.append("")
        
        # 清理多余的空行
        cleaned_content = []
        prev_empty = False
        for line in markdown_content:
            if line.strip() == "":
                if not prev_empty:
                    cleaned_content.append("")
                prev_empty = True
            else:
                cleaned_content.append(line)
                prev_empty = False
        
        # 保存文件
        output_path = os.path.join(self.output_dir, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(cleaned_content))
        
        print(f"✅ 转换完成!")
        print(f"📄 输出文件: {output_path}")
        print(f"🖼️  图片目录: {self.image_dir}")
        
        return output_path


def main():
    """主函数"""
    input_file = "test.docx"
    output_dir = "simple_output"
    
    if not os.path.exists(input_file):
        print(f"❌ 错误：找不到文件 {input_file}")
        return
    
    # 创建转换器
    converter = SimpleDocxConverter(input_file, output_dir)
    
    # 执行转换
    try:
        result = converter.convert_to_markdown("告警处理手册.md")
        
        # 显示统计信息
        print(f"\n📊 文档统计:")
        print(f"   - 段落数: {len(converter.doc.paragraphs)}")
        print(f"   - 表格数: {len(converter.doc.tables)}")
        
        # 显示文件大小
        if os.path.exists(result):
            size = os.path.getsize(result)
            print(f"   - 输出文件大小: {size} 字节")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
