# Word文档到Markdown转换工具使用说明

## 🎯 转换效果

经过优化的转换器现在能够完美处理告警参考文档的格式要求：

### ✅ 已实现的功能

1. **有序列表处理** - 正确识别并转换为 `1.` 格式
2. **无序列表处理** - 转换为 `-` 格式  
3. **多级缩进** - 子列表项正确缩进
4. **补充说明缩进** - 列表项的说明文本正确缩进到同一级别
5. **代码块识别** - 自动识别命令行内容并格式化为代码块
6. **表格转换** - 完整保持表格结构
7. **图片提取** - 自动提取并引用图片
8. **标题层级** - 保持原文档的标题层级结构

## 🚀 快速使用

### 推荐方法（python-docx优化版）

```bash
# 转换单个文档
python docx_converter_suite.py test.docx -m 1 -o output_folder

# 或直接使用增强转换器
python enhanced_doc_converter.py
```

### 转换结果示例

**原Word文档格式：**
```
处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 执行以下命令：
   su - root
   默认密码："*****"。
```

**转换后Markdown格式：**
```markdown
#### 处理步骤

1. 登录FusionSphere OpenStack安装部署界面。
   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
1. 执行以下命令：
   ```
   su - root
   ```
   默认密码："*****"。
```

## 📁 输出文件结构

```
output_folder/
├── result_docx.md          # 转换后的Markdown文件
└── images/                 # 提取的图片文件
    ├── image_001.png
    ├── image_002.png
    └── ...
```

## 🔧 转换器特性

### 智能列表识别
- 自动识别Word文档中的有序列表和无序列表
- 正确处理多级列表的层级关系
- 智能清理原始列表标记，避免重复

### 上下文感知缩进
- 根据上下文自动判断段落是否需要缩进
- 列表项的补充说明自动缩进到正确位置
- 支持多级缩进结构

### 代码块自动识别
转换器能自动识别以下类型的代码内容：
- 系统命令：`su - root`, `TMOUT=0`, `source set_env`
- CPS命令：`cpssafe`, `cps template-instance-list`
- 日志命令：`log policy-get`, `log policy-set`
- 以特殊字符开头的命令：`$`, `#`, `>`

### 表格完整转换
- 保持原表格的行列结构
- 处理单元格内的换行（转换为 `<br>`）
- 自动转义表格中的特殊字符

## 📊 转换质量对比

| 特性 | 原版本 | 优化版本 |
|------|--------|----------|
| 有序列表 | ❌ 显示为无序列表 | ✅ 正确显示为 `1.` |
| 缩进处理 | ❌ 缩进不正确 | ✅ 智能上下文缩进 |
| 代码识别 | ❌ 无代码块 | ✅ 自动识别并格式化 |
| 多级列表 | ❌ 层级混乱 | ✅ 完美保持层级 |
| 补充说明 | ❌ 格式错乱 | ✅ 正确缩进对齐 |

## 🛠️ 自定义配置

如需调整转换行为，可以修改 `enhanced_doc_converter.py` 中的以下设置：

### 缩进设置
```python
# 修改缩进字符数（默认3个空格）
indent = '   ' * style_info['list_level']  # 可改为 '  ' 或 '    '
```

### 代码块识别规则
```python
# 在 _is_code_block 方法中添加新的识别规则
code_indicators = [
    'su - root',
    'TMOUT=0',
    # 添加您的自定义命令
    'your_custom_command'
]
```

### 上下文缩进规则
```python
# 在 _should_indent 方法中添加新的缩进触发条件
if text.startswith(('具体请参见', '默认', '您的自定义前缀')):
    return True
```

## 🔍 故障排除

### 常见问题

**Q: 列表格式不正确？**
A: 检查Word文档中是否使用了标准的列表格式。如果使用了自定义样式，可能需要调整识别规则。

**Q: 缩进不对齐？**
A: 确保Word文档中的缩进是通过列表级别设置的，而不是手动空格。

**Q: 代码块识别错误？**
A: 可以在 `_is_code_block` 方法中添加或修改识别规则。

**Q: 图片无法显示？**
A: 检查 `images/` 目录是否存在，以及图片路径是否正确。

## 📝 最佳实践

1. **文档准备**：确保Word文档使用标准的列表格式和样式
2. **批量转换**：可以修改脚本支持批量处理多个文档
3. **后期调整**：转换后可以手动微调特殊格式
4. **版本控制**：建议将转换后的Markdown文件纳入版本控制

## 🎉 总结

优化后的转换器完美解决了您提出的需求：
- ✅ 处理步骤使用有序符号（1. 2. 3.）
- ✅ 无序符号内容正确缩进到对应有序符号位置
- ✅ 保持表格、图片和段落格式
- ✅ 智能识别代码块并格式化

现在您可以放心使用这个工具来转换告警参考文档了！
