# 文档转换结果

# 华为云Stack告警处理参考（测试版）

5. ******* ALM-6008 上传日志到OBS服务失败
   告警解释
   OpenStack周期（默认为300秒）检查上一次上传日志至OBS服务是否成功，当检查到上传失败时，产生此告警。
   告警属性
   告警参数
   对系统的影响
   日志无法上传至OBS服务，如果未设置本地备份，则会导致该次日志丢失。
   可能原因
   主机与OBS服务网络连接异常。
   Apacheproxy服务状态异常。
   日志配置文件中OBS服务IP、端口未正确设置。
OBS服务上指定的日志上传空间已满。

处理步骤

登录FusionSphere OpenStack安装部署界面。

   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。

使用PuTTY，通过主机的管理IP地址，登录告警所在主机。

   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

```
su - root
```

   默认密码：“*****”。
   执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
   执行以下命令，导入环境变量。
   ```
   source set_env
   ```
   回显如下类似信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   (4) openstack environment variable of cloud_admin (keystone v3)
   please choose:[1|2|3|4]
   输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
   默认密码为：*****。
   Apacheproxy服务状态异常
执行如下操作查看apacheproxy服务是否正常。

执行如下命令，采用安全方式操作。

```
cpssafe
```

   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入“1”，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   执行如下命令，查看apacheproxy服务是否正常。
   ```
   cps template-instance-list --service apacheproxy apacheproxy
   ```
   在apacheproxy主备部署的情况下，回显如下类似信息：
   在apacheproxy单实例部署的情况下，回显如下类似信息：
   如服务状态显示为fault，执行9。
   如未显示为fault，执行12。
   执行如下操作停止apacheproxy服务。
   执行如下命令，采用安全方式操作。
   ```
   cpssafe
   ```
   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入“1”，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   运行如下命令停止apacheproxy服务。
   ```
   cps host-template-instance-operate --service apacheproxy apacheproxy --action stop
   ```
   回显如下类似信息：
   查看操作结果是否为success。
   是，执行10。
   否，执行17。
   执行如下操作启动apacheproxy服务。
   执行如下命令，采用安全方式操作。
   ```
   cpssafe
   ```
   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入1，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   执行如下命令启动apacheproxy服务。
   ```
   cps host-template-instance-operate --service apacheproxy apacheproxy --action start
   ```
   回显如下类似信息：
   查看操作结果是否为success。
   是，执行11。
   否，执行17。
   执行如下操作查看apacheproxy服务是否正常。
   执行如下命令，采用安全方式操作。
   ```
   cpssafe
   ```
   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入“1”，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   执行如下命令查看apacheproxy服务是否正常。
   ```
   cps template-instance-list --service apacheproxy apacheproxy
   ```
   在apacheproxy主备部署的情况下，回显如下类似信息：
   在apacheproxy单实例部署的情况下，回显如下类似信息：
   如未显示fault，执行12。
   如仍显示为fault，执行17。
   日志配置文件中OBS服务IP、端口未正确设置
   执行以下命令，获取配置的OBS信息，确认OBS日志服务地址是否正确。
   ```
   log policy-get
   ```
   ```
   42174775-2DA8-B93F-CF47-47902E7AA2B0:~ # log policy-get
   ```
   +-----------------------------+---------------------------------+
   | Property                    |                  Value          |
   +-----------------------------+---------------------------------+
   | policy_gateway              |                                 |
   | policy_s3_access_key        |  92514a3e0b5a4acbb532aba9638    |
   | policy_s3_export_begin      |                   2             |
   | policy_s3_export_end        |                   3             |
   | policy_s3_host              |  s3.dc1.domainname.com:5443    |
   | policy_s3_operate_bucket    |                                 |
   | policy_s3_operate_lifecycle |                   4             |
   | policy_s3_region            |                                 |
   | policy_s3_run_bucket        |                                 |
   | policy_s3_run_lifecycle     |                   90            |
   | policy_s3_scheme            |                                 |
   | policy_s3_secret_key        |   3d99c09e38614b31930e349ec638  |
   +-----------------------------+---------------------------------+
是，请执行14。

否，请执行13。

   执行如下命令重新设置OBS服务地址(policy_s3_host)，AK(policy_s3_access_key)/SK(policy_s3_secret_key)等信息。
执行如下命令，采用安全方式操作。

```
cpssafe
```

   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入“1”，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   执行如下命令重新设置OBS服务地址(policy_s3_host)，AK(policy_s3_access_key)/SK(policy_s3_secret_key)等信息。
   ```
   log policy-set --parameter policy_s3_access_key=ACCESS_KEY policy_s3_secret_key=SECRET_KEY policy_s3_host=S3_HOST
   ```
   查看是否回显如下类似信息：
   +----------------------+----------------------------------+ 
     | Property             | Value                            | 
     +----------------------+----------------------------------+ 
     | policy_s3_access_key | 92514a3e0b5a4acbb532aba957a9d66f | 
     | policy_s3_host       | s3.dc1.domainname.com:5443       | 
     | policy_s3_secret_key | 3d99c09e38614b31930e349ec63842a3 | 
     +----------------------+----------------------------------+
   是，执行14。
   否，执行17。
执行如下操作手动触发日志上传。

执行如下命令，采用安全方式操作。

```
cpssafe
```

   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入“1”，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   执行如下命令手动触发日志上传。
   ```
   log log-flush --host 主机ID
   ```
   等待5分钟，执行如下操作查看各组件的日志上传状态。
   执行如下命令，采用安全方式操作。
   ```
   cpssafe
   ```
   显示如下信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   please choose:[1|2|3]
   输入“1”，选择使用keystone v3鉴权。
   显示如下信息：
   ```
   Input command:
   ```
   执行如下命令查看各组件的日志上传状态。
   ```
   log log-state-get --host 主机ID
   ```
   状态字段显示为上传时间，执行16。
   存在状态字段显示为“log flushing”，重新执行15。
   存在状态字段显示为“not flushed yet”，转17。
   等待1分钟查看告警是否恢复。
   是，处理完毕。
   否，执行17。
   请联系技术支持工程师协助解决。
   参考信息
   无。
   父主题： FusionSphere OpenStack告警参考 
版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

下一节 > 

5. ******* ALM-6010 NTP服务器与外部时钟源时间差超过阈值
   告警解释
   在配置外部时钟源之后，ntp-server（NTP服务器）会周期性（默认为2min）检查主ntp-server所在节点与外部时钟源的时间差（如果存在多个外部时钟源，则逐一检查），当任意一个时间差超过阈值时，产生此告警。
   告警属性
   告警参数
   对系统的影响
   如果本地主ntp-server所在节点与外部时钟源时间差超过阈值，则不与外部时钟源进行时间同步。
   可能原因
   本地主ntp-server所在节点时间被修改。
   外部时钟源时间发生跳变。
   第一次跟外部时钟源对接时，用户没有触发强制同步操作。
配置了多个外部时钟源，ntp-server和某个外部时钟源时差超过阈值。

处理步骤

登录FusionSphere OpenStack安装部署界面。

   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
选择“运维”，进入“日常维护”。

稍等2分钟，在“时间同步”标签下，提示由“系统时间状态查询中”变为“系统时间异常”，如下图所示。请根据界面提示查看详情，获取上报告警主机所对应的时间差。

选择“配置”，进入“系统”。打开“NTP”标签，查看已配置的外部时钟源信息，如下图所示。

使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。

   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：

Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。

Region Type II和Type III场景：ExternalOM-Reverse-Proxy。

执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

```
su - root
```

   默认密码：“*****”。
   执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
   执行以下命令，导入环境变量。
   ```
   source set_env
   ```
   回显如下类似信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   (4) openstack environment variable of cloud_admin (keystone v3)
   please choose:[1|2|3|4]
   输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
   默认密码为：*****。
   执行如下命令，获取ntp-server部署情况，包含节点id信息，回显结果中runsonhost属性的值。
```
cps template-instance-list --service ntp ntp-server
```

   回显如下类似信息：
    +------------+----------------+--------+------------+----------------+
    | instanceid | componenttype  | status |runsonhost  |omip            |
    +------------+----------------+--------+------------+----------------+
    | 1          | ntp-server     | active | Mgmt1      |***********     |
    | 0          | ntp-server     | standby| Mgmt3      |************    |
    +------------+----------------+--------+------------+----------------+
   回显信息数目取决于ntp-server的部署方式，status显示active字段的即为主NTP Server所在的节点。
   根据获取到的IP信息，登录主ntp-server部署所在节点，用户名与密码参照5～6。
执行以下命令，确认是否和每台服务器之间的系统时间差都小于阈值。

ntpdate -d 外部时钟源IP

   42174775-2DA8-B93F-CF47-47902E7AA2B0:~ # ntpdate -d **************
24 Jul 19:11:52 ntpdate[5469]: ntpdate 4.2.6p5@1.2349-o Thu Dec 20 00:00:00 UTC 2018 (1)

Looking for host ************** and service ntp

host found : **************

transmit(**************)

receive(**************)

transmit(**************)

receive(**************)

transmit(**************)

receive(**************)

transmit(**************)

receive(**************)

server **************, port 123

stratum 11, precision -24, leap 00, trust 000

refid [**************], delay 0.02592, dispersion 0.00005

transmitted 4, in filter 4

reference time:    e0e2bb54.e30f606d  Wed, Jul 24 2019 19:11:16.886

originate timestamp: e0e2bb7f.e6f1a121  Wed, Jul 24 2019 19:11:59.902

transmit timestamp:  e0e2bb7f.0a6cb785  Wed, Jul 24 2019 19:11:59.040

filter delay:  0.02679  0.02612  0.02629  0.02592

         0.00000  0.00000  0.00000  0.00000

filter offset: 0.861150 0.860937 0.860968 0.861003

         0.000000 0.000000 0.000000 0.000000

delay 0.02592, dispersion 0.00005

offset 0.861003

24 Jul 19:11:59 ntpdate[5469]: step time server ************** offset 0.861003 sec

是，请联系技术支持工程师协助解决。

否，执行13。

检查是否存在不准确的时钟源。

是，移除不准确的外部时钟源服务器配置，并执行14。

否，请联系技术支持工程师协助解决。

再次执行3，查看提示是否由“系统时间状态查询中”变为“当前系统时间正常”，如图所示。

是，执行15。

否，请联系技术支持工程师协助解决。

等待5分钟～10分钟，查看告警是否清除。

是，处理完毕。

否，请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 

5. ******* ALM-6014 DNS服务器连接中断
   告警解释
   OpenStack周期（默认为1分钟）采集DNS Server对外连接状态，当连接中断时产生此告警。
   告警属性
   告警参数
   对系统的影响
   如果DNS Server对外连接中断，就无法访问外部的域名服务器，导致跨AZ的域名解析失败。
   可能原因
   网络故障。
   外部域名服务器故障。
   处理步骤
使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。

   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：

Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。

Region Type II和Type III场景：ExternalOM-Reverse-Proxy。

执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

```
su - root
```

   默认密码：“*****”。
   执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
   执行以下命令，导入环境变量。
   ```
   source set_env
   ```
   回显如下类似信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   (4) openstack environment variable of cloud_admin (keystone v3)
   please choose:[1|2|3|4]
   输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
   默认密码为：*****。
   执行如下操作获取外部域名服务器地址。
cps template-params-show --service dns dns-server

返回结果：

     +----------+------------------------------------------+ 
     | Property | Value                                    | 
     +----------+------------------------------------------+ 
     | address  | /az1.dc1.domainname.com/**************   | 
     | network  | [{"ip": "***************", "systeminterf | 
     |          | ace": "external_api", "mask": "24", "gat | 
     |          | eway": "*************"}]                 | 
     | server   | /#/***************#53@external_api       | 
     +----------+------------------------------------------+     
返回结果中的***************就是外部域名服务器对应的ip地址。

执行如下命令，检查与外部域名服务器之间的网络是否正常。

ping 外部域名服务器地址

能够ping通，执行8。

不能ping通，执行10。

检查使用外部DNS服务器是否使用bind套件实现。

是，执行9。

否，执行11。

在zone配置文件中，增加“com”域配置。

例如：

zone "com" in 

{ 

  type master; 

  file "named.empty"; 

  };     

处理完毕。

联系网络管理员排查网络故障。网络故障消除后，检查告警是否恢复。

是，执行结束。

否，执行11。

联系第三方DNS服务器管理人员，确认DNS是否存在故障。

是，执行12。

否，执行13。

联系第三方DNS服务器管理人员消除第三方DNS服务器故障。DNS故障消除后，检查告警是否恢复。

是，执行结束。

否，执行13。

请联系技术支持工程师协助解决。

参考信息

如果恢复第三方DNS服务器之后，由于DNS缓存，导致FusionSphere OpenStack系统DNS解析功能未恢复，请参考如何清理DNS缓存章节清理FusionSphere OpenStack系统中的DNS缓存。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
