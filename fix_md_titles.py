#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MD文件中的标题格式
检查指定名词是否为标题，如果不是则改为五级标题
"""

import os
import re
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_md_titles.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MDTitleFixer:
    def __init__(self, directory="optimized_batch_results"):
        self.directory = directory
        
        # 需要检查的关键词
        self.keywords = [
            "告警解释",
            "告警属性", 
            "告警参数",
            "对系统的影响",
            "可能原因",
            "处理步骤"
        ]
        
        logger.info(f"初始化MD标题修复器")
        logger.info(f"目标目录: {directory}")
        logger.info(f"检查关键词: {', '.join(self.keywords)}")
    
    def is_title_line(self, line):
        """判断是否已经是标题行"""
        stripped_line = line.strip()
        # 检查是否以#开头（任意级别的标题）
        return stripped_line.startswith('#')
    
    def contains_keyword(self, line):
        """检查行是否包含需要检查的关键词"""
        stripped_line = line.strip()
        for keyword in self.keywords:
            if keyword in stripped_line:
                return keyword
        return None
    
    def fix_line(self, line, keyword):
        """修复行，将其改为五级标题"""
        stripped_line = line.strip()
        
        # 如果已经是标题，不修改
        if self.is_title_line(line):
            return line
        
        # 改为五级标题
        fixed_line = f"##### {stripped_line}\n"
        return fixed_line
    
    def process_file(self, file_path):
        """处理单个MD文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            modified = False
            new_lines = []
            
            for i, line in enumerate(lines):
                keyword = self.contains_keyword(line)
                
                if keyword:
                    # 检查是否已经是标题
                    if not self.is_title_line(line):
                        # 不是标题，需要修复
                        fixed_line = self.fix_line(line, keyword)
                        new_lines.append(fixed_line)
                        modified = True
                        logger.info(f"  修复第{i+1}行: '{line.strip()}' -> '##### {line.strip()}'")
                    else:
                        # 已经是标题，保持不变
                        new_lines.append(line)
                        logger.debug(f"  第{i+1}行已是标题，跳过: '{line.strip()}'")
                else:
                    # 不包含关键词，保持不变
                    new_lines.append(line)
            
            # 如果有修改，写回文件
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                logger.info(f"  ✅ 文件已修复并保存")
                return True
            else:
                logger.info(f"  ✅ 文件无需修复")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ 处理文件时出错: {e}")
            return False
    
    def process_directory(self):
        """处理目录下的所有MD文件"""
        if not os.path.exists(self.directory):
            logger.error(f"❌ 目录不存在: {self.directory}")
            return
        
        # 查找所有MD文件
        md_files = []
        for root, dirs, files in os.walk(self.directory):
            for file in files:
                if file.lower().endswith('.md'):
                    md_files.append(os.path.join(root, file))
        
        if not md_files:
            logger.warning(f"⚠️  在目录 {self.directory} 中未找到MD文件")
            return
        
        logger.info(f"🔍 找到 {len(md_files)} 个MD文件")
        
        # 处理每个文件
        processed_count = 0
        modified_count = 0
        
        for file_path in md_files:
            relative_path = os.path.relpath(file_path, self.directory)
            logger.info(f"\n📄 处理文件: {relative_path}")
            
            try:
                was_modified = self.process_file(file_path)
                processed_count += 1
                if was_modified:
                    modified_count += 1
                    
            except Exception as e:
                logger.error(f"❌ 处理文件 {relative_path} 时出错: {e}")
                continue
        
        # 输出总结
        logger.info(f"\n" + "="*60)
        logger.info(f"📊 处理完成总结")
        logger.info(f"="*60)
        logger.info(f"📁 处理目录: {self.directory}")
        logger.info(f"📄 总文件数: {len(md_files)}")
        logger.info(f"✅ 成功处理: {processed_count}")
        logger.info(f"🔧 需要修复: {modified_count}")
        logger.info(f"📈 成功率: {processed_count/len(md_files)*100:.1f}%")
        
        if modified_count > 0:
            logger.info(f"\n🎉 已修复 {modified_count} 个文件的标题格式")
        else:
            logger.info(f"\n✨ 所有文件的标题格式都正确，无需修复")
    
    def preview_changes(self):
        """预览将要进行的更改（不实际修改文件）"""
        logger.info("🔍 预览模式：检查需要修复的内容...")
        
        if not os.path.exists(self.directory):
            logger.error(f"❌ 目录不存在: {self.directory}")
            return
        
        # 查找所有MD文件
        md_files = []
        for root, dirs, files in os.walk(self.directory):
            for file in files:
                if file.lower().endswith('.md'):
                    md_files.append(os.path.join(root, file))
        
        if not md_files:
            logger.warning(f"⚠️  在目录 {self.directory} 中未找到MD文件")
            return
        
        logger.info(f"🔍 找到 {len(md_files)} 个MD文件")
        
        total_issues = 0
        
        for file_path in md_files:
            relative_path = os.path.relpath(file_path, self.directory)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                file_issues = []
                
                for i, line in enumerate(lines):
                    keyword = self.contains_keyword(line)
                    
                    if keyword and not self.is_title_line(line):
                        file_issues.append({
                            'line_num': i + 1,
                            'original': line.strip(),
                            'keyword': keyword
                        })
                
                if file_issues:
                    logger.info(f"\n📄 {relative_path}:")
                    for issue in file_issues:
                        logger.info(f"  第{issue['line_num']}行: '{issue['original']}' -> '##### {issue['original']}'")
                    total_issues += len(file_issues)
                    
            except Exception as e:
                logger.error(f"❌ 读取文件 {relative_path} 时出错: {e}")
        
        logger.info(f"\n📊 预览总结: 共发现 {total_issues} 处需要修复的标题")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='修复MD文件中的标题格式')
    parser.add_argument('--directory', '-d', default='optimized_batch_results', 
                       help='要处理的目录路径 (默认: optimized_batch_results)')
    parser.add_argument('--preview', '-p', action='store_true', 
                       help='预览模式，只显示需要修复的内容，不实际修改文件')
    
    args = parser.parse_args()
    
    # 创建修复器
    fixer = MDTitleFixer(args.directory)
    
    if args.preview:
        # 预览模式
        fixer.preview_changes()
    else:
        # 实际修复模式
        logger.info("🚀 开始修复MD文件标题格式...")
        fixer.process_directory()


if __name__ == "__main__":
    main()
