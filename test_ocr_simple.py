#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的OCR测试脚本
"""

from enhanced_doc_converter import EnhancedDocxToMarkdown
import os

def test_ocr_simple():
    """简单测试OCR功能"""
    input_file = "test.docx"
    output_dir = "simple_ocr_test"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    # 创建转换器
    converter = EnhancedDocxToMarkdown(input_file, output_dir)
    
    # 只提取图片和OCR
    print("提取图片并进行OCR识别...")
    image_files = converter.extract_images()
    
    print(f"\n提取了 {len(image_files)} 张图片")
    print("\nOCR结果:")
    print("=" * 50)
    
    for i, (original_name, image_info) in enumerate(converter.image_map.items(), 1):
        print(f"\n图片 {i}: {image_info['filename']}")
        print(f"原始名称: {original_name}")
        
        if image_info.get('ocr_text'):
            print(f"OCR识别内容:")
            print("-" * 30)
            print(image_info['ocr_text'])
            print("-" * 30)
        else:
            print("OCR识别失败或为空")

if __name__ == "__main__":
    test_ocr_simple()
