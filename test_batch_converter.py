#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量转换器
"""

from optimized_batch_converter import OptimizedBatchConverter

def test_batch_converter():
    """测试批量转换器"""
    print("=== 测试批量转换器 ===")
    
    # 创建转换器
    converter = OptimizedBatchConverter("华为云Stack告警处理参考.docx", "test_batch_output")
    
    # 查找章节
    chapters = converter.find_all_alarm_chapters()
    
    print(f"找到 {len(chapters)} 个章节")
    
    # 只转换前3个章节进行测试
    test_chapters = chapters[:3]
    
    for i, chapter in enumerate(test_chapters):
        print(f"\n转换第 {i+1} 个章节:")
        print(f"  标题: {chapter['title']}")
        print(f"  告警代码: {chapter['alarm_code']}")
        print(f"  告警名称: {chapter['alarm_name']}")
        
        try:
            output_path = converter.convert_chapter_optimized(chapter)
            if output_path:
                print(f"  ✅ 转换成功: {output_path}")
            else:
                print(f"  ❌ 转换失败")
        except Exception as e:
            print(f"  ❌ 转换出错: {e}")

if __name__ == "__main__":
    test_batch_converter()
