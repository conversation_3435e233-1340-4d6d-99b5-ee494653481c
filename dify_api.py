import requests
import json

# API 配置

## 查询知识库列表
url = "http://localhost/v1/datasets"

## 查询知识库中的文档列表
url = "http://localhost/v1/datasets/06ea8a7b-17f1-48d5-9e9f-53c0c6caace8/documents"

## 查询知识库元数据列表
#url =  'http://localhost/v1/datasets/06ea8a7b-17f1-48d5-9e9f-53c0c6caace8/metadata'


##
params = {
    "page": 1,
    "limit": 100
}

headers = {
    "Authorization": "Bearer dataset-dyT48T6t6lrkKq06EYv3qlKV",
    "Accept": "application/json; charset=utf-8"  # 明确要求 UTF-8 编码
}

try:
    # 发送 GET 请求
    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()  # 检查请求是否成功

    # 解析 JSON 响应（自动处理 Unicode 转义符）
    data = response.json()

    data = json.dumps(data, indent=2, ensure_ascii=False)


    # 美化打印结果（ensure_ascii=False 确保中文正常显示）
    print(data)


except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
except json.JSONDecodeError as e:
    print(f"JSON 解析失败: {e}")