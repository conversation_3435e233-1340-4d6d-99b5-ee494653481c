#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接批量告警文档转换器
不保存中间文档，直接转换为Markdown
"""

import os
import re
import time
from datetime import datetime
from docx import Document
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('direct_batch_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DirectBatchConverter:
    def __init__(self, source_docx, output_dir="direct_batch_results"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"初始化直接批量转换器")
        logger.info(f"源文档: {source_docx}")
        logger.info(f"输出目录: {output_dir}")
    
    def find_all_alarm_chapters(self):
        """查找所有告警章节"""
        logger.info("🔍 开始查找所有告警章节...")
        
        chapters = []
        doc = Document(self.source_docx)
        
        # 定义支持的章节格式
        patterns = [
            (r'^5\.2\.3\.1\.\d+\s+ALM-\d+', '*******', 'ALM告警'),
            (r'^5\.2\.3\.2\.\d+\s+ALM-\d+', '*******', 'Service OM告警'),
            (r'^5\.2\.4\.1\.\d+\s+ALM-\d+', '*******', '其他告警'),
            (r'^5\.2\.4\.\d+\.\d+\s+ALM-\d+', '5.2.4.x', '其他告警'),
            (r'^5\.\d+\.\d+\.\d+\.\d+\s+ALM-\d+', '5.x.x.x', '其他告警')
        ]
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            # 检查每种模式
            for pattern, section_prefix, section_type in patterns:
                if re.match(pattern, text):
                    # 提取告警代码
                    alarm_code = self.extract_alarm_code(text, section_type)
                    
                    if alarm_code:
                        # 提取告警名称
                        alarm_name = self.extract_alarm_name(text, alarm_code)
                        
                        chapters.append({
                            'title': text,
                            'alarm_code': alarm_code,
                            'alarm_name': alarm_name,
                            'section_type': section_type,
                            'section_prefix': section_prefix,
                            'start_para': i,
                            'style': paragraph.style.name
                        })
                        
                        if len(chapters) % 50 == 0:
                            logger.info(f"  已找到 {len(chapters)} 个告警章节...")
                    break
            
            # 如果遇到下一个主要章节，停止搜索
            if (text.startswith('5.3') or 
                text.startswith('6.') or
                text.startswith('7.')):
                logger.info(f"  遇到非告警章节: {text[:50]}...")
                logger.info(f"  停止搜索，所有告警章节已全部找到")
                break
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                # 最后一个章节的结束位置
                end_para = len(doc.paragraphs)
                for j in range(chapter['start_para'] + 1, len(doc.paragraphs)):
                    para_text = doc.paragraphs[j].text.strip()
                    if (para_text.startswith('5.3') or 
                        para_text.startswith('6.') or 
                        para_text.startswith('7.')):
                        end_para = j
                        break
                chapter['end_para'] = end_para
        
        # 统计
        stats = {}
        for chapter in chapters:
            section_type = chapter['section_type']
            stats[section_type] = stats.get(section_type, 0) + 1
        
        logger.info(f"✅ 找到告警章节总计: {len(chapters)} 个")
        for section_type, count in stats.items():
            logger.info(f"   - {section_type}: {count} 个")
        
        return chapters
    
    def extract_alarm_code(self, text, section_type):
        """提取告警代码"""
        if section_type == 'ALM告警':
            match = re.search(r'ALM-\d+', text)
            return match.group() if match else None
        elif section_type == 'Service OM告警':
            match = re.search(r'^(5\.2\.3\.2\.\d+)', text)
            if match:
                return f"SOM-{match.group(1).replace('.', '-')}"
            return None
        elif section_type == '其他告警':
            # 支持多种格式
            match = re.search(r'^(5\.2\.4\.1\.\d+)', text)
            if match:
                return f"OTHER-{match.group(1).replace('.', '-')}"
            
            match = re.search(r'^(5\.2\.4\.\d+\.\d+)', text)
            if match:
                return f"OTHER-{match.group(1).replace('.', '-')}"
            
            match = re.search(r'^(5\.\d+\.\d+\.\d+\.\d+)', text)
            if match:
                return f"OTHER-{match.group(1).replace('.', '-')}"
            
            return None
        return None
    
    def extract_alarm_name(self, text, alarm_code):
        """提取告警名称"""
        if alarm_code.startswith('ALM-'):
            match = re.search(r'ALM-\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"
        elif alarm_code.startswith('SOM-'):
            match = re.search(r'^5\.2\.3\.2\.\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"
        elif alarm_code.startswith('OTHER-'):
            # 支持多种格式
            match = re.search(r'^5\.2\.4\.1\.\d+\s+(.+)', text)
            if match:
                return match.group(1)
            
            match = re.search(r'^5\.2\.4\.\d+\.\d+\s+(.+)', text)
            if match:
                return match.group(1)
            
            match = re.search(r'^5\.\d+\.\d+\.\d+\.\d+\s+(.+)', text)
            if match:
                return match.group(1)
            
            return "未知告警"
        return "未知告警"
    
    def extract_chapter_content(self, chapter):
        """直接提取章节内容并转换为Markdown"""
        try:
            doc = Document(self.source_docx)
            start_para = chapter['start_para']
            end_para = chapter['end_para']
            
            logger.info(f"     提取段落范围: {start_para} - {end_para} (共{end_para - start_para}个段落)")
            
            # 直接提取文本内容
            content_lines = []
            
            # 添加标题
            content_lines.append(f"# {chapter['alarm_code']} {chapter['alarm_name']}")
            content_lines.append("")
            
            # 提取段落内容
            current_table = None
            
            for i in range(start_para, min(end_para, len(doc.paragraphs))):
                para = doc.paragraphs[i]
                text = para.text.strip()
                
                if not text:
                    content_lines.append("")
                    continue
                
                # 检查是否是标题
                if self.is_heading(para):
                    level = self.get_heading_level(para)
                    content_lines.append(f"{'#' * level} {text}")
                    content_lines.append("")
                else:
                    # 普通段落
                    content_lines.append(text)
                    content_lines.append("")
            
            # 处理表格（简化版）
            try:
                body_elements = list(doc.element.body)
                for i, element in enumerate(body_elements):
                    if element.tag.endswith('tbl'):
                        # 找到对应的表格
                        for table in doc.tables:
                            if table._tbl == element:
                                content_lines.append("")
                                content_lines.append(self.convert_table_to_markdown(table))
                                content_lines.append("")
                                break
            except Exception as e:
                logger.warning(f"     处理表格时出错: {e}")
            
            return "\n".join(content_lines)
            
        except Exception as e:
            logger.error(f"提取章节内容时出错: {e}")
            return None
    
    def is_heading(self, paragraph):
        """判断是否是标题"""
        style_name = paragraph.style.name.lower()
        return ('heading' in style_name or 
                paragraph.text.strip().startswith(('##', '###', '####')))
    
    def get_heading_level(self, paragraph):
        """获取标题级别"""
        style_name = paragraph.style.name.lower()
        if 'heading 1' in style_name:
            return 2
        elif 'heading 2' in style_name:
            return 3
        elif 'heading 3' in style_name:
            return 4
        else:
            return 2
    
    def convert_table_to_markdown(self, table):
        """将表格转换为Markdown格式"""
        try:
            if not table.rows:
                return ""
            
            lines = []
            
            # 处理表头
            if table.rows:
                header_cells = []
                for cell in table.rows[0].cells:
                    header_cells.append(cell.text.strip() or " ")
                lines.append("| " + " | ".join(header_cells) + " |")
                lines.append("| " + " | ".join(["---"] * len(header_cells)) + " |")
            
            # 处理数据行
            for row in table.rows[1:]:
                data_cells = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', '<br>')
                    data_cells.append(cell_text or " ")
                lines.append("| " + " | ".join(data_cells) + " |")
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.warning(f"转换表格时出错: {e}")
            return ""
    
    def generate_safe_filename(self, chapter):
        """生成安全的文件名"""
        alarm_code = chapter['alarm_code']
        alarm_name = chapter['alarm_name']
        
        # 清理文件名中的非法字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', alarm_name)
        safe_name = safe_name.strip()
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]
        
        return f"{alarm_code}_{safe_name}"
    
    def convert_chapter_directly(self, chapter):
        """直接转换章节为Markdown文件"""
        try:
            # 提取内容
            markdown_content = self.extract_chapter_content(chapter)
            
            if not markdown_content:
                return None
            
            # 生成文件名
            safe_name = self.generate_safe_filename(chapter)
            md_filename = f"{safe_name}.md"
            md_path = os.path.join(self.output_dir, md_filename)
            
            # 保存Markdown文件
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            return md_path
            
        except Exception as e:
            logger.error(f"直接转换章节 {chapter['alarm_code']} 时出错: {e}")
            return None
    
    def process_chapters_batch(self, chapters, batch_size=10):
        """批量处理章节"""
        logger.info(f"🚀 开始直接批量处理 {len(chapters)} 个告警章节...")
        
        successful_conversions = []
        failed_conversions = []
        
        # 分批处理
        for batch_start in range(0, len(chapters), batch_size):
            batch_end = min(batch_start + batch_size, len(chapters))
            batch_chapters = chapters[batch_start:batch_end]
            
            logger.info(f"\n📦 处理批次 {batch_start//batch_size + 1}: 章节 {batch_start+1}-{batch_end}")
            
            for i, chapter in enumerate(batch_chapters):
                chapter_idx = batch_start + i + 1
                chapter_start_time = time.time()
                
                logger.info(f"\n📄 处理第 {chapter_idx}/{len(chapters)} 章: {chapter['alarm_code']}")
                logger.info(f"   类型: {chapter['section_type']}")
                logger.info(f"   标题: {chapter['alarm_name']}")
                
                try:
                    # 直接转换为Markdown
                    logger.info("   🔄 直接转换为Markdown...")
                    md_path = self.convert_chapter_directly(chapter)
                    
                    if md_path:
                        chapter_time = time.time() - chapter_start_time
                        logger.info(f"   ✅ 转换成功! 耗时: {chapter_time:.2f}秒")
                        logger.info(f"   📁 输出: {md_path}")
                        
                        successful_conversions.append({
                            'chapter': chapter,
                            'md_path': md_path,
                            'time': chapter_time
                        })
                    else:
                        logger.error(f"   ❌ 转换失败")
                        failed_conversions.append({
                            'chapter': chapter,
                            'error': '转换失败'
                        })
                    
                except Exception as e:
                    logger.error(f"   ❌ 处理章节时出错: {e}")
                    failed_conversions.append({
                        'chapter': chapter,
                        'error': str(e)
                    })
                    continue
        
        return successful_conversions, failed_conversions
    
    def process_all_chapters(self):
        """处理所有章节"""
        start_time = time.time()
        
        # 查找章节
        chapters = self.find_all_alarm_chapters()
        
        if not chapters:
            logger.error("❌ 未找到告警章节")
            return []
        
        # 批量处理
        successful_conversions, failed_conversions = self.process_chapters_batch(chapters)
        
        # 输出总结
        total_time = time.time() - start_time
        self.print_summary(successful_conversions, failed_conversions, total_time)
        
        return successful_conversions
    
    def print_summary(self, successful, failed, total_time):
        """打印处理总结"""
        logger.info("\n" + "="*80)
        logger.info("📊 直接批量转换完成总结")
        logger.info("="*80)
        
        logger.info(f"⏱️  总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        logger.info(f"✅ 成功转换: {len(successful)} 个章节")
        logger.info(f"❌ 转换失败: {len(failed)} 个章节")
        
        if len(successful) + len(failed) > 0:
            logger.info(f"📈 成功率: {len(successful)/(len(successful)+len(failed))*100:.1f}%")
        
        # 按类型统计
        if successful:
            type_stats = {}
            for item in successful:
                section_type = item['chapter']['section_type']
                type_stats[section_type] = type_stats.get(section_type, 0) + 1
            
            logger.info(f"\n📁 成功转换的章节（按类型）:")
            for section_type, count in type_stats.items():
                logger.info(f"   - {section_type}: {count} 个")
            
            # 计算平均转换时间
            avg_time = sum(item['time'] for item in successful) / len(successful)
            logger.info(f"\n⚡ 平均转换时间: {avg_time:.2f}秒/章节")
        
        if failed:
            logger.info(f"\n❌ 转换失败的章节:")
            for i, item in enumerate(failed[:10], 1):  # 只显示前10个
                chapter = item['chapter']
                error = item['error']
                logger.info(f"  {i:2d}. {chapter['alarm_code']} - {error}")
            
            if len(failed) > 10:
                logger.info(f"  ... 还有 {len(failed) - 10} 个失败")
        
        logger.info(f"\n📂 输出目录: {self.output_dir}")
        logger.info("="*80)


def create_index_file(successful_conversions, output_dir):
    """创建索引文件"""
    try:
        index_path = os.path.join(output_dir, "README.md")
        
        # 按类型分组
        type_groups = {}
        for item in successful_conversions:
            section_type = item['chapter']['section_type']
            if section_type not in type_groups:
                type_groups[section_type] = []
            type_groups[section_type].append(item)
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write("# 华为云Stack告警处理参考 - 直接转换索引\n\n")
            f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"总计: {len(successful_conversions)} 个告警章节\n\n")
            f.write("## 转换说明\n\n")
            f.write("本批量转换使用直接转换方式，不保存中间文档，提高转换速度。\n\n")
            
            # 按类型输出
            for section_type, items in type_groups.items():
                f.write(f"## {section_type} ({len(items)} 个)\n\n")
                
                for i, item in enumerate(items, 1):
                    chapter = item['chapter']
                    md_path = item['md_path']
                    relative_path = os.path.relpath(md_path, output_dir)
                    
                    f.write(f"{i:3d}. [{chapter['alarm_code']} {chapter['alarm_name']}]({relative_path})\n")
                
                f.write("\n")
        
        logger.info(f"📋 已生成索引文件: {index_path}")
        
    except Exception as e:
        logger.warning(f"生成索引文件时出错: {e}")


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "direct_batch_results"
    
    if not os.path.exists(source_file):
        logger.error(f"❌ 错误：找不到源文件 {source_file}")
        return
    
    # 创建转换器
    converter = DirectBatchConverter(source_file, output_dir)
    
    # 执行批量转换
    try:
        successful_conversions = converter.process_all_chapters()
        
        if successful_conversions:
            logger.info(f"\n🎉 直接批量转换完成!")
            logger.info(f"📄 成功转换 {len(successful_conversions)} 个告警章节")
            logger.info(f"📁 输出目录: {output_dir}")
            logger.info(f"⚡ 使用直接转换方式，大幅提升转换速度")
            
            # 生成索引文件
            create_index_file(successful_conversions, output_dir)
        else:
            logger.error("❌ 没有成功转换任何章节")
            
    except Exception as e:
        logger.error(f"❌ 直接批量转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
