#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试遗漏章节的原因
"""

from docx import Document
import re

def debug_missing_chapters():
    """调试遗漏章节的原因"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    print("🔍 调试遗漏章节的原因...")
    
    # 模拟转换器的逻辑
    patterns = [
        # ALM告警（5位数）
        (r'^5\.\d+\.\d+\.\d+\.\d+\s+ALM-\d+', 'ALM告警_5位'),
        # ALM告警（6位数）
        (r'^5\.\d+\.\d+\.\d+\.\d+\.\d+\s+ALM-', 'ALM告警_6位'),
        # 十六进制告警
        (r'^5\.\d+\.\d+\.\d+\.\d+\s+0x[0-9A-Fa-f]+', '十六进制告警'),
        # 其他告警代码（数字开头）
        (r'^5\.\d+\.\d+\.\d+\.\d+\s+\d+\s+', '其他告警代码'),
        # 其他章节（所有其他以5.开头的章节）
        (r'^5\.\d+', '其他章节')
    ]
    
    found_chapters = []
    stop_reason = None
    stop_index = -1
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # 检查每种模式
        chapter_found = False
        for pattern, section_type in patterns:
            if re.match(pattern, text):
                found_chapters.append({
                    'index': i,
                    'text': text,
                    'type': section_type
                })
                chapter_found = True
                
                if len(found_chapters) % 50 == 0:
                    print(f"  已找到 {len(found_chapters)} 个章节...")
                break
        
        # 检查停止条件
        if text.startswith('6.'):
            stop_reason = f"遇到第6章: {text}"
            stop_index = i
            print(f"  {stop_reason}")
            break
    
    print(f"\n📊 转换器模拟结果:")
    print(f"找到章节数: {len(found_chapters)}")
    print(f"停止原因: {stop_reason}")
    print(f"停止位置: 第{stop_index}行")
    
    # 现在检查实际的所有5.x章节
    print(f"\n🔍 检查实际的所有5.x章节...")
    all_5x_chapters = []
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        
        if re.match(r'^5\.', text):
            all_5x_chapters.append({
                'index': i,
                'text': text
            })
        
        if text.startswith('6.'):
            print(f"遇到第6章: {text}")
            break
    
    print(f"实际5.x章节数: {len(all_5x_chapters)}")
    
    # 找出遗漏的章节
    found_indices = {ch['index'] for ch in found_chapters}
    all_indices = {ch['index'] for ch in all_5x_chapters}
    
    missing_indices = all_indices - found_indices
    missing_chapters = [ch for ch in all_5x_chapters if ch['index'] in missing_indices]
    
    print(f"\n❌ 遗漏的章节 ({len(missing_chapters)} 个):")
    for i, chapter in enumerate(missing_chapters, 1):
        print(f"{i:3d}. 第{chapter['index']:4d}行: {chapter['text']}")
        
        # 分析为什么遗漏
        text = chapter['text']
        matched_any = False
        for pattern, section_type in patterns:
            if re.match(pattern, text):
                print(f"     应该匹配: {section_type}")
                matched_any = True
                break
        
        if not matched_any:
            print(f"     ❌ 不匹配任何模式!")
    
    # 检查最后几个找到的章节和遗漏的章节的位置关系
    if found_chapters and missing_chapters:
        last_found = found_chapters[-1]
        first_missing = min(missing_chapters, key=lambda x: x['index'])
        
        print(f"\n🎯 位置分析:")
        print(f"最后找到的章节: 第{last_found['index']}行 - {last_found['text']}")
        print(f"第一个遗漏的章节: 第{first_missing['index']}行 - {first_missing['text']}")
        
        # 检查中间是否有导致停止的内容
        print(f"\n🔍 检查第{last_found['index']}行到第{first_missing['index']}行之间的内容:")
        for i in range(last_found['index'] + 1, first_missing['index']):
            if i < len(doc.paragraphs):
                text = doc.paragraphs[i].text.strip()
                if text:
                    print(f"  第{i}行: {text[:100]}...")
                    if text.startswith('6.'):
                        print(f"    ⚠️  这里有6.x章节，可能导致提前停止!")

if __name__ == "__main__":
    debug_missing_chapters()
