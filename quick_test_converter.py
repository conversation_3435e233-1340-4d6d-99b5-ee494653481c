#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试转换器
先转换前几个ALM章节来测试效果
"""

from enhanced_doc_converter import EnhancedDocxToMarkdown
from docx import Document
import os
import re


class QuickTestConverter:
    def __init__(self, source_docx, max_chapters=5):
        self.source_docx = source_docx
        self.max_chapters = max_chapters
        self.doc = Document(source_docx)
        
        print(f"正在加载文档: {source_docx}")
        print(f"文档加载完成，将转换前 {max_chapters} 个ALM章节")
    
    def find_first_n_chapters(self):
        """查找前N个ALM告警章节"""
        print("查找ALM告警章节...")
        
        chapters = []
        
        for i, paragraph in enumerate(self.doc.paragraphs):
            if len(chapters) >= self.max_chapters:
                break
                
            text = paragraph.text.strip()
            
            # 检查是否是告警章节标题
            if (paragraph.style.name.startswith('Heading') and 
                'ALM-' in text and 
                text.startswith('*******.')):
                
                chapters.append({
                    'title': text,
                    'start_para': i,
                    'alm_code': self._extract_alm_code(text)
                })
                
                print(f"  找到: {chapters[-1]['alm_code']}")
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                # 查找下一个ALM章节作为结束位置
                for j in range(chapter['start_para'] + 1, len(self.doc.paragraphs)):
                    para = self.doc.paragraphs[j]
                    if (para.style.name.startswith('Heading') and 
                        'ALM-' in para.text and 
                        para.text.startswith('*******.')):
                        chapter['end_para'] = j
                        break
                else:
                    chapter['end_para'] = min(chapter['start_para'] + 500, len(self.doc.paragraphs))
        
        print(f"找到 {len(chapters)} 个ALM告警章节")
        return chapters
    
    def _extract_alm_code(self, title):
        """提取ALM代码"""
        match = re.search(r'ALM-\d+', title)
        return match.group() if match else "ALM-UNKNOWN"
    
    def create_test_document(self, chapters):
        """创建包含前几个章节的测试文档"""
        test_doc = Document()
        
        # 添加文档标题
        title_para = test_doc.add_paragraph("华为云Stack告警处理参考（测试版）")
        title_para.style = 'Heading 1'
        
        # 添加每个章节的内容
        for chapter in chapters:
            start = chapter['start_para']
            end = chapter['end_para']
            
            print(f"添加章节: {chapter['alm_code']} (段落 {start}-{end})")
            
            for i in range(start, min(end, len(self.doc.paragraphs))):
                para = self.doc.paragraphs[i]
                new_para = test_doc.add_paragraph(para.text)
                try:
                    new_para.style = para.style
                except:
                    pass
        
        return test_doc
    
    def convert_test_document(self):
        """转换测试文档"""
        print("开始转换测试文档...")
        
        # 查找章节
        chapters = self.find_first_n_chapters()
        
        if not chapters:
            print("未找到ALM告警章节")
            return
        
        # 创建测试文档
        test_doc = self.create_test_document(chapters)
        
        # 保存测试文档
        test_file = "test_alarms.docx"
        test_doc.save(test_file)
        print(f"测试文档已保存: {test_file}")
        
        # 转换测试文档
        converter = EnhancedDocxToMarkdown(test_file, "test_output")
        markdown_file = converter.convert_to_markdown("test_alarms.md")
        
        print(f"\n✅ 测试转换完成!")
        print(f"📄 输出文件: {markdown_file}")
        
        # 显示统计信息
        with open(markdown_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = len(content.split('\n'))
        chars = len(content)
        alm_count = content.count('ALM-')
        
        print(f"📏 总行数: {lines}")
        print(f"📝 总字符数: {chars}")
        print(f"🚨 告警数量: {alm_count}")
        
        # 显示前几行内容
        print(f"\n📋 内容预览:")
        preview_lines = content.split('\n')[:20]
        for i, line in enumerate(preview_lines, 1):
            print(f"  {i:2d}: {line}")
        
        if len(content.split('\n')) > 20:
            print(f"  ... 还有 {len(content.split('\n')) - 20} 行")


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 找不到源文件: {source_file}")
        return
    
    try:
        converter = QuickTestConverter(source_file, max_chapters=3)
        converter.convert_test_document()
        
        print("\n💡 如果测试效果满意，可以运行完整转换:")
        print("   python batch_alarm_converter.py")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
