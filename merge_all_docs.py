#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并所有MD文档
"""

import os
import glob

def merge_all_docs():
    """合并所有MD文档"""
    
    # 查找所有MD文件
    directory = "optimized_batch_results"
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return
    
    md_files = glob.glob(os.path.join(directory, "*.md"))
    md_files.sort()  # 按文件名排序
    
    if not md_files:
        print(f"⚠️  在目录 {directory} 中未找到MD文件")
        return
    
    print(f"🔍 找到 {len(md_files)} 个MD文件")
    print(f"📋 开始合并所有文档...")
    
    # 合并文档
    merged_content = []
    
    for i, file_path in enumerate(md_files):
        filename = os.path.basename(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 添加文档内容
            merged_content.append(content)
            
            # 如果不是最后一个文件，添加分隔符
            if i < len(md_files) - 1:
                merged_content.append("\n===\n")
            
            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"  ✅ 已处理: {i + 1}/{len(md_files)} 个文件")
            
        except Exception as e:
            print(f"  ❌ 读取文件 {filename} 时出错: {e}")
    
    # 写入合并后的文件
    output_file = "merged_all_docs.md"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(merged_content))
        
        print(f"\n🎉 合并完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 合并了 {len(md_files)} 个文档")
        
        # 统计信息
        total_content = '\n'.join(merged_content)
        lines_count = len(total_content.split('\n'))
        chars_count = len(total_content)
        file_size_mb = chars_count / (1024 * 1024)
        
        print(f"📈 统计信息:")
        print(f"   - 总行数: {lines_count:,}")
        print(f"   - 总字符数: {chars_count:,}")
        print(f"   - 文件大小: {file_size_mb:.2f} MB")
        
        # 分隔符统计
        separator_count = merged_content.count("\n===\n")
        print(f"   - 分隔符数量: {separator_count}")
        
    except Exception as e:
        print(f"❌ 写入合并文件时出错: {e}")

def show_file_list():
    """显示要合并的文件列表"""
    directory = "optimized_batch_results"
    md_files = glob.glob(os.path.join(directory, "*.md"))
    md_files.sort()
    
    print(f"📋 将要合并的文件列表 (共{len(md_files)}个):")
    print("="*80)
    
    for i, file_path in enumerate(md_files, 1):
        filename = os.path.basename(file_path)
        print(f"{i:3d}. {filename}")
    
    print("="*80)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "list":
        show_file_list()
    else:
        merge_all_docs()
