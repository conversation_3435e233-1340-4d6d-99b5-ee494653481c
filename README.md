# Word文档到Markdown转换工具套件

这是一个功能强大的Word文档(.docx)到Markdown格式转换工具套件，支持多种转换方法，能够很好地保持文档的原始格式，包括：

- ✅ **表格转换** - 完整保持表格结构
- ✅ **图片提取** - 自动提取并引用图片
- ✅ **段落格式** - 保持标题、列表、段落等格式
- ✅ **文档结构** - 维持原始文档的层次结构

## 🚀 快速开始

### 1. 安装依赖

```bash
# 运行依赖安装脚本
python install_dependencies.py

# 或手动安装
pip install python-docx mammoth Pillow lxml
```

### 2. 安装Pandoc (可选，但推荐)

**macOS:**
```bash
brew install pandoc
```

**Ubuntu/Debian:**
```bash
sudo apt-get install pandoc
```

**Windows:**
从 [pandoc.org](https://pandoc.org/installing.html) 下载安装包

### 3. 运行转换

```bash
# 使用所有方法转换 test.docx
python docx_converter_suite.py test.docx

# 指定输出目录
python docx_converter_suite.py test.docx -o my_output

# 只使用特定方法
python docx_converter_suite.py test.docx -m 3  # 只用pandoc
```

## 📋 转换方法对比

| 方法 | 工具 | 优势 | 适用场景 |
|------|------|------|----------|
| **方法1** | python-docx | 完全自定义控制，详细格式处理 | 需要精确控制转换过程 |
| **方法2** | mammoth | 简单易用，HTML中间格式 | 快速转换，格式要求不高 |
| **方法3** | pandoc | 最强大，支持最多格式 | 专业文档转换，最佳效果 |

## 🔧 各转换器详细说明

### 方法1: Enhanced DocX Converter (推荐用于自定义需求)

```python
from enhanced_doc_converter import EnhancedDocxToMarkdown

converter = EnhancedDocxToMarkdown("input.docx", "output_dir")
result = converter.convert_to_markdown("output.md")
```

**特点:**
- 🎯 精确的段落样式识别
- 📊 完整的表格转换
- 🖼️ 智能图片提取和命名
- 📝 保持文本格式(粗体、斜体)
- 📋 列表层级处理

### 方法2: Mammoth Converter (推荐用于快速转换)

```python
from mammoth_converter import MammothDocxConverter

converter = MammothDocxConverter("input.docx", "output_dir")
result = converter.convert_to_markdown("output.md")
```

**特点:**
- ⚡ 转换速度快
- 🔄 通过HTML中间格式
- 🖼️ 自动图片处理
- 📐 良好的格式保持

### 方法3: Pandoc Converter (推荐用于最佳效果)

```python
from pandoc_converter import PandocConverter

converter = PandocConverter("input.docx", "output_dir")
result = converter.convert_to_markdown("output.md")
```

**特点:**
- 🏆 最强大的转换引擎
- 📚 支持最多文档格式
- 🎨 最佳格式保持
- ⚙️ 丰富的自定义选项

## 📁 输出结构

转换后会生成以下文件结构：

```
output_directory/
├── result.md          # 转换后的Markdown文件
└── images/            # 提取的图片文件
    ├── image_001.png
    ├── image_002.jpg
    └── ...
```

## 🎯 使用示例

### 基础转换
```bash
python docx_converter_suite.py test.docx
```

### 指定方法和输出
```bash
# 只使用pandoc方法
python docx_converter_suite.py test.docx -m 3 -o pandoc_result

# 使用mammoth方法
python docx_converter_suite.py test.docx -m 2 -o mammoth_result
```

### 程序化使用
```python
# 直接使用转换器
from enhanced_doc_converter import EnhancedDocxToMarkdown

converter = EnhancedDocxToMarkdown("告警文档.docx", "output")
markdown_file = converter.convert_to_markdown("告警处理手册.md")
print(f"转换完成: {markdown_file}")
```

## 🔍 转换质量对比

运行套件后，会自动生成转换质量报告：

```
📊 转换结果比较:
==================================================

方法1 (python-docx):
  📄 文件: output_docx/result_docx.md
  📏 行数: 1250
  📝 字符数: 45678
  🖼️  图片数: 12
  📋 表格标记: 156
  📑 标题数: 23

方法2 (mammoth):
  📄 文件: output_mammoth/result_mammoth.md
  📏 行数: 1180
  📝 字符数: 43210
  🖼️  图片数: 12
  📋 表格标记: 142
  📑 标题数: 21

方法3 (pandoc):
  📄 文件: output_pandoc/result_pandoc.md
  📏 行数: 1320
  📝 字符数: 48901
  🖼️  图片数: 12
  📋 表格标记: 168
  📑 标题数: 25
```

## 🛠️ 高级配置

### 自定义图片处理
```python
# 在 enhanced_doc_converter.py 中修改
def extract_images(self):
    # 自定义图片提取逻辑
    pass
```

### 自定义表格格式
```python
# 修改表格转换格式
def convert_table_to_markdown(self, table):
    # 自定义表格转换逻辑
    pass
```

## 🐛 常见问题

### Q: 转换后图片不显示？
A: 检查图片路径是否正确，确保 `images/` 目录存在

### Q: 表格格式不正确？
A: 尝试使用 pandoc 方法(方法3)，它对表格的支持最好

### Q: 中文字符乱码？
A: 确保使用 UTF-8 编码，所有脚本都已配置正确的编码

### Q: Pandoc 安装失败？
A: 参考官方安装指南：https://pandoc.org/installing.html

## 📝 文件说明

- `docx_converter_suite.py` - 主转换工具，支持多种方法
- `enhanced_doc_converter.py` - 基于python-docx的增强转换器
- `mammoth_converter.py` - 基于mammoth的转换器  
- `pandoc_converter.py` - 基于pandoc的转换器
- `install_dependencies.py` - 依赖安装脚本
- `doc_process.py` - 原始转换脚本(已改进)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具套件！

## 📄 许可证

MIT License - 详见 LICENSE 文件
