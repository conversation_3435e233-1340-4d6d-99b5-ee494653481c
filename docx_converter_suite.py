#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档到Markdown转换工具套件
提供多种转换方法供选择
"""

import os
import sys
import argparse
from pathlib import Path


def check_dependencies():
    """检查依赖库"""
    dependencies = {
        'docx': 'python-docx',
        'mammoth': 'mammoth',
        'PIL': 'Pillow'
    }

    missing = []
    for name, package in dependencies.items():
        try:
            __import__(name)
        except ImportError:
            missing.append(package)

    if missing:
        print("缺少以下依赖库:")
        for pkg in missing:
            print(f"  pip install {pkg}")
        return False
    return True


def method_1_python_docx(input_file, output_dir):
    """方法1: 使用 python-docx (自定义解析)"""
    print("\n🔧 方法1: 使用 python-docx 自定义解析")

    try:
        from enhanced_doc_converter import EnhancedDocxToMarkdown

        converter = EnhancedDocxToMarkdown(input_file, output_dir + "_docx")
        result = converter.convert_to_markdown("result_docx.md")

        if result:
            print(f"✅ 方法1 转换成功: {result}")
            return result
        else:
            print("❌ 方法1 转换失败")
            return None

    except Exception as e:
        print(f"❌ 方法1 出错: {e}")
        return None


def method_2_mammoth(input_file, output_dir):
    """方法2: 使用 mammoth"""
    print("\n🦣 方法2: 使用 mammoth 库")

    try:
        # 检查 mammoth 是否安装
        try:
            import mammoth
        except ImportError:
            print("正在安装 mammoth...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "mammoth"])
            import mammoth

        from mammoth_converter import MammothDocxConverter

        converter = MammothDocxConverter(input_file, output_dir + "_mammoth")
        result = converter.convert_to_markdown("result_mammoth.md")

        if result:
            print(f"✅ 方法2 转换成功: {result}")
            return result
        else:
            print("❌ 方法2 转换失败")
            return None

    except Exception as e:
        print(f"❌ 方法2 出错: {e}")
        return None


def method_3_pandoc(input_file, output_dir):
    """方法3: 使用 pandoc"""
    print("\n📚 方法3: 使用 pandoc")

    try:
        from pandoc_converter import PandocConverter

        converter = PandocConverter(input_file, output_dir + "_pandoc")
        result = converter.convert_to_markdown("result_pandoc.md")

        if result:
            print(f"✅ 方法3 转换成功: {result}")
            return result
        else:
            print("❌ 方法3 转换失败")
            return None

    except Exception as e:
        print(f"❌ 方法3 出错: {e}")
        return None


def compare_results(results):
    """比较转换结果"""
    print("\n📊 转换结果比较:")
    print("=" * 50)

    for method, result in results.items():
        if result:
            try:
                with open(result, 'r', encoding='utf-8') as f:
                    content = f.read()

                lines = len(content.split('\n'))
                chars = len(content)
                images = content.count('![')
                tables = content.count('|')
                headers = content.count('#')

                print(f"\n{method}:")
                print(f"  📄 文件: {result}")
                print(f"  📏 行数: {lines}")
                print(f"  📝 字符数: {chars}")
                print(f"  🖼️  图片数: {images}")
                print(f"  📋 表格标记: {tables}")
                print(f"  📑 标题数: {headers}")

            except Exception as e:
                print(f"\n{method}: 无法分析结果 - {e}")
        else:
            print(f"\n{method}: 转换失败")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Word文档到Markdown转换工具套件')
    parser.add_argument('input_file', nargs='?', default='test.docx',
                       help='输入的Word文档文件 (默认: test.docx)')
    parser.add_argument('-o', '--output', default='conversion_results',
                       help='输出目录 (默认: conversion_results)')
    parser.add_argument('-m', '--method', choices=['1', '2', '3', 'all'], default='all',
                       help='转换方法: 1=python-docx, 2=mammoth, 3=pandoc, all=全部 (默认: all)')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"❌ 错误：找不到文件 {args.input_file}")
        return

    print(f"📄 输入文件: {args.input_file}")
    print(f"📁 输出目录: {args.output}")

    # 检查基础依赖
    if not check_dependencies():
        print("❌ 请先安装必要的依赖库")
        return

    # 执行转换
    results = {}

    if args.method in ['1', 'all']:
        result = method_1_python_docx(args.input_file, args.output)
        if result:
            results['方法1 (python-docx)'] = result

    if args.method in ['2', 'all']:
        result = method_2_mammoth(args.input_file, args.output)
        if result:
            results['方法2 (mammoth)'] = result

    if args.method in ['3', 'all']:
        result = method_3_pandoc(args.input_file, args.output)
        if result:
            results['方法3 (pandoc)'] = result

    # 比较结果
    if results:
        compare_results(results)

        print(f"\n🎉 转换完成！共成功 {len(results)} 种方法")
        print("\n💡 建议:")
        print("  - 如果需要最佳格式保持，推荐使用 pandoc (方法3)")
        print("  - 如果需要自定义处理，推荐使用 python-docx (方法1)")
        print("  - 如果需要简单快速转换，推荐使用 mammoth (方法2)")
    else:
        print("❌ 所有转换方法都失败了")


if __name__ == "__main__":
    main()
