# 文档转换结果

# 5.2.3.1.1 ALM-6008 上传日志到OBS服务失败

#### 告警解释

OpenStack周期（默认为300秒）检查上一次上传日志至OBS服务是否成功，当检查到上传失败时，产生此告警。

#### 告警属性

| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6008 | 重要 | 是 |

#### 告警参数

| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |

#### 对系统的影响

日志无法上传至OBS服务，如果未设置本地备份，则会导致该次日志丢失。

#### 可能原因

- 主机与OBS服务网络连接异常。
- Apacheproxy服务状态异常。
- 日志配置文件中OBS服务IP、端口未正确设置。
- OBS服务上指定的日志上传空间已满。
#### 处理步骤

1. 登录FusionSphere OpenStack安装部署界面。
   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
   ```
   su - root
   ```
   默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
6. 执行以下命令，导入环境变量。
   ```
   source set_env
   ```
   回显如下类似信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   (4) openstack environment variable of cloud_admin (keystone v3)
   please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
   默认密码为：*****。

**Apacheproxy服务状态异常**

8. 执行如下操作查看apacheproxy服务是否正常。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入“1”，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 执行如下命令，查看apacheproxy服务是否正常。
         ```
         cps template-instance-list --service apacheproxy apacheproxy
         ```
         在apacheproxy主备部署的情况下，回显如下类似信息：
         在apacheproxy单实例部署的情况下，回显如下类似信息：
   - 如服务状态显示为fault，执行9。
   - 如未显示为fault，执行12。
9. 执行如下操作停止apacheproxy服务。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入“1”，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 运行如下命令停止apacheproxy服务。
         ```
         cps host-template-instance-operate --service apacheproxy apacheproxy --action stop
         ```
         回显如下类似信息：
         查看操作结果是否为success。
   - 是，执行10。
   - 否，执行17。
10. 执行如下操作启动apacheproxy服务。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入1，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 执行如下命令启动apacheproxy服务。
         ```
         cps host-template-instance-operate --service apacheproxy apacheproxy --action start
         ```
         回显如下类似信息：
         查看操作结果是否为success。
   - 是，执行11。
   - 否，执行17。
11. 执行如下操作查看apacheproxy服务是否正常。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入“1”，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 执行如下命令查看apacheproxy服务是否正常。
         ```
         cps template-instance-list --service apacheproxy apacheproxy
         ```
         在apacheproxy主备部署的情况下，回显如下类似信息：
         在apacheproxy单实例部署的情况下，回显如下类似信息：
   - 如未显示fault，执行12。
   - 如仍显示为fault，执行17。

**日志配置文件中OBS服务IP、端口未正确设置**

12. 执行以下命令，获取配置的OBS信息，确认OBS日志服务地址是否正确。
      ```
      log policy-get
      ```
   ```
   42174775-2DA8-B93F-CF47-47902E7AA2B0:~ # log policy-get
   ```
   +-----------------------------+---------------------------------+
   | Property                    |                  Value          |
   +-----------------------------+---------------------------------+
   | policy_gateway              |                                 |
   | policy_s3_access_key        |  92514a3e0b5a4acbb532aba9638    |
   | policy_s3_export_begin      |                   2             |
   | policy_s3_export_end        |                   3             |
   | policy_s3_host              |  s3.dc1.domainname.com:5443    |
   | policy_s3_operate_bucket    |                                 |
   | policy_s3_operate_lifecycle |                   4             |
   | policy_s3_region            |                                 |
   | policy_s3_run_bucket        |                                 |
   | policy_s3_run_lifecycle     |                   90            |
   | policy_s3_scheme            |                                 |
   | policy_s3_secret_key        |   3d99c09e38614b31930e349ec638  |
   +-----------------------------+---------------------------------+
   - 是，请执行14。
   - 否，请执行13。
13. 执行如下命令重新设置OBS服务地址(policy_s3_host)，AK(policy_s3_access_key)/SK(policy_s3_secret_key)等信息。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入“1”，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 执行如下命令重新设置OBS服务地址(policy_s3_host)，AK(policy_s3_access_key)/SK(policy_s3_secret_key)等信息。
         ```
         log policy-set --parameter policy_s3_access_key=ACCESS_KEY policy_s3_secret_key=SECRET_KEY policy_s3_host=S3_HOST
         ```
         查看是否回显如下类似信息：
         +----------------------+----------------------------------+ 
           | Property             | Value                            | 
           +----------------------+----------------------------------+ 
           | policy_s3_access_key | 92514a3e0b5a4acbb532aba957a9d66f | 
           | policy_s3_host       | s3.dc1.domainname.com:5443       | 
           | policy_s3_secret_key | 3d99c09e38614b31930e349ec63842a3 | 
           +----------------------+----------------------------------+
   - 是，执行14。
   - 否，执行17。
14. 执行如下操作手动触发日志上传。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入“1”，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 执行如下命令手动触发日志上传。
         ```
         log log-flush --host 主机ID
         ```
15. 等待5分钟，执行如下操作查看各组件的日志上传状态。
   - 执行如下命令，采用安全方式操作。
         ```
         cpssafe
         ```
         显示如下信息：
         please choose environment variable which you want to import:
         (1) openstack environment variable (keystone v3)
         (2) cps environment variable
         (3) openstack environment variable legacy (keystone v2)
         please choose:[1|2|3]
   - 输入“1”，选择使用keystone v3鉴权。
         显示如下信息：
         ```
         Input command:
         ```
   - 执行如下命令查看各组件的日志上传状态。
         ```
         log log-state-get --host 主机ID
         ```
   - 状态字段显示为上传时间，执行16。
   - 存在状态字段显示为“log flushing”，重新执行15。
   - 存在状态字段显示为“not flushed yet”，转17。
16. 等待1分钟查看告警是否恢复。
   - 是，处理完毕。
   - 否，执行17。
17. 请联系技术支持工程师协助解决。
#### 参考信息

无。
