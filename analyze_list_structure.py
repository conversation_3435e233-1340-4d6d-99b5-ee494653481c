#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Word文档的列表结构
"""

from docx import Document
from docx.oxml.ns import qn
import re

def analyze_list_structure():
    """分析列表结构"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    print("=== 分析ALM-6008章节的列表结构 ===")
    
    # 找到ALM-6008章节
    start_found = False
    processing_section = False
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        
        # 找到ALM-6008章节开始
        if "*******.1 ALM-6008" in text:
            start_found = True
            print(f"\n找到章节开始: {text}")
            continue
            
        if not start_found:
            continue
            
        # 找到下一个章节就停止
        if re.match(r'^5\.2\.3\.1\.\d+\s+ALM-\d+', text):
            print(f"\n找到下一个章节，停止: {text}")
            break
            
        # 检查是否进入处理步骤部分
        if "处理步骤" in text:
            processing_section = True
            print(f"\n=== 进入处理步骤部分 ===")
            continue
            
        # 如果在处理步骤部分，详细分析列表结构
        if processing_section and text:
            # 检查段落的列表属性
            if hasattr(para, '_element') and para._element is not None:
                pPr = para._element.find(qn('w:pPr'))
                if pPr is not None:
                    numPr = pPr.find(qn('w:numPr'))
                    if numPr is not None:
                        # 获取列表级别
                        ilvl = numPr.find(qn('w:ilvl'))
                        level = 0
                        if ilvl is not None:
                            level = int(ilvl.get(qn('w:val'), 0))
                        
                        # 获取编号ID
                        numId = numPr.find(qn('w:numId'))
                        num_id = numId.get(qn('w:val')) if numId is not None else "None"
                        
                        # 分析文本内容
                        is_main_step = analyze_main_step(text)
                        
                        print(f"\n段落 {i}: 级别={level}, 编号ID={num_id}, 主步骤={is_main_step}")
                        print(f"  文本: {text[:80]}...")
                        
                        # 建议的列表类型
                        if level == 0 and is_main_step:
                            print(f"  建议: 有序列表 (主要处理步骤)")
                        else:
                            print(f"  建议: 无序列表 (子步骤或说明)")

def analyze_main_step(text):
    """分析是否是主要处理步骤"""
    # 主要步骤通常以动作词开头，且是独立的操作
    main_step_patterns = [
        r'^登录',
        r'^使用.*登录',
        r'^执行.*命令.*切换',
        r'^执行.*命令.*防止',
        r'^执行.*命令.*导入',
        r'^输入.*选择',
        r'^执行.*操作.*查看',
        r'^执行.*操作.*停止',
        r'^执行.*操作.*启动',
        r'^执行.*命令.*获取',
        r'^执行.*命令.*设置',
        r'^执行.*操作.*触发',
        r'^等待.*查看',
        r'^请联系'
    ]
    
    for pattern in main_step_patterns:
        if re.match(pattern, text):
            return True
    
    # 排除明显的子步骤或说明
    sub_step_patterns = [
        r'^具体请参见',
        r'^默认',
        r'^系统同时支持',
        r'^回显',
        r'^显示',
        r'^在.*情况下',
        r'^如.*执行',
        r'^否.*执行',
        r'^是.*处理',
        r'^状态字段',
        r'^存在状态'
    ]
    
    for pattern in sub_step_patterns:
        if re.match(pattern, text):
            return False
    
    return False

if __name__ == "__main__":
    analyze_list_structure()
