#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灵活的Word文档到Markdown转换器
支持多种输入类型：
1. 文件路径（原有功能）
2. Document对象
3. 段落列表
4. 文档片段
"""

from docx import Document
from docx.shared import Inches
from docx.oxml.ns import qn
import os
import shutil
import zipfile
from docx.oxml import parse_xml
from docx.oxml.ns import nsmap
import re
from PIL import Image
import base64
from io import BytesIO
try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("警告: pytesseract未安装，无法进行OCR识别。如需OCR功能，请安装: pip install pytesseract")
import xml.etree.ElementTree as ET


class FlexibleDocxToMarkdown:
    def __init__(self, input_source, output_dir="output", input_type="file"):
        """
        初始化转换器
        
        Args:
            input_source: 输入源，可以是：
                - 文件路径 (input_type="file")
                - Document对象 (input_type="document")
                - 段落列表 (input_type="paragraphs")
                - 文档片段字典 (input_type="fragment")
            output_dir: 输出目录
            input_type: 输入类型 ("file", "document", "paragraphs", "fragment")
        """
        self.output_dir = output_dir
        self.image_dir = os.path.join(output_dir, "images")
        self.image_counter = 0
        self.image_map = {}
        self.input_type = input_type
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 根据输入类型处理输入源
        if input_type == "file":
            self.docx_path = input_source
            self.doc = Document(input_source)
            self.paragraphs = self.doc.paragraphs
            self.tables = self.doc.tables
        elif input_type == "document":
            self.doc = input_source
            self.docx_path = None
            self.paragraphs = self.doc.paragraphs
            self.tables = self.doc.tables
        elif input_type == "paragraphs":
            self.paragraphs = input_source
            self.tables = []
            self.doc = None
            self.docx_path = None
        elif input_type == "fragment":
            # 文档片段包含段落和表格
            self.paragraphs = input_source.get('paragraphs', [])
            self.tables = input_source.get('tables', [])
            self.doc = input_source.get('document', None)
            self.docx_path = input_source.get('docx_path', None)
        else:
            raise ValueError(f"不支持的输入类型: {input_type}")

    def extract_images(self):
        """提取文档中的所有图片并进行OCR识别"""
        image_files = []
        
        # 如果没有原始文档，跳过图片提取
        if not self.docx_path:
            return image_files

        try:
            # 解压docx文件
            with zipfile.ZipFile(self.docx_path, 'r') as zip_ref:
                # 查找所有图片文件
                for file_info in zip_ref.filelist:
                    if file_info.filename.startswith('word/media/'):
                        # 提取图片
                        image_data = zip_ref.read(file_info.filename)
                        
                        # 生成图片文件名
                        self.image_counter += 1
                        image_filename = f"image_{self.image_counter}.png"
                        image_path = os.path.join(self.image_dir, image_filename)
                        
                        # 保存图片
                        with open(image_path, 'wb') as img_file:
                            img_file.write(image_data)
                        
                        # 记录图片映射
                        original_name = os.path.basename(file_info.filename)
                        self.image_map[original_name] = image_filename
                        
                        # OCR识别
                        ocr_text = ""
                        if OCR_AVAILABLE:
                            try:
                                ocr_text = pytesseract.image_to_string(Image.open(image_path), lang='chi_sim+eng')
                                ocr_text = ocr_text.strip()
                            except Exception as e:
                                print(f"OCR识别失败: {e}")
                        
                        image_files.append({
                            'filename': image_filename,
                            'path': image_path,
                            'ocr_text': ocr_text
                        })
                        
        except Exception as e:
            print(f"提取图片时出错: {e}")
        
        return image_files

    def paragraph_to_markdown(self, paragraph):
        """将段落转换为Markdown格式"""
        text = paragraph.text.strip()
        
        if not text:
            return ""
        
        # 检查段落样式
        style_name = paragraph.style.name.lower()
        
        # 标题处理
        if 'heading' in style_name:
            if 'heading 1' in style_name:
                return f"# {text}\n"
            elif 'heading 2' in style_name:
                return f"## {text}\n"
            elif 'heading 3' in style_name:
                return f"### {text}\n"
            elif 'heading 4' in style_name:
                return f"#### {text}\n"
            elif 'heading 5' in style_name:
                return f"##### {text}\n"
            elif 'heading 6' in style_name:
                return f"###### {text}\n"
        
        # 列表处理
        if hasattr(paragraph, '_element') and paragraph._element is not None:
            # 检查是否是列表项
            pPr = paragraph._element.find(qn('w:pPr'))
            if pPr is not None:
                numPr = pPr.find(qn('w:numPr'))
                if numPr is not None:
                    # 获取列表级别
                    ilvl = numPr.find(qn('w:ilvl'))
                    level = 0
                    if ilvl is not None:
                        level = int(ilvl.get(qn('w:val'), 0))
                    
                    # 获取编号ID
                    numId = numPr.find(qn('w:numId'))
                    if numId is not None:
                        indent = "  " * level
                        # 简单处理：假设都是无序列表
                        return f"{indent}- {text}\n"
        
        # 普通段落
        return f"{text}\n\n"

    def table_to_markdown(self, table):
        """将表格转换为Markdown格式"""
        if not table.rows:
            return ""
        
        markdown_lines = []
        
        # 处理表头
        header_cells = []
        for cell in table.rows[0].cells:
            cell_text = cell.text.strip().replace('\n', ' ')
            header_cells.append(cell_text if cell_text else " ")
        
        markdown_lines.append("| " + " | ".join(header_cells) + " |")
        markdown_lines.append("| " + " | ".join(["---"] * len(header_cells)) + " |")
        
        # 处理数据行
        for row in table.rows[1:]:
            data_cells = []
            for cell in row.cells:
                cell_text = cell.text.strip().replace('\n', '<br>')
                data_cells.append(cell_text if cell_text else " ")
            markdown_lines.append("| " + " | ".join(data_cells) + " |")
        
        return "\n".join(markdown_lines) + "\n\n"

    def convert_to_markdown(self, output_filename="output.md"):
        """转换为Markdown格式"""
        markdown_content = []
        
        # 提取图片（如果可能）
        image_files = self.extract_images()
        
        # 处理段落
        for paragraph in self.paragraphs:
            md_text = self.paragraph_to_markdown(paragraph)
            if md_text:
                markdown_content.append(md_text)
        
        # 处理表格
        for table in self.tables:
            md_table = self.table_to_markdown(table)
            if md_table:
                markdown_content.append(md_table)
        
        # 合并内容
        final_content = "".join(markdown_content)
        
        # 保存文件
        output_path = os.path.join(self.output_dir, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        return output_path


def create_document_fragment(doc, start_para_idx, end_para_idx):
    """
    从Document对象中创建文档片段
    
    Args:
        doc: Document对象
        start_para_idx: 起始段落索引
        end_para_idx: 结束段落索引
    
    Returns:
        包含段落和表格的字典
    """
    fragment = {
        'paragraphs': [],
        'tables': [],
        'document': doc
    }
    
    # 提取段落
    all_paragraphs = doc.paragraphs
    fragment['paragraphs'] = all_paragraphs[start_para_idx:end_para_idx]
    
    # 提取相关的表格（简化处理）
    # 这里可以根据需要实现更复杂的表格提取逻辑
    fragment['tables'] = []
    
    return fragment


def create_paragraphs_from_text_list(text_list):
    """
    从文本列表创建段落对象（模拟）
    
    Args:
        text_list: 文本列表
    
    Returns:
        段落对象列表
    """
    class MockParagraph:
        def __init__(self, text):
            self.text = text
            self.style = MockStyle()
    
    class MockStyle:
        def __init__(self):
            self.name = "Normal"
    
    return [MockParagraph(text) for text in text_list]


# 使用示例
def example_usage():
    """使用示例"""
    
    # 方式1：使用文件路径（原有方式）
    converter1 = FlexibleDocxToMarkdown("test.docx", "output1", input_type="file")
    converter1.convert_to_markdown("output1.md")
    
    # 方式2：使用Document对象
    doc = Document("test.docx")
    converter2 = FlexibleDocxToMarkdown(doc, "output2", input_type="document")
    converter2.convert_to_markdown("output2.md")
    
    # 方式3：使用段落列表
    doc = Document("test.docx")
    selected_paragraphs = doc.paragraphs[10:50]  # 选择第10-50段
    converter3 = FlexibleDocxToMarkdown(selected_paragraphs, "output3", input_type="paragraphs")
    converter3.convert_to_markdown("output3.md")
    
    # 方式4：使用文档片段
    doc = Document("test.docx")
    fragment = create_document_fragment(doc, 10, 50)
    converter4 = FlexibleDocxToMarkdown(fragment, "output4", input_type="fragment")
    converter4.convert_to_markdown("output4.md")
    
    # 方式5：从文本列表创建
    text_list = ["标题1", "这是第一段内容", "这是第二段内容"]
    paragraphs = create_paragraphs_from_text_list(text_list)
    converter5 = FlexibleDocxToMarkdown(paragraphs, "output5", input_type="paragraphs")
    converter5.convert_to_markdown("output5.md")


if __name__ == "__main__":
    example_usage()
