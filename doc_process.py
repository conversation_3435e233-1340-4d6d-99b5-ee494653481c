from docx import Document
from docx.shared import Inches
from docx.oxml.ns import qn
import os
import shutil
import zipfile
from docx.oxml import parse_xml
from docx.oxml.ns import nsmap
import re
from PIL import Image
import base64
from io import BytesIO

def extract_images_from_docx(docx_path, output_image_dir):
    """从DOCX文件中提取所有图片到指定目录"""
    if not os.path.exists(output_image_dir):
        os.makedirs(output_image_dir)

    with zipfile.ZipFile(docx_path) as z:
        image_files = [f for f in z.namelist() if f.startswith('word/media/')]
        for image_file in image_files:
            z.extract(image_file, output_image_dir)
            src_path = os.path.join(output_image_dir, image_file)
            dst_path = os.path.join(output_image_dir, os.path.basename(image_file))
            shutil.move(src_path, dst_path)

    return [os.path.basename(f) for f in image_files]

def get_element_order(doc):
    """获取文档中所有元素的原始顺序及其类型"""
    element_order = []
    for paragraph in doc.paragraphs:
        element_order.append(('paragraph', paragraph))

    for table in doc.tables:
        # 找到表格在文档中的实际位置
        tbl = table._tbl
        parent = tbl.getparent()
        if parent is not None:
            # 在父元素的所有子元素中查找表格位置
            for i, child in enumerate(parent):
                if child == tbl:
                    element_order.insert(i, ('table', table))
                    break

    return sorted(element_order, key=lambda x: x[1]._element.xpath('count(preceding::*)'))

def docx_to_markdown(docx_path, output_md_path, image_output_dir='media'):
    """将DOCX转换为Markdown并保持原始元素顺序"""
    # 创建图片输出目录
    md_dir = os.path.dirname(os.path.abspath(output_md_path))
    full_image_dir = os.path.join(md_dir, image_output_dir)
    image_files = extract_images_from_docx(docx_path, full_image_dir)
    image_map = {os.path.basename(f): f for f in image_files}

    doc = Document(docx_path)
    markdown_content = []
    current_list_level = 0

    # 获取文档中所有元素的原始顺序
    elements = []
    for element in doc.element.body:
        if element.tag.endswith('p'):  # 段落
            elements.append(('paragraph', element))
        elif element.tag.endswith('tbl'):  # 表格
            elements.append(('table', element))

    # 处理每个元素
    for elem_type, elem in elements:
        if elem_type == 'paragraph':
            # 处理段落
            para = doc.paragraphs[elements.index(('paragraph', elem))]
            text = para.text.strip()
            style = para.style.name

            if not text:
                continue

            # 处理标题
            if style.startswith('Heading'):
                level = int(style.split()[-1])
                markdown_content.append(f"{'#' * level} {text}\n")
                continue

            # 处理列表项
            list_level = 0
            if para._p.xpath('.//w:numPr'):
                if para._p.xpath('.//w:ilvl/@w:val'):
                    list_level = int(para._p.xpath('.//w:ilvl/@w:val')[0])

                if list_level > current_list_level:
                    markdown_content.append("    " * (list_level - 1) + "- \n")
                current_list_level = list_level

                indent = '    ' * list_level
                markdown_content.append(f"{indent}- {text}\n")
                continue
            else:
                current_list_level = 0

            # 处理普通段落
            markdown_content.append(f"{text}\n")

        elif elem_type == 'table':
            # 处理表格
            table = None
            for t in doc.tables:
                if t._tbl == elem:
                    table = t
                    break

            if table:
                table_md = []
                for i, row in enumerate(table.rows):
                    cells = []
                    for cell in row.cells:
                        cell_text = cell.text.replace('\n', '<br>')
                        cells.append(cell_text)

                    if i == 0:
                        table_md.append(f"| {' | '.join(cells)} |")
                        table_md.append(f"| {' | '.join(['---'] * len(cells))} |")
                    else:
                        table_md.append(f"| {' | '.join(cells)} |")

                markdown_content.append('\n'.join(table_md) + '\n')

    # 写入Markdown文件
    with open(output_md_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(markdown_content))

    print(f"转换完成，Markdown文件已保存到: {output_md_path}")
    print(f"图片已保存到: {full_image_dir}")

if __name__ == "__main__":
    input_docx = "test.docx"
    output_md = "output.md"
    docx_to_markdown(input_docx, output_md, image_output_dir="media")