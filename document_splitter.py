#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档拆分工具
按章节将大文档拆分为多个小文档
"""

from docx import Document
from docx.shared import Inches
import os
import re
from pathlib import Path


class DocumentSplitter:
    def __init__(self, source_docx, output_dir="split_documents"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        self.doc = Document(source_docx)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
    
    def analyze_chapters(self):
        """分析章节结构"""
        print("分析文档章节结构...")
        
        chapters = []
        for i, paragraph in enumerate(self.doc.paragraphs):
            if paragraph.style.name.startswith('Heading 1'):
                text = paragraph.text.strip()
                if text and 'ALM-' in text:  # 只处理告警章节
                    chapters.append({
                        'title': text,
                        'start_index': i,
                        'paragraph': paragraph
                    })
        
        # 计算每章的结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_index'] = chapters[i + 1]['start_index']
            else:
                chapter['end_index'] = len(self.doc.paragraphs)
        
        print(f"找到 {len(chapters)} 个告警章节")
        return chapters
    
    def extract_chapter_content(self, start_index, end_index):
        """提取章节内容（段落和表格）"""
        content_elements = []
        
        # 获取文档中所有元素的顺序
        body_elements = list(self.doc.element.body)
        
        # 建立段落和表格的索引映射
        para_to_element = {}
        table_to_element = {}
        
        for elem in body_elements:
            if elem.tag.endswith('p'):
                for i, para in enumerate(self.doc.paragraphs):
                    if para._element == elem:
                        para_to_element[i] = elem
                        break
            elif elem.tag.endswith('tbl'):
                for i, table in enumerate(self.doc.tables):
                    if table._tbl == elem:
                        table_to_element[i] = elem
                        break
        
        # 收集章节范围内的所有元素
        target_elements = []
        
        # 找到起始和结束元素
        start_element = para_to_element.get(start_index)
        end_element = para_to_element.get(end_index) if end_index < len(self.doc.paragraphs) else None
        
        if start_element is None:
            return content_elements
        
        # 收集范围内的元素
        collecting = False
        for elem in body_elements:
            if elem == start_element:
                collecting = True
            
            if collecting:
                target_elements.append(elem)
            
            if end_element and elem == end_element:
                break
        
        # 转换为段落和表格对象
        for elem in target_elements:
            if elem.tag.endswith('p'):
                for i, para in enumerate(self.doc.paragraphs):
                    if para._element == elem:
                        content_elements.append(('paragraph', para))
                        break
            elif elem.tag.endswith('tbl'):
                for i, table in enumerate(self.doc.tables):
                    if table._tbl == elem:
                        content_elements.append(('table', table))
                        break
        
        return content_elements
    
    def create_chapter_document(self, chapter, content_elements):
        """创建单个章节的文档"""
        # 创建新文档
        new_doc = Document()
        
        # 复制样式（简化处理）
        try:
            # 尝试复制一些基本样式
            for style in self.doc.styles:
                if style.name not in new_doc.styles:
                    try:
                        new_doc.styles.add_style(style.name, style.type)
                    except:
                        pass
        except:
            pass
        
        # 添加内容
        for elem_type, elem in content_elements:
            if elem_type == 'paragraph':
                self._copy_paragraph(new_doc, elem)
            elif elem_type == 'table':
                self._copy_table(new_doc, elem)
        
        return new_doc
    
    def _copy_paragraph(self, new_doc, original_para):
        """复制段落到新文档"""
        try:
            # 创建新段落
            new_para = new_doc.add_paragraph()
            
            # 复制样式
            try:
                new_para.style = original_para.style
            except:
                pass
            
            # 复制文本和格式
            for run in original_para.runs:
                new_run = new_para.add_run(run.text)
                try:
                    new_run.bold = run.bold
                    new_run.italic = run.italic
                    new_run.underline = run.underline
                    if run.font.size:
                        new_run.font.size = run.font.size
                    if run.font.name:
                        new_run.font.name = run.font.name
                except:
                    pass
        except Exception as e:
            # 如果复制失败，至少保留文本
            new_doc.add_paragraph(original_para.text)
    
    def _copy_table(self, new_doc, original_table):
        """复制表格到新文档"""
        try:
            if not original_table.rows:
                return
            
            # 创建新表格
            rows = len(original_table.rows)
            cols = len(original_table.rows[0].cells) if rows > 0 else 1
            
            new_table = new_doc.add_table(rows=rows, cols=cols)
            
            # 复制表格内容
            for i, row in enumerate(original_table.rows):
                for j, cell in enumerate(row.cells):
                    if i < len(new_table.rows) and j < len(new_table.rows[i].cells):
                        new_table.rows[i].cells[j].text = cell.text
        except Exception as e:
            # 如果表格复制失败，添加一个说明段落
            new_doc.add_paragraph(f"[表格内容 - 复制失败: {str(e)}]")
    
    def generate_filename(self, chapter_title):
        """生成安全的文件名"""
        # 提取ALM编号
        alm_match = re.search(r'ALM-\d+', chapter_title)
        if alm_match:
            alm_code = alm_match.group()
        else:
            alm_code = "ALM-UNKNOWN"
        
        # 提取告警名称（去掉编号部分）
        title_part = re.sub(r'^[\d\.\s]+ALM-\d+\s*', '', chapter_title)
        
        # 清理文件名中的非法字符
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', title_part)
        safe_title = safe_title.strip()
        
        # 限制长度
        if len(safe_title) > 50:
            safe_title = safe_title[:50]
        
        return f"{alm_code}_{safe_title}.docx"
    
    def split_document(self):
        """执行文档拆分"""
        print(f"开始拆分文档: {self.source_docx}")
        print("=" * 60)
        
        # 分析章节
        chapters = self.analyze_chapters()
        
        if not chapters:
            print("未找到可拆分的章节")
            return []
        
        created_files = []
        
        # 拆分每个章节
        for i, chapter in enumerate(chapters):
            print(f"\n处理第 {i+1}/{len(chapters)} 章: {chapter['title']}")
            
            try:
                # 提取章节内容
                content_elements = self.extract_chapter_content(
                    chapter['start_index'], 
                    chapter['end_index']
                )
                
                if not content_elements:
                    print("  ⚠️  章节内容为空，跳过")
                    continue
                
                # 创建新文档
                new_doc = self.create_chapter_document(chapter, content_elements)
                
                # 生成文件名
                filename = self.generate_filename(chapter['title'])
                filepath = os.path.join(self.output_dir, filename)
                
                # 保存文档
                new_doc.save(filepath)
                created_files.append(filepath)
                
                print(f"  ✅ 已保存: {filename}")
                print(f"     段落数: {len([e for e in content_elements if e[0] == 'paragraph'])}")
                print(f"     表格数: {len([e for e in content_elements if e[0] == 'table'])}")
                
            except Exception as e:
                print(f"  ❌ 处理失败: {str(e)}")
                continue
        
        print(f"\n🎉 拆分完成!")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📄 成功创建: {len(created_files)} 个文档")
        
        return created_files


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "split_alarm_documents"
    
    if not os.path.exists(source_file):
        print(f"❌ 错误：找不到源文件 {source_file}")
        return
    
    # 创建拆分器
    splitter = DocumentSplitter(source_file, output_dir)
    
    # 执行拆分
    created_files = splitter.split_document()
    
    # 显示结果
    if created_files:
        print(f"\n📋 创建的文档列表:")
        for i, filepath in enumerate(created_files[:10], 1):  # 只显示前10个
            filename = os.path.basename(filepath)
            print(f"  {i:2d}. {filename}")
        
        if len(created_files) > 10:
            print(f"  ... 还有 {len(created_files) - 10} 个文档")
        
        print(f"\n💡 接下来可以:")
        print(f"   1. 使用转换器逐个转换为Markdown")
        print(f"   2. 转换为PDF")
        print(f"   3. 合并PDF文件")


if __name__ == "__main__":
    main()
