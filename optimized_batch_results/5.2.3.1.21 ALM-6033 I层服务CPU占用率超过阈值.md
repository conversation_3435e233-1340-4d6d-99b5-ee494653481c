# 5.2.3.1.21 ALM-6033 I层服务CPU占用率超过阈值

##### 告警解释
告警采集模块监测IaaS层服务的CPU使用率状态，如果检测到CPU占用率超过阈值，且持续时长达到阈值，则系统产生此告警。
系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
- 重要：I层CPU占用率≥90%
- 次要：80%≤I层CPU占用率＜90%
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6033 | 重要/次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | host：产生告警的主机ID。 |
| 附加信息 | 告警阈值：告警阈值信息<br>主机名：产生告警的主机名。<br>已使用：当前I层服务CPU占用率。<br>Cpu占用详情：当前占用率最高的前3个组件信息。 |
##### 对系统的影响
此告警产生时，系统IaaS层服务的CPU占用率过高，可能会影响组件服务质量。
##### 可能原因
- 资源隔离配置的IaaS层预留CPU核数较少
- 存在人为启动的非OpenStack进程
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 选择“配置”，进入“资源隔离”，查看“资源组配置”中FusionSphere OpenStack隔离的vCPU个数的取值是否较少，标准取值如下表所示。
| 表1 Region Type I级联层 | 表1 Region Type I级联层 | 表1 Region Type I级联层 |
| --- | --- | --- |
| 档位 | FusionSphere OpenStack隔离的vCPU个数 | FusionSphere OpenStack隔离的内存(GB) |
| 50PM, 500VM | 10 | 43 |
| 100PM, 1000VM | 12 | 69 |
| 200PM, 2000VM | 14 | 69 |
| 500PM, 5000VM | 18 | 69 |
| 1000PM, 10000VM | 24 | 69 |
| 2000PM, 20000VM（及更大档位） | 24 | 69 |
| 表2 Region Type II和Region Type III | 表2 Region Type II和Region Type III | 表2 Region Type II和Region Type III |
| --- | --- | --- |
| 档位 | FusionSphere OpenStack隔离的vCPU个数 | FusionSphere OpenStack隔离的内存(GB) |
| 50PM, 500VM | 14 | 53 |
| 100PM, 1000VM | 22 | 78 |
| 200PM, 2000VM | 30 | 135 |
| 500PM, 5000VM | 30 | 135 |
| 1000PM, 10000VM | 34 | 135 |
| 2000PM, 20000VM（及更大档位） | 34 | 135 |
- 是，适当增加取值，请等待界面提示生效后，执行4。
- 否，执行3。
3. 检查BMC上是否有硬件告警。
- 是，参考管理节点中相关章节 ，根据告警类型进行处理，执行4。
- 否，执行5。
4. 等待3~4分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，执行5。
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。