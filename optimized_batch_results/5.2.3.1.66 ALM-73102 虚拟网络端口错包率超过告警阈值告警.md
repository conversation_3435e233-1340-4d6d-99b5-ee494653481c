# 5.2.3.1.66 ALM-73102 虚拟网络端口错包率超过告警阈值告警

##### 告警解释
当虚拟网络端口的错包率达到告警配置中配置的百分比后，系统会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73102 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
可能会导致业务层丢包。
##### 可能原因
OVS网桥上物理端口收到或发送错误报文。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行以下命令，查看虚拟网络监控告警日志，并通过搜索关键字段“vport pkt error overturn happened”来获取错包率超过阈值的虚拟网络端口。
vim /var/log/sysmonitor/unetwork_alarm.log
9. 执行如下命令确认错包的端口是否存在。
ovs-vsctl list interface port_name
port_name为具体的端口名。
例如端口名为tap0，执行命令回显如下所示。
- 如果回显有类似如下内容的返回，说明端口存在，执行10。
图1 端口存在
- 如果回显有类似如下内容的返回，说明端口不存在，请手动清除告警。
图2 端口不存在
10. 执行如下命令，返回端口的所属网桥。
ovs-vsctl iface-to-br port_name
例如端口名为tap0，执行命令后回显如下所示，表示端口tap0属于网桥br0。
图3 端口所属网桥
11. 判断网桥为内核态还是用户态。
假如端口属于网桥br0，执行如下命令，根据返回结果中的datapath_type字段，判断网桥是内核态还是用户态。
ovs-vsctl list br br0
- 图4中datapath_type字段如果为dpdk，则该网桥为用户态网桥，执行13。
- 图4中datapath_type字段如果不是dpdk，则该网桥为内核态网桥，执行12。
图4 datapath_type字段
12. 如果是内核态网桥端口，使用tcpdump抓取端口的收发包，使用wireshark等网络分析工具分析收发的数据包是否正常。
如果无法分析，请执行14。
13. 如果是用户态网桥端口，执行EVS获取端口的收发数据包，分析数据包是否正常。
14. 请联系技术支持工程师协助解决。
##### 参考信息
无。