# 5.2.3.1.9 ALM-6020 主机逻辑磁盘占用率超过阈值

##### 告警解释
OpenStack周期（默认为300s）检测主机逻辑磁盘占用率，当检测到主机逻辑磁盘占用率大于等于系统设置的告警阈值时，系统产生此告警。
系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
- 重要：主机逻辑磁盘占用率≥95%
- 次要：85%≤主机逻辑磁盘占用率＜95%
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6020 | 重要/次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>逻辑磁盘：告警主机上产生告警的逻辑磁盘名称。 |
| 附加信息 | 告警阈值：Minor：85%-95%，Major：>=95%，表示告警阈值。<br>主机名：产生告警的主机名。<br>逻辑磁盘：产生告警的逻辑磁盘名称。<br>总容量：告警的逻辑磁盘的大小。<br>逻辑磁盘路径：告警的逻辑磁盘的挂载点。<br>已使用：当前告警逻辑磁盘使用率。 |
##### 对系统的影响
可能会造成系统的性能下降，并且无法存储系统新产生的数据。
当根分区满时，系统业务将无法正常处理。
当swift分区满时，会造成注册镜像业务失败。
##### 可能原因
主机逻辑磁盘上存储的文件占用空间过大。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 进入告警磁盘所在目录，删除用户自己拷入的文件。
然后根据目录用途选择进一步处理方法。（各分区占用率可通过命令df -h查看。）
- 日志分区（“/var/log”）被占满，执行6。
- “/opt/HUAWEI/image”分区被占满，执行10。
- “/opt/HUAWEI/image_cache”被占满，执行12。
- “/opt/HUAWEI/swift”分区被占满，执行14。
- “/var/ceilometer”分区被占满，执行15。
- 根分区（“/”）被占满，执行17。
- 数据库分区“/opt/fusionplatform/data/gaussdb_data”，执行以下命令判断是否是数据库主节点，其中“$HOSTNAME”是本节点的主机ID：
cps host-template-instance-list $HOSTNAME |grep gaussdb
如果有显示"active"，则表示是数据库主节点，执行21。
- 其他分区，执行20。
6. 执行cd /var/log命令切换到日志分区目录，然后执行以下命令查看大容量的日志文件。
du -ah --max-depth=4|sort -rn|grep -v K |grep -v 0
该命令按照大小列出大于1MB的日志文件。
7. 通过以下两种方式（任选其一）清除查询到的大文件。
如果需要提前备份待删除的日志文件，可将日志文件拷贝到“/home/<USER>
- 执行以下命令将日志文件的内容清空（日志文件不被删除）：
> /var/log/filepath
“filepath”为上一步列出的大于1MB的日志文件的文件路径，例如“./fusionsphere/component/swift-proxy.log”。
使用该方法不会造成分区空间不能回收的现象，但该命令只针对一条日志文件，而不能删除某个组件的整个日志目录。
- 使用rm命令手动删除日志文件。
该方法适合批量删除某目录中的全部日志，单可能造成空间无法回收的问题，需要通过以下方式处理：
如果删除后通过命令df -h查询发现日志空间仍未释放，需要手动查询并重启日志对应的服务：
查询日志对应服务：lsof | grep allfilepath
“allfilepath”为所删除的日志文件（或日志文件目录）的完整文件路径，例如“/var/log/fusionsphere/component/swift-proxy.log”。
- 若有回显：
回显中第一个字段即为该日志对应的服务。
重启该主机上对应服务：service service restart
- 若无回显，则无需处理。
8. 执行如下命令，重新加载日志服务。
service syslog reload
9. 执行以下命令重启mongodb服务。
cps host-template-instance-operate --action stop --service mongodb mongodb
cps host-template-instance-operate --action start --service mongodb mongodb
命令执行完成后，执行20。
10. 在Service OM界面，查询本地盘虚拟机，并手动清除无用的本地盘虚拟机。
等待3分钟~4分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，执行11。
11. 在FusionSphere OpenStack安装部署界面的“配置 > 磁盘”页面，扩容image的分区。
扩容成功后，执行20。
12. “/opt/HUAWEI/image_cache”被占满时，后续是否需要在当前节点上使用新镜像创建卷或虚拟机。
- 是，执行13。
- 否，当前告警不影响业务，处理完毕。
已经被用来创建过卷或虚拟机的镜像，再次被用来创卷或创虚拟机时，当前告警不影响创建。
13. 在FusionSphere OpenStack安装部署界面的“配置 > 磁盘”页面，扩容image-cache的分区。
扩容成功后，执行20。
14. 通过以下两种方式，增加swift分区可用空间。
- 在Service OM界面，单击“资源 > 镜像资源 > 镜像列表”，选择不使用的镜像，单击“删除”，释放swift空间。
- 参考《华为云Stack 6.5.1 扩容指南》中“扩容业务存储资源”章节，扩容swift的分区。
扩容成功后，执行20。
15. 在FusionSphere OpenStack安装部署界面的“配置 > 磁盘”页面，参考下面公式对mongodb的分区进行扩容。
mongodb磁盘占用空间估算（单位GB）：（50*VM+50*PM）*1.5KB/1024/1024
其中VM为虚拟机数，PM为物理主机数，1.5KB是平均单条记录的大小。
16. 扩容成功后，执行以下命令重启mongodb服务。
cps host-template-instance-operate --action stop --service mongodb mongodb
cps host-template-instance-operate --action start --service mongodb mongodb
17. 执行df -h命令查看磁盘分区占用情况，判断“/home/<USER>
- 是，执行22。
- 否，执行18。
18. 执行cd /home/<USER>
du -ah --max-depth=4|sort -rn|grep -v K
该命令按照大小列出大于1MB的文件。
- 若存在大文件，执行19。
- 若不存在，执行22。
19. 使用rm filepath命令手动删除环境中的大文件。“filepath”为上一步列出的大于1MB的文件路径，例如“./filename.tar.gz”。
执行df -h查询根分区占用率是否释放到85%以下。
- 是，执行20。
- 否，执行22。
20. 等待3分钟~4分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，执行22。
21. 执行以下命令检测keystone数据库中的token表是否过大，如果过大则会分批清理过期token。
nohup python /etc/gaussdb/gaussdb/db_occupation_checker.py $CLEAN_TOKENS &
其中“$CLEAN_TOKENS”是选择初始清理token的数量（输入范围10000-50000，推荐初始输入20000）。
由于清理token可能耗时较长，该脚本在后台运行，可以在/var/log/fusionsphere/component/db_occupation_checker/db_occupation_checker.log日志中查看清理任务进展：
- 若打印"The user having most tokens is "，则提示申请token最多的keystone帐户。
- 若打印"Start to clean expired tokens..."，则提示开始清理token。
- 等待打印"Vacuum tokens successfully..."，则表示清理过期token完成。
清理token完成不能保证立即释放token所占用的磁盘空间，但不会影响新token的申请。
如果日志打印如下提示，请执行22。
- 若打印"The 'base' directory is not one of the large files(dir) in gaussdb partition, please contact technical staff to check other files."，提示数据库表物理文件所在的目录（base）不是磁盘占用最大的文件
- 若打印"The largest file(base)'s size (kb) is more smaller than total partition size (kb), please contact technical staff."，提示数据库表物理文件所在的目录（base）小于整个分区大小的70%
- 若打印"Keytone database is not the largest database, please contact technical staff to check the other database:"，提示keystone不是磁盘占用最大的数据库
- 若打印"The token table is not the largest table, please contact technical staff to check the other table"，提示keystone数据库中token表不是占用磁盘最大的数据库
22. 请联系技术支持工程师协助解决。
##### 参考信息
无。