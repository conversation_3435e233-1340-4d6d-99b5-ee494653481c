# 5.2.3.1.105 ALM-1223017 负载均衡器后端实例不在线

##### 告警解释
ELB proxy组件周期收集后端member的健康检查状态，如果存在member不在线，则上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223017 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>负载均衡器ID：弹性负载均衡的ID。 |
| 附加信息 | 弹性负载均衡器名称：弹性负载均衡的名称。<br>租户ID：项目的ID。<br>离线后端实例：健康检查为离线的后端服务器的信息，包括名称、IP地址。 |
##### 对系统的影响
影响业务实际负载均衡能力，业务不会分发给离线的后端，导致业务集中于在线的后端上处理。
##### 可能原因
后端服务器上的业务进程异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“告警 > 当前告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 获取附加信息和定位信息。
5. 登录FusionSphere OpenStack首节点，获取ManageOne-DB01的IP地址。
- 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令，防止系统超时退出。
TMOUT=0
- 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
- 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
- 执行以下命令，获取ManageOne-DB01的IP地址。
nova list
6. 登录ManageOne-DB01节点，获取上报告警的租户名称。
- 使用PuTTY，通过ManageOne-DB01节点IP地址登录节点。
默认帐号：sopuser，默认密码：*****。
- 执行如下命令，切换到root帐号。
sudo su root
默认密码：*****。
- 分别执行以下命令，获取告警附加信息中租户ID所属的租户名称。
projectid=告警附加信息中租户ID
port=$(su ossadm -c '/opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-db-instance -type zenith'|grep 'tenantdbsvr'|grep 'Master'|awk -F' ' '{print$7}')
u - dbuser -c '/opt/zenith/app/bin/zsql movdcservicedb/*****@127.0.0.1:'$port' -c "select * from PROJECTS where id='\'$projectid\''";'
7. 根据租户名称，获取VDC管理员登录的帐户。
- 使用浏览器，以运营管理员帐号登录ManageOne。
- 非B2B场景登录地址：https://ManageOne运营面的访问地址。例如，https://console.demo.com。
- B2B场景登录地址：https://ManageOne运营管理面的访问地址。例如，https://admin.demo.com。
运营管理员默认帐号为bss_admin，默认密码为*****。首次登录需要修改密码，如果不是首次登录，请获取新密码。
- 选择“租户 > 租户管理”，在租户列表中查找6.c中获取的租户名称，单击租户名称，进入租户概览页面。在左侧菜单中选择“用户管理”，获取用户名称。若密码忘记，可以重置密码。
8. 使用浏览器，以7中获取的VDC管理员帐号登录ManageOne。
- 非B2B场景登录地址：https://ManageOne运营面的访问地址。例如，https://console.demo.com。
- B2B场景登录地址：https://ManageOne租户面的访问地址。例如，https://tenant.demo.com。
9. 在导航栏左上角下拉框选择区域和项目，选择“控制台 > 网络 > 弹性负载均衡”，进入弹性负载均衡页面。
10. 根据4中获取的“弹性负载均衡器名称”和“负载均衡器ID”，找到对应的elb实例，单击实例名，进入详情页面。
11. 单击“后端云服务器组”，进入“后端云服务器组”页签。
在页签的右侧，选择按“ID”搜索，在输入框内输入从4中获取的“离线后端实例”中的“pool_id”，搜索到对应的pool。
12. 单击“后端云服务器数量”列上的数字，查看该pool下的所有后端实例。
根据4中获取的“离线后端实例”中member的名称和ip信息，找到离线的后端云服务器。
13. 单击对应的云服务器名称，进入后端云服务器详情页面。
14. 单击“远程登录”，登录云服务器，并联系租户查看并处理异常的业务进程。
15. 处理完成后，等待五分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，请联系技术支持协助解决。