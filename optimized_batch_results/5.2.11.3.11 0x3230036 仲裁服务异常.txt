# 5.2.11.3.11 0x3230036 仲裁服务异常

##### 告警解释
仲裁服务异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230036 | 紧急 | 是 |
##### 对系统的影响
可能导致故障发生时，业务不能恢复。
##### 可能原因
- eReplication仲裁服务配置错误。
- 仲裁服务器证书校验失败。
- 仲裁服务内部错误。
##### 处理步骤
1. 在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“Third_site_IP_of_Arbitration_Servic”、“Arbitration_DC1_01_API”、“Arbitration_DC1_02_API”、“Arbitration_DC2_01_API”、“Arbitration_DC2_02_API”获取仲裁服务器的IP地址，搜索“csha_region_map_info”对应值中“|”符号后面对应的值为AZ配置信息。
2. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
3. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
4. 执行cd /opt/BCManager/Runtime/bin && sh configArbitration.sh -a命令，回显中localAZ，PeerAZ，ArbIP行对应的配置值与1获取的信息是否相同。
- 是，请转5。
- 否，在《OceanStor BCManager 8.0.0 eReplication 用户指南》中搜索configArbitration.sh命令，按指导重新配置仲裁服务器信息，等待10秒钟后重新查询仲裁服务，检查告警是否清除。如果未清除，请转5。
5. 执行cd /opt/BCManager/Runtime/LegoRuntime/certs;../../jre6.0.18/bin/keytool -list -v -keystore arb.keystore命令检查仲裁服务器证书是否过期（密钥库默认密码：*****）。回显中“Valid from”行对应的“until”是否已经超过当前日期。若超过，表示证书已经过期。
- 是，获取最新的仲裁服务器证书，并参考《OceanStor BCManager 8.0.0 eReplication 用户指南》的“替换仲裁服务器证书” 章节完成证书替换，然后检查告警是否清除，如果已清除，流程结束，否则请转6。
- 否，请转6。
6. 请联系技术工程师协助解决。
##### 参考信息
无。