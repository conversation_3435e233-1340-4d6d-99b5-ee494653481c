# ********.1 ALM-2000904 haproxy服务异常

告警解释
当HAProxy进程异常或HAProxy浮动IP未绑定到对应网卡上时，系统产生此告警，提示系统故障或者风险。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000904 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
当各服务调用HAProxy服务转发消息时，无法正常转发。
可能原因
- HAProxy进程异常。
- HAProxy浮动IP未绑定到对应网卡上。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，登录4中确认的虚拟机。
默认帐号：ulb，默认密码：*****。
6. 执行以下命令，防止PuTTY超时退出。
TMOUT=0
7. 执行以下命令切换到root用户。
sudo su - root
默认帐号：root，默认密码：*****。
8. 执行以下命令，重启HAProxy后观察是否仍有告警。
service haproxy restart
- 是，执行9。
- 否，处理完毕。
9. 执行以下命令，检查浮动IP是否绑定成功。
ip a
回显信息中包含告警的不可达的浮动IP，表示已绑定成功。
- 是，执行10。
- 否，重新分配浮动IP。
10. 检查浮动IP是否与HAProxy节点HAPROXY01和HAPROXY02在同一个网段。
- 是，执行11。
- 否，重新分配浮动IP。
11. 分别在HAProxy两个节点HAPROXY01和HAPROXY02执行以下命令，检查是否都配置了该浮动IP。
cat /home/<USER>/keepalived/etc/keepalived/keepalived.conf
回显如下所示，表示已配置。
! Configuration File for keepalived
vrrp_script chk_http_port {
script "/home/<USER>/keepalived/script/check_haproxy.sh"interval 2weight 20
}
vrrp_instance VI_1 {
state BACKUP
nopreempt
interface eth0
virtual_router_id 162
priority 100
advert_int 1
virtual_ipaddress {
IP地址
……
}
track_script {
chk_http_port
}
notify_master "/home/<USER>/keepalived/script/notify.sh master"
notify_backup "/home/<USER>/keepalived/script/notify.sh backup"
}
- 是，执行12。
- 否，请在HAProxy的两个节点均配置该浮动IP。
12. 执行以下命令，检查浮动IP是否与其他IP冲突。
arping 浮动IP
如果出现两个MAC地址，则表示浮动IP冲突。
- 是，重新分配浮动IP。
- 否，请联系技术支持工程师协助解决。
参考信息
无。