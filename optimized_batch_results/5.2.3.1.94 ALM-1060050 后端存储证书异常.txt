# 5.2.3.1.94 ALM-1060050 后端存储证书异常

##### 告警解释
用户对接后端存储，并且在对接时开启了“使用存储证书”开关。此时，若后端存储设备证书发生变化，而且未在FusionSphere OpenStack上同步最新证书，则会出现该告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060050 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>可用分区：产生告警的后端存储所属可用分区<br>后端存储管理地址：产生告警的后端存储设备管理地址 |
##### 对系统的影响
无
##### 可能原因
用户对接后端存储，并且在对接时开启了“使用存储证书”开关，后端存储设备证书进行了证书更换，但是未在FusionSphere OpenStack上同步最新证书。
##### 处理步骤
1. 根据告警附加信息中的后端存储管理地址，访问后端存储设备，获取存储设备最新的CA证书。
常用设备证书的手动获取方式如下，如果获取不到，请联系技术支持工程师协助解决。
- OceanStor V3/OceanStor V5/Dorado V3的CA证书
使用文件传输工具（如WinSCP），以admin帐户登录V3/V5阵列，将CA证书（“cacert.pem”或“ca.crt”）拷贝到本地任意目录。不同的设备型号CA证书的存放路径不同。如：若存储阵列版本是V300R006C10及其之后的版本，则CA证书存放在阵列的“/OSM/coffer_data/omm/export_import”目录下。若是V300R006C10之前的版本，则CA证书存放在阵列的“/OSM/export_import/”目录下。
- VMware的CA证书
使用浏览器登入VMware vCenter环境，下载证书压缩包至本地任意目录。下载后，证书压缩包文件类型修改为“.zip”格式，修改完成后，打开证书压缩文件，查找“*.0”格式文件，并修改其文件类型为“*.crt”格式。
- OceanStor 9000的对象存储服务的CA证书
请根据OceanStor 9000的具体版本，在support网站获取相应的资料，使用OceanStor 9000自带的对象存储服务证书工具生成证书。比如，请参见《OceanStor 9000 V300R006C00 安全维护》中“更换对象存储服务（兼容Amazon S3接口）的安全证书”章节的“异常处理”。
- FusionStorage的CA证书
请根据FusionStorage的具体版本，在support网站获取相应的资料，使用FusionStorage自带的对象存储服务证书工具生成证书。比如，请参见《FusionStorage V100R006C20 对象存储服务安全维护》中“更换对象存储服务（兼容Amazon S3接口）的安全证书”章节的“异常处理”。
2. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
3. 选择“运维 > 证书管理 > 后端存储证书管理” 。
4. 根据告警定位信息中的后端存储名称，在“证书更新”里勾选对应的后端存储，单击“后端存储证书”所在行的，选择1中获取的有效文件，单击“打开”。
5. 单击“上传”，待证书上传成功后，单击“提交”。
6. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
7. 查看告警列表中对应告警是否恢复。
- 是，处理完毕。
- 否，执行8。
8. 请联系技术支持工程师协助解决。
##### 参考信息
无。