# 5.2.4.3.1 ALM-100550 LVS配置失败告警

##### 告警解释
服务主动调用MORCLVSService服务的注册接口，当服务向MORCLVSService服务注册生成配置文件失败时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100550 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 发生告警的云服务名称。 |
| 服务 | 发生告警的服务名称。 |
| 节点名称 | 发生告警的节点名称。 |
| IP地址 | 发生告警的服务所在服务器的IP地址。 |
##### 对系统的影响
告警发生之后，上报到LVS（Linux Virtual Server）的告警无法分发到注册失败的服务节点。
##### 可能原因
- MORCLVSService安装部署失败。
- 系统平台在安装MORCLVSService下RCLVSService.zip包失败。
##### 处理步骤
1. 登录部署面查看LVS安装部署是否失败。
- 使用浏览器登录ManageOne部署面。
登录地址：https://部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“产品 > 部署产品软件”。
- 单击“ManageOne”产品对应的卡片。
- 单击“部署历史”中最新部署的那一行对应的“操作”列的“详细信息”，并在搜索框中输入MORCLVSService微服务所属的服务名称“MODriverFramework”，单击“Enter”，查询该服务的部署列表。
如果未查询到该服务对应的部署列表，则返回“部署历史”，在“部署历史”中下一行对应的“操作”列的“详细信息”，继续查询，以此类推。
- 找到“特性”列为“MOAdmin”对应的实例名称，并单击该实例名称。
- 在“部署历史”中查看最新部署的那一行对应的“部署状态”列单击“成功”。
- 在“资源部署状态”中找到“名称”包含MORCLVSService微服务且“类型”为“Stage”的资源名称，查看“部署状态”列是否显示“成功”。
- 是，请联系技术支持工程师协助解决，重新部署MORCLVSService服务。
- 否，请执行2。
2. 查看在安装部署MORCLVSService时是否正确安装了RCLVSService.zip包。
- 使用PuTTY，通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP。
默认帐号：sopuser，默认密码：*****
- 执行如下命令，切换到root帐号。
sudo su - root
默认密码：*****
- 执行如下命令，进入/opt/envs目录。
cd /opt/envs/
- 执行如下命令，查看是否有RCLVSService目录。
ll
如果回显信息中包含“RCLVSService”，则表明有RCLVSService目录。
- 是，请执行3。
- 否，请联系技术支持工程师协助解决。
3. 查看RCLVSService目录中RCLVSService.zip包的内容是否完整。
- 执行如下命令，进入RCLVSService目录。
cd RCLVSService
- 执行如下命令，查看RCLVSService.zip包的内容是否完整。
ll
完整的回显示例如下。
- 是，收集上述告警处理过程中的信息，请联系技术支持工程师协助解决。
- 否，请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
以下介绍查找节点对应的管理IP地址的方法。
4. 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
5. 输入用户名、密码，单击“登录”。
6. 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
7. 在“节点名称”列查找待查询管理IP地址的节点。
8. 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。