# ********.19 0x20100031000C 证书校验失败

##### 告警解释
在服务器（IP：[NodeIP]）上的微服务（[MicroService_Name]）与数据库（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000C | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |
##### 对系统的影响
与数据库之间的连接存在安全风险。
##### 可能原因
系统中不存在连接该数据库的CA证书或CA证书已过期。
##### 处理步骤
- 可能原因1：系统中不存在连接该数据库的CA证书或CA证书已过期。
- 请联管理员获取未过期的数据库证书文件，将其重命名为“cacert.pem”。
- 通过WinSCP工具将“cacert.pem”文件拷贝到上报告该警的服务器的“/home/<USER>
默认帐户：hcp，默认密码：*****
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行mv /home/<USER>/cacert.pem /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf命令，将证书文件移动到对应微服务的配置目录中。其中ebk_xxx为微服务名称。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf命令，进入证书保存目录。
- 执行chmod 600 cacert.pem命令，将“cacert.pem”文件的权限设置为600。
- 执行chown hcpprocess:hcpmgr cacert.pem命令，将“cacert.pem”文件的所有者修改为hcpprocess。
- 执行service hcp restart命令，重启服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无