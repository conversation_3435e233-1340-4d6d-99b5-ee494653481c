# 5.2.3.1.70 ALM-73109 UVP关键进程内存占用率超过阈值告警

##### 告警解释
按5秒周期检测虚拟化关键进程内存使用值，当虚拟化关键进程内存使用值大于等于告警设置的阈值，系统产生此告警。当虚拟化关键进程内存使用值小于恢复阈值时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73109 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
虚拟化平台可用内存减少，甚至内存不足。
##### 可能原因
- 虚拟化管理进程内存泄露。
##### 处理步骤
1. 通过告警对象确定异常进程名称。
- 如果进程名称为libvirtd、hirmd、vBMC_agentd、UVPHostd、virtlogd、systemd-journal、dbus-daemon、rsyslogd、sysalarm、sysmonitor、getosstat，请执行2。
- 如果进程名称为systemd，请执行3。
- 如果进程名称为ovs-vswitchd、ovsdb-server，请执行4。
- 如果进程名称为虚拟机名称，请执行5。
2. 通过PuTTY登录主机，并切换至root用户，执行如下命令，重启进程对应的服务（等待30秒钟确认告警是否自动消除）。
systemctl restart ${SERVER_NAME}
${SERVER_NAME}为进程对应的服务名称。
以下几个进程名称对应的${SERVER_NAME}需要重新指定：
- 进程systemd-journal对应的服务名为systemd-journald；
- 进程dbus-daemon对应服务名为dbus；
- 进程rsyslogd对应的服务名为rsyslog。
- 是，执行完毕。
- 否，请执行6。
3. 通过PuTTY登录主机，并切换至root用户，执行如下命令，重新加载systemd（等待30秒钟确认告警是否自动消除）。
systemctl daemon-reload
- 是，执行完毕。
- 否，请执行6。
4. 执行uvplog -o host -t hyp命令，收集回显目录中日志，并请联系技术支持工程师协助解决。
5. 收集/var/log/libvirt/目录下的日志，并请联系技术支持工程师协助解决。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。