# ********.89 0x10E01A0015 删除FusionStorage卷失败

##### 告警解释
在Proxy（管理平面IP address：[IP_addr]）中，删除卷(卷名：[Vol_name]，FusionStorageManager：[DSWare_IP])失败，显示错误信息([errMsg])。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0015 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| DSWare_IP | FusionStorageManager地址。 |
| Vol_name | 卷名称。 |
| errMsg | 错误信息。 |
##### 对系统的影响
造成备份或恢复任务失败。
##### 可能原因
- Proxy与受保护环境所在服务器间物理连接中断。
- 要删除的卷没有进行卸载。
##### 处理步骤
- 可能原因1：备份代理与受保护环境所在服务器间连接中断。
- 在当前告警界面查看ID为0x10E0140000（连接受保护环境失败）或0x10E0140001（扫描受保护环境失败）的告警是否上报。
- 是，请先处理1.a所述告警，保证Proxy与受保护环境网络连接正常。
- 否，执行1.b。
- 根据告警中的卷信息执行命令“/opt/huawei-data-protection/ebackup/vbstool/vrmVBSTool.sh --op deleteVolume --dsaIp 127.0.0.1 --dswareFloatIP DSWareIP --volName VolumeName”手动删除卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看返回码是否为0。
- 是，处理结束。
- 否，执行2。
- 可能原因2：要删除的卷没有进行卸载。
- 根据告警中的卷信息执行命令“/usr/bin/vbs_cli -c detachwithip -v VolumeName -i DSWareIP -p 0”手动卸载卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看返回码是否为0。
- 是，执行2.b。
- 否，请联系技术支持工程师协助解决。
- 执行命令“/opt/huawei-data-protection/ebackup/vbstool/vrmVBSTool.sh --op deleteVolume --dsaIp 127.0.0.1 --dswareFloatIP DSWareIP --volName VolumeName”手动删除卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看返回码是否为0。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。