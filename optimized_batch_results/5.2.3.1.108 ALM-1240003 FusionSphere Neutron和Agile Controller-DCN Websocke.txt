# 5.2.3.1.108 ALM-1240003 FusionSphere Neutron和Agile Controller-DCN Websocket连接故障

##### 告警解释
Agile Controller-DCN的云平台插件定时检测与控制器的Websocket通道的联通状态。当Websocket连通正常时，检测周期为10秒/次，当检测到一次失败后，检测周期为5秒/次。当连续检测到2次失败时，上报告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1240003 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>主机名：产生告警的主机名称<br>本端地址：产生告警的主机地址信息<br>对端地址：产生告警的主机对接的Agile Controller-DCN的地址信息<br>详细信息：产生告警的详细信息 |
##### 对系统的影响
当FusionSphere与Agile Controller-DCN控制器之间的Websocket通道连接失败时，FusionSphere无法接收来自控制器的状态上报。
##### 可能原因
- FusionSphere与控制器的Websocket网路链路不通。
- FusionSphere向控制器获取TOKEN鉴权失败。
- Agile Controller-DCN控制器故障。
##### 处理步骤
1. 登录Agile Controller-DCN业务面界面，网址是https://{Agile Controller-DCN北向浮动IP}:31943。
2. 选择“集成 > 云平台 > OpenStack”，进入OpenStack页面。
3. 查看该FusionSphere节点对应的云平台详情。
查看“Websocket链路状态”是否在线。
- 是，则当前通信业务下发链路正常，等待一段时间，执行18。
- 否，执行4。
4. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
5. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
6. 执行以下命令，防止系统超时退出。
TMOUT=0
7. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
8. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
9. 执行以下命令，进入neutron-server的日志目录。
cd /var/log/fusionsphere/component/neutron-server
10. 执行以下命令，检查neutron-server的实时报错信息。
tailf neutron-server_error.log.200
- 如果报错中包含“Authorization Failed”鉴权失败的信息，执行11。
- 如果报错中包含“There is no terminal from the ip”连接失败的信息，执行15。
- 如果有其他未知报错，执行19。
11. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
12. 选择“配置 > OpenStack > Neutron”。
13. 在“Neutron扩展配置项”右侧，单击“配置”按钮，进入“Neutron扩展配置项”界面。
14. 正确配置控制器北向帐户的帐户密码，单击“提交”，执行18。
15. 参考11~13，进入“Neutron扩展配置项”界面，检查host_ip是否配置为正确的Agile Controller的北向浮动IP。
- 是，执行16。
- 否，请正确配置控制器北向浮动IP，单击“提交”， 执行18。
16. 以root登录控制器节点，执行命令netstat -anp | grep 18010，检查端口监听。
- 如果至少1个节点有正常的监听进程，则执行17。
- 如果没有监听进程，请联系控制器技术支持工程师协助排查控制器问题，执行18。
17. 以root用户再次登录OpenStack节点，检查FusionSphere与控制器之间的物理链路是否正常。以控制器的北向浮动IP为************为例，执行ping -I external_api ************和ping -I external_om ************。
- 若有ping不通的情况，请联系网络规划工程师协助打通该网络平面的链路，执行18。
- 若均能ping通，执行19。
18. 检查告警是否自动清除。
- 是，处理完毕。
- 否，执行19。
19. 请联系技术支持工程师协助解决。
##### 参考信息
无。