# 5.2.3.1.25 ALM-6038 主机磁盘占用率超过阈值

##### 告警解释
OpenStack按300秒周期检测主机磁盘占用率（即主机的所有逻辑分区的使用量总和占所有逻辑分区总量的百分比）。当检测到主机磁盘占用率大于等于系统设置的告警阈值时，系统产生此告警。
- 系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
- 紧急：主机磁盘占用率≥95%
- 重要：85%≤主机磁盘占用率＜95%
- 本告警与ALM-6020 主机逻辑磁盘占用率超过阈值告警的区别在于，ALM-6020针对主机上的各个逻辑分区分别做检测，若单个逻辑分区的使用率达到设置的阈值，则上报告警。而本告警针对所有逻辑分区的总量做检测，当主机的所有逻辑分区的使用量总和占所有逻辑分区总量的百分比达到阈值时，才上报告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6038 | 紧急/重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | host_id：产生告警的主机ID。 |
| 附加信息 | 告警阈值：Major：85~95%，Critical：>= 95%，表示告警阈值。<br>主机名：产生告警的主机名。<br>已使用：当前磁盘占用率。 |
##### 对系统的影响
- 可能会造成系统的性能下降，并且无法存储系统新产生的数据。
- 当某些分区（如根分区）满时，系统业务将无法正常处理。
##### 可能原因
主机逻辑磁盘上存储的文件总量占用空间过大。
##### 处理步骤
1. 在告警定位信息中获取主机ID。
2. 在告警界面查看该主机ID是否存在6020告警。
- 是，执行3。
- 否，执行5。
3. 参考ALM-6020 主机逻辑磁盘占用率超过阈值章节，清除6020告警。
4. 待6020告警清除完成后，等待4~5分钟，查看6038告警是否恢复。
- 是，处理完毕。
- 否，执行5。
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。