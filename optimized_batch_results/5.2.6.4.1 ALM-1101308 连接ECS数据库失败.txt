# 5.2.6.4.1 ALM-1101308 连接ECS数据库失败

告警解释
当组合 API的 数据库节点因各种原因宕机,或网络故障导致组合 API 节点无法连接数据库时,产生此条告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101308 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源设备名称 | 来源设备名称 | 产生告警信息的设备名称 |
| 监控系统名称 | 监控系统名称 | 对接系统的类型 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 发生时间 | 发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | HostIP | 组合 API 主机 IP |
| 附加信息 | CloudService | 云服务名称 |
| 附加信息 | Service | 服务名称 |
| 附加信息 | DatabaseIP | 组合 API 数据库 IP |
对系统的影响
ECS、EVS、IMS等在组合API中托管的服务无法正常使用。
可能原因
- 组合API的数据库节点服务异常。
- 网络异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”，在界面上方导航栏选择“集中告警 > 当前告警”，查询该告警的附加信息及定位信息。
3. 查看告警附加信息的“DatabaseIP”，使用ping命令，检查网络连通性。
4. 如果网络连接存在问题，请优先排除网络连接故障。如果经检查网络无故障，参考登录FusionSphere OpenStack后台和导入环境变量，登录FusionSphere Openstack后台，执行6。
5. 在FusionSphere OpenStack后台，登录数据库虚拟机节点。执行以下命令，查询PUB-DB-01数据库节点IP。
nova list --all-t | grep PUB-DB-01
执行以下命令，切换到fsp用户。
su fsp
执行以下命令，切换到虚拟机数据库节点。
ssh gaussdb@数据库节点IP
帐号：gaussdb，默认密码：*****。
6. 执行以下命令，切换到root用户。
sudo su - root
帐号：root，默认密码*****。
执行以下命令，防止会话超时退出。
TMOUT=0
7. 执行以下命令，查询当前虚拟机是否为主虚拟机。
service had query
- 如果回显信息中包含“active”，则当前虚拟机为主虚拟机。
- 如果回显信息中不包含“active”，则当前虚拟机为备虚拟机。
回显信息类似如图1所示，查看回显信息中，主备节点的状态“STAT”栏是否均为“Normal”。
图1 数据库状态
8. 执行以下命令，检查数据库配置是否正确。
service gaussdb query
- “LOCAL_ROLE”的参数值为“Standby”。
- “PEER_STATE”的参数值为“Normal”。
- “SYNC_PERCENT”的参数值为“100%”。
回显信息类似如图2所示，请关注红框内信息是否正确。
图2 数据库配置信息
9. 如果数据库服务存在异常，请联系技术支持工程师协助解决。
参考信息
无。