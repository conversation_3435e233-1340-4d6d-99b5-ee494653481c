# ********.1 ALM-servicemonitor_heartbeat 服务监控节点心跳异常告警

********.1.2 ALM-servicemonitor_heartbeat 服务监控节点心跳异常告警
##### 告警解释
当MOSMAccessService服务所在节点心跳异常（即服务监控AP节点心跳异常）时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_heartbeat | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务名称 | 产生告警的服务名称。 |
| 组件名称 | 产生告警的IP地址或虚拟机名称。 |
| 组件类型 | 产生告警的实例类型。 |
##### 对系统的影响
当MOSMAccessService服务所在节点心跳异常，会影响到AP节点所在区域监控数据的上报，造成监控数据异常，告警数据异常。
##### 可能原因
- MOSMAccessService服务所在节点进程停止。
- MOSMAccessService服务所在节点所在虚拟机电源状态异常。
##### 处理步骤
1. 查看上报告警的主机IP信息和所对应的虚拟机主机名称。
- 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取上报告警的主机IP信息。
- 通过主机IP确定虚拟机主机名称。
- 在菜单中选择“运维工具箱 > 服务监控”，进入服务监控页面。
- 单击“监控看板 > 节点列表”，进入节点列表页面。
- 在页面右上角搜索框输入上报告警的主机IP进行搜索，节点对应的名称就是虚拟机主机名称。
2. 查看上报告警的IP节点所对应虚拟机“电源状态”是否为“运行中”。
- 使用浏览器登录Service OM。
- 登录ManageOne运维面，在页面上方的导航栏，选择“运维地图”，进入“运维地图”页面。
- 在“运维地图”页面右侧的“快速访问”导航栏中，单击“Service OM”，进入Service OM界面。
- 在主菜单选择“资源 > 计算资源”，进入“计算资源”页面。
- 在“虚拟机”页签搜索虚拟机主机名称，查看虚拟机的“电源状态”是否为“运行中”。
- 是，执行5。
- 否，单击“操作”列中的“更多 > 重启”，使虚拟机的“电源状态”为“运行中”。如果重启不成功，则执行5。
3. 查看上报告警的IP节点进程是否已经停止运行。
- 使用Putty以SSH方式通过sopuser用户登录上报告警的主机IP，执行如下命令切换root用户。
sudo su root
- 执行如下命令查看MOSMAccessService服务进程是否在运行中。
ps -ef | grep "MOSMAccessService"
- 回显信息如下，表示进程是运行中状态，执行5。
java -Dfile.encoding=UTF-8 -Dlog.dir=/opt/log/oss/Product/MOSMService/mosmservice-3-0 -DNFW=mosmservice-3-0 -DTOMCAT_LOG_DIR=/opt/log/oss/Product/MOSMService/mosmservice-3-0/tomcatlog -DTOMCAT_WORK_DIR=/opt/share/Product/MOSMService/mosmservice-3-0/tomcatwork -DNFW=mosmservice-3-0 -Dprocname=mosmservice-3-0 -Xss256k -XX:NativeMemoryTracking=detail -XX:+UseParallelGC -XX:+UseAdaptiveSizePolicy -XX:GCTimeRatio=49 -XX:+UseParallelOldGC -server -XX:MaxDirectMemorySize=128m -Xms256m -Xmx512m -Xmn128m -Xss256K -XX:MaxPermSize=64m -XX:InitialCodeCacheSize=32m -XX:ReservedCodeCacheSize=64m -Djdk.nio.maxCachedBufferSize=128k -XX:MetaspaceSize=32m -XX:MaxMetaspaceSize=128m -XX:+CrashOnOutOfMemoryError -XX:-UseLargePages -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:-UseBiasedLocking -XX:AutoBoxCacheMax=20000 -XX:NativeMemoryTracking=detail -XX:CompileThreshold=100000 -XX:MaxInlineSize=2 -XX:OnStackReplacePercentage=10000 -XX:MaxInlineLevel=1 -XX:InlineSmallCode=20 -XX:-InlineSynchronizedMethods -XX:CICompilerCount=2 -XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses -Dbsp.app.datasource=mosmdb -Xms256m -Xmx256m -XX:InitialCodeCacheSize=8m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:ReservedCodeCacheSize=256m -XX:MaxDirectMemorySize=256m -DMOSCALE=B2B200VM com.huawei.mo.jetty.JettyBootUp MOSMService,MOSMAccessService,MOSMDataService
- 否则，进程已经停止运行，执行4。
4. 重启对应服务进程。
- 使用Putty以SSH方式通过sopuser用户登录报告警的主机IP，执行如下命令切换root用户。
sudo su root
- 执行如下命令，启动主进程。
su ossadm -c ". /opt/oss/manager/agent/bin/engr_profile.sh;ipmc_adm -cmd startapp -app MOSMService"
- 回显信息如下，表示进程启动成功。否则，进程启动失败，执行5。
Starting process mosmservice-x-x ... success
- 如果进程启动成功，等待10分钟，查看告警是否清除。
- 是，结束。
- 否，执行5。
5. 请联系技术支持工程师协助解决。
##### 告警清除
当服务监控AP节点心跳正常时，自动清除此告警。
##### 参考信息
无。