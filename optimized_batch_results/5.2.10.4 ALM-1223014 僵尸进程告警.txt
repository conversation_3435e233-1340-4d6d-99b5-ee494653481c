# 5.2.10.4 ALM-1223014 僵尸进程告警

##### 告警解释
ELB API每30分钟秒检测后端服务节点业务进程，如果进程显示为无响应状态，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223014 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
影响业务性能或者业务完全无法处理。
##### 可能原因
管理节点进行僵尸进程检查时集群主机（LVS或Nginx）存在僵尸进程。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：对端地址
5. 使用PuTTY，登录出现僵尸进程的虚拟机节点，虚拟机节点可以是LVS节点或Nginx节点。
登录地址：LVS节点或Nginx节点的IP地址，节点IP地址为4中询到的对端地址。
默认帐户：elb
默认密码：*****。
6. 5中登录的节点为LVS节点，请执行7~10。
5中登录的节点为Nginx节点，请执行11~14。
7. 执行以下命令，查看keepalived进程和nginx进程是否运行正常。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "keepalived" | awk 'END{print NR}'
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关进程运行正常。
- 是，请执行15。
- 否，请执行8。
8. 执行以下命令，获取僵尸进程的父进程ID。
ps -A -o stat,ppid,cmd | grep keepalived | grep -e '^[Zz]' | awk '{print $2}'
ps -A -o stat,ppid,cmd | grep nginx | grep -e '^[Zz]' | awk '{print $2}'
9. 执行以下命令，清除僵尸进程的父进程。
kill -s 9 <pid>
其中<pid>为8查询出的进程ID。
清除僵尸进程不会对其他进程造成影响。
10. 执行以下命令，查看僵尸进程是否被清除。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "keepalived" | awk 'END{print NR}'
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关僵尸进程已被清除。
- 是，请执行15。
- 否，请联系技术支持工程师协助解决。
11. 执行以下命令，查看nginx进程是否运行正常。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关进程运行正常。
- 是，请执行15。
- 否，请执行12。
12. 执行以下命令，获取僵尸进程的父进程ID。
ps -A -o stat,ppid,cmd | grep nginx | grep -e '^[Zz]' | awk '{print $2}'
13. 执行以下命令，清理僵尸进程的父进程。
kill –s 9 <pid>
其中<pid>为12查询出的进程ID。
清除僵尸进程不会对其他业务造成影响。
14. 执行以下命令，查看僵尸进程是否清除。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
命令回显0，表示僵尸进程已被清除。
- 是，请执行15。
- 否，请联系技术支持工程师协助解决。
15. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。