# 5.2.3.1.49 ALM-70251 聚合网口状态异常

##### 告警解释
配置网络分组中的bond时，如果bond的从属网口状态异常则产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70251 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>聚合网口名：产生告警的bond名称 |
| 附加信息 | 详细信息：<br>聚合网口名：产生告警的bond名称<br>主机ID：产生告警的主机ID<br>主机名：产生告警的主机名称 |
##### 对系统的影响
bond的模式，包括主备、负载均衡，无法完全生效。
##### 可能原因
在节点中生效的bond，如果只存在一个从属网口，或者从属网口中存在非法网口造成bond功能无法全部生效，产生该告警。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 根据告警详细信息选择处理步骤。
- 告警信息显示bond的从属网口只有1个且不存在unknown的网口，执行7。
- 告警信息显示bond的从属网口大于1个且存在unknown的网口，执行8。
- 告警信息显示bond的从属网口大于1个且不存在unknown的网口，执行12。
7. bond下只有一个从属网口，需要增加bond的从属网口数量，有如下两种处理方式：
- 主机上未运行业务时，登录FusionSphere OpenStack安装部署界面，选择“配置 > 网络 > 网口映射配置”页面，根据告警定位信息中的“主机ID”，找到告警主机所在的主机组。删除聚合网口bond，选择可用的空闲nic网口，重新创建bond。
- 主机上存在业务时，切换到PuTTY执行以下命令，更新bond的从属网口：
cps hostcfg-item-update --item bond --bond-name trunk_name --slaves nic_name_1,nic_name_2,... --type network group_name
cps commit
其中， trunk_name从告警信息中获取，nic_name可通过“cat /usr/bin/ports_info |python -mjson.tool|grep eth”查看，group_name可通过cps hostcfg-list --type network命令查看分组名称。
例如：
cps-hostcfg-item-update --item bond --bond-name trunk1 --slaves nic3,nic4 --type network group1
cps commit
执行成功后，执行15。
8. 登录FusionSphere OpenStack安装部署界面，选择“配置 > 网络 > 网口映射配置”页面，根据告警定位信息中的“主机ID”，找到告警主机所在的主机组。切换到PuTTY，通过cli命令查询告警bond从属口的option属性是否是AUTO_PXE。
cps hostcfg-show --type network 主机组
- 如果unknown的从属网口属于AUTO_PXE类型，执行9。
- 如果unknown的从属网口不属于AUTO_PXE类型，执行10。
9. 在主机上规划一张物理网卡，适配给unknown的从属网口：
- 切换到PuTTY，执行命令cat /usr/bin/ports_info |python -mjson.tool|grep eth，确认该eth口是否注册为nic口。
- 是，该物理网卡已经被主机所在的网络分组注册，执行9.b。
- 否，该物理网卡还没有被主机所在的网络分组注册，执行11。
- 执行以下命令从网络分组中解除注册，：
cps hostcfg-item-delete --item nic --nic-name nic_name --type network group_name
cps commit
其中，group_name可通过cps hostcfg-list --type network命令查看分组名称，nic名称可通过“cat /usr/bin/ports_info |python -mjson.tool|grep eth”查看。
例如：
cps hostcfg-item-delete --item nic --nic-name nic3 --type network group1
cps commit
处理完毕，执行11。
10. 替换bond从属网口中为unknown的网口，有如下两种处理方式：
- 主机上未运行业务时，登录FusionSphere OpenStack安装部署界面，选择“配置 > 网络 > 网口映射配置”页面，根据告警定位信息中的“主机ID”，找到告警主机所在的主机组。删除聚合网口bond，重新创建同名聚合网口bond，新bond中需使用其他正常网口替换异常网口。
- 主机上存在业务时，切换到PuTTY执行以下命令，更新bond的从属网口：
cps hostcfg-item-update --item bond --bond-name trunk_name --slaves nic1,nic2,... --type network group_name
cps commit
其中，trunk_name从告警信息中获取，nic_name可通过“cat /usr/bin/ports_info |python -mjson.tool|grep eth”查看，group_name可通过cps hostcfg-list --type network命令查看分组名称。
例如：
cps hostcfg-item-update --item bond --bond-name trunk1 --slaves nic3,nic4 --type network group1
cps commit
执行成功后，执行15。
11. 配置交换机使规划的物理网卡与PXE网口处于同交叉平面内（二层互通）。
执行成功后，执行15。
12. 查看告警中附加信息。
- 如果提示status is down，执行13。
- 如果提示aggregator id mismatch，执行14。
13. 由于告警上报有查重功能，附加信息中状态异常网卡和正常网卡，参考ALM-6021 主机网口状态异常处理步骤一并排查修复。
执行成功后，执行15。
14. 登录聚合网口对端主机或者交换机，检查trunk设置是否正确。
- 如果配置错误，请修改配置正确后，执行15。
- 如果配置正确，请执行16。
15. 等待5分钟时间，查看告警是否清除。
- 是，处理完毕。
- 否，执行16。
16. 请联系技术支持工程师协助解决。
##### 参考信息
无。