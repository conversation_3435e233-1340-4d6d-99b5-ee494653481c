# 5.2.3.1.63 ALM-73017 主机系统用户密码过期预警

##### 告警解释
系统每日凌晨1点检测fsp用户密码过期状态，当fsp用户密码剩余的有效天数在到期预警天数内时，则系统会上报本告警。
以默认配置为例，fsp用户密码默认有效期为90天，默认到期预警天数为15天，如距离上一次修改密码的天数超过75天，则剩余有效天数小于15天，该用户密码在过期预警时间内，系统上报本告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73017 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>用户名：fsp。 |
| 附加信息 | 详细信息：The host OS password will expire。<br>密码过期时间：所有主机密码中最近的过期时间。 |
##### 对系统的影响
fsp用户密码即将过期，如密码过期，使用该用户登录主机后台需要修改密码，否则无法登录。
##### 可能原因
fsp用户密码即将过期。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 进入“配置 > 系统”界面，单击“主机密码管理”。
3. 查看“密码状态”，在弹出的界面中记录标记为红色的主机，然后单击“确认”关闭该界面。
4. 确认告警解决策略：
- 修改密码有效期天数，执行5。
- 修改fsp密码，执行6。
5. 单击“密码周期”后的“修改”，修改“密码有效期”，然后单击“提交”完成修改后，执行7。
6. 单击“主机密码”后的“修改”，选择3记录的密码过期预警的主机，修改其对应的“fsp”用户名对应的密码，单击“提交”完成修改后，执行7。
7. 修改全部生效后，查看“密码状态”可自动清除告警。如无法修复，请联系技术支持工程师协助解决。
##### 参考信息
无。