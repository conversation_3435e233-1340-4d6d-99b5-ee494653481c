# 5.2.11.1.25 0x6000840002 证书即将过期

##### 告警解释
证书（区域：[Region]，部件：[ServiceName]，证书名：[CertName]）即将在[Date]过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840002 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |
| Date | 证书过期日期。 |
##### 对系统的影响
无。
##### 可能原因
证书即将过期。
##### 处理步骤
- 可能原因1：证书即将过期。
- 如果告警中的证书名是“eBackup-Portal”，请参考中的“证书管理 > 更换A类证书 > 更换灾备服务eBackup证书（eBackup-Portal）”更换灾备服务eBackup证书（eBackup-Portal） 章节替换证书。
- 如果告警中的证书名是“eBackup-Cert”或“eBackup-IAMCert”，请参考通过ManageOne界面方式单个或批量更换证书章节替换证书。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无