# 5.2.3.1.15 ALM-6026 主机光纤通道中断

##### 告警解释
OpenStack周期(默认300秒)检测本机的光纤通道的状态，如果光纤通道不通，则产生告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6026 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>异常详情：故障信息。 |
##### 对系统的影响
光纤通道出现故障时，会对存储业务的可靠性或功能产生影响。
##### 可能原因
光纤松动或光纤网口故障。
##### 处理步骤
1. 从告警信息中获取故障主机的ID信息。
2. 根据实验室主机对照表查询故障主机所在的机房。
3. 到机房查看故障主机的光纤是否松动或损坏：
- 是，插入或更换光纤，执行4。
插入或更换光纤时，请保证操作前后光纤口的对应位置和数量保持一致；如果光纤口位置发生交换或减少了光纤口数量，执行4之前，需要重启上报告警的主机，否则告警无法自动清除。
- 否，执行5。
4. 等待1分钟~2分钟，看告警是否恢复。
- 是，需要将处于出现告警的节点上的挂载有云磁盘的虚拟机或者从云磁盘启动的虚拟机硬重启虚拟机。进入Service OM界面，选择“资源 > 计算资源 > 虚拟机”，虚拟机所在行，选择“更多 > 强制重启”。重启成功，处理完毕。
- 否，执行5。
5. 联系运维人员，排查是否网络问题。
- 是，联系运维人员解决网络问题，执行6。
- 否，执行7。
6. 等待1分钟~2分钟，看告警是否恢复。
- 是，需要将处于出现告警的节点上的挂载有云磁盘的虚拟机或者从云磁盘启动的虚拟机硬重启虚拟机。进入Service OM界面，选择“资源 > 计算资源 > 虚拟机”，虚拟机所在行，选择“更多 > 强制重启”。重启成功，处理完毕。
- 否，执行7。
7. 请联系技术支持工程师协助解决。
##### 参考信息
无。