# 5.2.4.8.2 ALM-999999993 License已经过期

##### 告警解释
当License文件已经超过截止日期时，产生该告警。当License文件在有效期或宽限期内时，该告警会自动清除。
- 发生此告警之前都会发生一条ALM-999999992 License即将过期。上报此告警后，ALM-999999992 License即将过期的“清除状态”自动更新为“已清除”。
- 截止日期为有效期加宽限期。
- 该告警适用于License 2.00和License 3.00所有文件类型的License文件。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999993 | 紧急 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| LSN | License序列号。 |
##### 对系统的影响
License已过期，用户不能成功登录系统，需要更新License才能登录系统。
##### 可能原因
当前License文件已经超过截止日期。
##### 处理步骤
1. 更新License文件。
- 在拦截页面单击“更新License”。
- 在“更新License”页面中，单击“License文件”后面的。
- 选择已申请的License文件，单击“打开”。
- 单击“上传”，查看License更新前后的结果对比。
- 单击“应用”，立即应用新的License文件。
2. 查看本告警是否清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。