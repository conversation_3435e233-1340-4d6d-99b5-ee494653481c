# 5.2.4.18.2 ALM-47 服务内存占用过高告警

5.2.4.18.2.7 ALM-47 服务内存占用过高告警
##### 告警解释
系统每隔15秒检测一次服务占用内存，当系统连续40次检测到服务占用内存大于等于用户设定的告警门限值时，产生该告警；只要有一次服务占用内存小于用户设定的告警门限值时，该告警将会被自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 47 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 服务名 | 产生告警的服务进程名称。 |
| 站点名称 | 产生告警的站点名称。 |
| 门限值 | 告警产生门限和清除门限。 |
| 占用值 | 服务使用内存的数值。 |
##### 对系统的影响
业务面服务器的响应速度变慢。
##### 可能原因
程序处理异常。
##### 处理步骤
1. 登录部署面。
- 打开浏览器，在地址栏中输入“https://部署面的客户端登录IP地址:31945”，按“Enter”。
如果管理面的部署模式是集群模式，请使用管理节点浮动IP地址进行登录。
- 在登录界面输入用户名admin和密码，单击“登录”。
2. 在部署面主菜单中选择“产品 > 系统监控”。
3. 在“系统监控”页面左上方，光标移至并选择告警参数中“主机”对应节点所属的产品。
4. 在“节点”页签，单击产生告警的节点名称。
5. 在“进程”页签中查找告警产生的进程名称，选中产生告警的进程名称，先单击“停止”，待状态转为“未运行”后再单击“启动”。
从告警定位信息中查找服务名来确定进程名称。
6. 待进程启动成功后，等待5分钟，检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
7. 产生该告警的节点名称发生了变化。
8. 产生该告警的站点名称发生了变化。
9. 产生该告警的服务器不被监控了。
##### 参考信息
无。