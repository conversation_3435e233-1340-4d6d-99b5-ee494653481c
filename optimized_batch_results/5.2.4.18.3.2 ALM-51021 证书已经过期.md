# ********.3 ALM-51021 证书已经过期

********.3.2 ALM-51021 证书已经过期
##### 告警解释
系统每天对部署面和业务面的ER证书的有效期进行一次检查，若证书的有效期过期或者证书无效时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51021 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点IP地址。 |
| 证书类型 | ER证书。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
如果ER证书失效，将无法登录部署面或业务面界面。
##### 可能原因
证书已超过有效期。
##### 处理步骤
根据已经过期的证书类型，处理证书过期问题。
1. 如果部署面或运营面的ER证书已经过期，则登录运维面更换部署面或运营面的ER证书。具体操作请参见《华为云Stack 6.5.1 安全管理指南》手册中“证书管理”对应证书类型的更新步骤。
2. 如果运维面的ER证书已经过期，此时运维面无法登录，则需要更新运维面的ER证书后，再执行其他证书的更新操作。具体操作请参见《华为云Stack 6.5.1 故障处理》中的“ER证书过期导致运维、运营面无法登录”。
##### 告警清除
系统在更新证书后的00:00:00检查证书的有效性，证书有效则系统会自动清除此告警，无需手工清除。
##### 参考信息
无。