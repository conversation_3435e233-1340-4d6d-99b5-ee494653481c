# ******** ALM-1200054 公网IP地址不足

##### 告警解释
弹性IP审计模块每天2:00校验IP地址池可用IP剩余量是否低于告警值（告警值默认为IP地址池内IP总数的10%），如果低于告警值，产生此审计告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200054 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 来源系统 | 告警来源。 |
| IP地址 | IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 网络ID | 产生告警信息的外部网络ID。 |
| 定位信息 | 子网ID | 产生告警信息的子网ID。 |
| 附加信息 | 附加信息 | 告警附加信息。 |
##### 对系统的影响
EIP地址不足，导致无法申请EIP资源。
##### 可能原因
弹性IP使用率较大，造成资源不足。
##### 处理步骤
新增弹性IP子网网段
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的导航栏，选择“运维地图”。
3. 在“运维地图”页面右边的“快速访问”导航栏中，单击“ServiceOM”进入Service OM界面。
4. 选择“资源 > 网络资源”。
5. 选择“外部网络”页签。
6. 在外部网络列表中找到对应的EIP外部网络，在其所在行操作列，选择“更多 > 创建IPv4子网”。
7. 根据环境的网络规划分配子网网段，创建弹性IP外部网络的子网。
8. Region Type I场景下，还需切换至被级联层创建弹性IP外部网络的子网。
手动屏蔽该EIP外部网络子网网段的告警
9. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
10. 单击“登录”。
11. 在页面上方的菜单栏，选择“集中告警”。
12. 在告警列表中，找到待处理的告警记录，单击“”，选择“设置屏蔽规则”。
13. 在“设置屏蔽规则”窗口中设置如下屏蔽规则，屏蔽该EIP外部网络子网网段的告警。
- 屏蔽：“云服务”告警分组名称上的“公网IP地址不足”告警
- 条件：定位信息 包含 对应告警的网络ID；子网ID；服务名称
- 屏蔽后的告警：显示在“被屏蔽告警”中
当出现多条相同告警时，告警信息被汇聚。此时在“被汇聚告警”页签查看告警定位信息。
14. 单击“确定”，完成屏蔽规则设置。
##### 告警清除
进入告警监控页面，在告警列表中选中该条告警，单击“清除”。
##### 参考信息
无。