# 5.2.3.2.3 ALM-9203 Service OM服务器时间被修改

##### 告警解释
时间管理模块监测到Service OM节点时间被修改，同时修改的时间超出了Service OM节点允许的时间调整阈值（默认60秒），系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9203 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 阈值：固定为“1 minute” |
##### 对系统的影响
此告警产生时，系统的功能会产生异常，如Service OM使用定时器的业务等。同时如果Service OM配置了上级时间服务器可能会产生ALM-9204 Service OM与上级时间服务器时间差异过大的告警。
##### 可能原因
人为修改了Service OM节点的时间。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“系统 > 系统管理 > 时间”。
3. 在“时间”界面，单击“强制时间同步”。
强制时间同步过程中，会重启系统服务，耗时约20分钟。
4. 等待大约20分钟后，重新登录Service OM界面，进入“时间”页面，查看强制时间同步是否成功。
- 是，执行5。
- 否，执行7。
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 按照系统时间错误处理系统时间错误。
7. 请联系技术支持工程师协助解决。
##### 参考信息
无