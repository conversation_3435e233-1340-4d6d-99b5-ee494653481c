# ********.5 ALM-48316-请求超出单实例流控阈值

##### 告警解释
业务请求量超过单实例的处理能力，系统启动保护机制。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48316 | 重要 | 处理错误告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Node_Rate_Limit | 流控阀值 |
##### 对系统的影响
部分业务调用不能正常执行。
##### 可能原因
- 业务请求量增加，达到流控阈值。
- 外部攻击导致访问量增大，达到流控阈值。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息：Node表示告警源节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录故障节点Node。
默认帐号：paas，默认密码：*****。
3. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 执行以下命令，收集业务运行日志并联系技术支持。
cd /var/log/apigateway/shubao
zip -r shubao_log.zip run
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。