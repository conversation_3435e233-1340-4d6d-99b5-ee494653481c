# 5.2.17.3.1 ALM-1510000 GaussdbHA服务进程异常

##### 告警解释
数据库正常状态为Primary或者Standby，如果出现其他状态则表示数据库异常上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1510000 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
数据库异常可能导致对应的服务无法访问数据库。
##### 可能原因
网络、磁盘等异常，或者软件缺陷导致的数据库异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：gaussdb，默认密码：*****。
6. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行如下命令确认数据库状态是否正常。
service gaussdb query|grep DB_STATE
回显信息如下，表示数据库状态正常。
[root@PUB-DB01 ~]# service gaussdb query|grep DB_STATE
DB_STATE                     : Normal
- 是，请联系技术支持工程师协助解决。
- 否，执行9。
9. 执行如下命令停止本节点数据库系统。
source /etc/profile
haStopAll -a
10. 执行如下命令启动本节点数据库系统。
haStartAll -a
11. 执行如下命令确认数据库状态是否正常。
service gaussdb query|grep DB_STATE
- 回显信息如下，表示数据库状态正常，执行16。
- [root@PUB-DB01 ~]# service gaussdb query|grep DB_STATE
DB_STATE                     : Normal
- 回显信息如下，表示数据库待修复，执行12。
- [root@PUB-DB01 ~]# service gaussdb query|grep DB_STATE
DB_STATE                     : NeedRepair
12. 执行如下命令，查看日志文件。
cd /var/log/postgresql/
vim gaussdb-年-月-日_XXXXXX.log
示例如下:
vim gaussdb-2019-05-31_000000.log
如果日志中包含以下语句，执行13。
FATAL:  failedto identify consistence at 9/986B94A0: SSL connection has been closedunexpectedly
13. 执行如下命令，与主节点进行同步。
su dbadmin
gs_ctl build
14. 执行如下命令，切回root用户。
exit
15. 执行11，再次查询数据库状态是否正常。
- 是，执行16。
- 否，请联系技术支持工程师协助解决。
16. 等待10分钟，查看告警是否自动清除。
- 是，告警自动清除。
- 否，请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。