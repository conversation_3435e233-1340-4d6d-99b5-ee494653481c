# 5.2.3.1.14 ALM-6025 存储使用率超过阈值

##### 告警解释
OpenStack周期（默认为5分钟）检测存储使用占用率，当检测到存储使用占用率大于等于系统预设置的告警阈值时，系统产生此告警。
- 系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
- 紧急：存储占用率≥95%
- 重要：90%≤存储占用率<95%
- 次要：85%≤存储占用率<90%
- 提示：80%≤存储占用率<85%
- 本告警仅针对FusionSphere OpenStack本身所使用的存储，只支持华为磁阵、FusionStorage，FusionCompute或vCenter中的存储是否上报阈值告警请关注各自管理软件。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6025 | 紧急/重要/次要/提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>阈值：存储使用率阈值<br>已使用容量：产生告警的后端存储的容量使用率<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
##### 对系统的影响
存储的使用率过高会影响存储的性能，当出现存储不足时，系统将无存储资源可用。
##### 可能原因
存储资源使用过多，资源不足。
##### 处理步骤
1. 查看是否存在ALM-70300 卷审计告警，且告警内容为野卷。
- 是，参考ALM-70300 卷审计告警处理完成后，查看此告警是否自动清除。
- 否，执行2。
2. 通过告警定位信息，获取后端存储名称，对相应的存储池扩容。后端存储如果是SAN设备，扩容操作请参考SAN存储设备产品手册，可根据存储型号，通过https:/support.huawei.com/enterprise/查找获取对应型号存储设备的产品手册，如果是FusionStorage，扩容操作请参考《FusionStorage V100R006C30SPC500 块存储服务容量调整指南》。
3. 当存储的使用率低于80%时，告警是否恢复。
- 是，处理完成。
- 否，执行4。
4. 请联系技术支持工程师协助解决。
##### 参考信息
无。