# 5.2.3.2.5 ALM-9206 Service OM数据备份失败

##### 告警解释
备份恢复模块在每天2:00进行本地自动备份，每隔30分钟，把备份文件上传到第三方服务器。
当本地自动备份失败、备份操作失败、上传备份文件到第三方服务器失败时，系统产生此告警。在告警的附加信息中会说明产生告警的错误码和错误信息。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9206 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | FTP地址：FTP的地址，IP或者域名。<br>错误码：FTP备份或者上传文件失败的错误码<br>错误信息：错误码对应的错误描述信息 |
##### 对系统的影响
- 当出现一次本地自动备份失败，系统会缺少一天的备份数据。
- 当出现一次备份操作失败，系统会缺少本次的备份数据。
- 当出现一次上传备份文件到第三方服务器失败，第三方服务器会缺少一份备份数据。
##### 可能原因
本地自动备份失败、备份操作失败或上传备份文件到第三方服务器失败。
##### 处理步骤
1. 确认告警产生的原因。
请参见下表。
| 序号 | 错误码 | 错误描述 | 处理方案 |
| --- | --- | --- | --- |
| 1 | 10409101 | 本地自动备份失败，原因为内部错误。 | 执行处理故障8 |
| 2 | 10409106 | 本地自动备份失败，原因为数据库状态异常。 | 执行处理故障1 |
| 3 | 10409108 | 本地自动备份失败，原因为本地主机备份空间不足。 | 执行处理故障2 |
| 4 | 10409109 | 本地自动备份失败，原因为本地主机备份文件序号超过上限。 | 执行处理故障3 |
| 5 | 10409301 | 上传备份文件到第三方服务器失败，内部错误。 | 执行处理故障8 |
| 6 | 10409309 | 上传备份文件到第三方服务器失败，原因为本地主机备份文件序号超过上限。 | 执行处理故障4 |
| 8 | 10409311 | 上传备份文件到第三方服务器失败，原因为无法连接第三方服务器。 | 执行处理故障6 |
| 9 | 10409316 | 上传备份文件到第三方服务器失败，原因为本地主机的备份文件数超过上限。 | 执行处理故障7 |
| 10 | 10409327 | 备份操作超时。 | 执行处理故障8 |
处理故障1
2. 使用“PuTTY”，登录Service OM节点主机。
用“galaxmanager”用户，以Service OM节点主机的管理浮动IP登录。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
5. 执行以下命令，检查数据库的状态。
galaxmanager status|grep gaussDB
6. 数据库状态是否正常。
- 显示status:abnormal，数据库状态异常，执行7。
- 显示status:normal，数据库状态正常，执行9。
7. 执行以下命令，停止数据库。
gaussDB stop
8. 执行命令成功后，两分钟后执行5，检查数据库状态。
9. 登录告警页面，手工清除告警。
- 告警清除成功，任务结束。
- 告警清除失败，执行54。
处理故障2
10. 使用“PuTTY”，登录Service OM节点主机。
用“galaxmanager”用户，以Service OM节点主机的管理IP登录。
11. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
12. 执行以下命令，检查备份目录所在分区的空间。
df -m
Filesystem                     Size  Used Avail Use% Mounted on
/dev/vda1                      6.0G  1.3G  4.7G  22% /
devtmpfs                       4.8G     0  4.8G   0% /dev
tmpfs                          4.8G     0  4.8G   0% /dev/shm
tmpfs                          4.8G  505M  4.4G  11% /run
tmpfs                          4.8G     0  4.8G   0% /sys/fs/cgroup
/dev/mapper/vg_om-lv_home      5.0G   33M  5.0G   1% /home
/dev/mapper/vg_om-lv_sysback  1014M   33M  982M   4% /sysback
/dev/mapper/vg_om-lv_var       4.0G   98M  3.9G   3% /var
/dev/mapper/vg_om-lv_tmp       3.0G   33M  3.0G   2% /tmp
/dev/mapper/vg_om-lv_log       6.0G   87M  6.0G   2% /var/log
/dev/mapper/vg_om-lv_goku      8.0G   81M  8.0G   1% /var/log/goku
/dev/mapper/vg_om-lv_opt        10G  2.0G  8.0G  20% /opt
/dev/mapper/vg_om-lv_gmbackup  6.0G   55M  6.0G   1% /opt/gmbackup
/dev/mapper/vg_om-lv_data       21G   43M   21G   1% /opt/goku/data
/dev/mapper/vg_om-lv_db        6.0G  418M  5.6G   7% /opt/goku/data/db
tmpfs                          983M     0  983M   0% /run/user/0
tmpfs                          983M     0  983M   0% /run/user/2000
显示每个分区的空间使用情况，查看第6列（Mounted on），目录/opt对应的第4列（Avail），可用磁盘空间是否小于900。这里的900为参考值，实际备份任务对磁盘空间的要求还和备份的数据量大小相关，如果备份数据量较大时，建议为备份任务保留更大的磁盘空间。
- 小于900，执行13。
- 大于等于900，执行9。
13. 执行以下命令，进入目录“/opt/gmbackup/db”。
cd /opt/gmbackup/db
14. 执行以下命令，查看目录“/opt/gmbackup/db”下的文件和目录。
ls
15. 检查目录“/opt/gmbackup/db”下是否有不合法的文件和目录。
- 有，执行16。
- 没有，执行18。
目录和文件的合法性请参见本文末尾的参考信息。
16. 执行以下命令，逐个删除目录“/opt/gmbackup/db”下不合法的文件和目录。
rm -r 不合法的文件或目录的名字
17. 执行12。
18. 执行以下命令，进入目录“/opt/gmbackup/db/manualbk”。
cd /opt/gmbackup/db/manualbk
19. 执行以下命令，查看目录“/opt/gmbackup/db/manualbk”下的文件和目录。
ls
20. 检查目录“/opt/gmbackup/db/manualbk”下是否有不合法的文件和目录。
- 有，执行21。
- 没有，执行23。
21. 执行以下命令，逐个删除目录“/opt/gmbackup/db/manualbk”下不合法的文件和目录。
rm -r 不合法的文件或目录的名字
22. 执行12。
23. 执行以下命令，进入目录“/opt/gmbackup/db”。
cd /opt/gmbackup/db
24. 执行以下命令，查看配置的自动备份文件个数。
modConfig backup -l | grep "MAX_AUTO_BACKUP_NUM"
如果用户不进行配置，自动备份文件个数默认为7。
25. 执行以下命令，查看目录“/opt/gmbackup/db”下的备份文件。
ls
备份文件名的格式请参见本文末尾的参考信息。
26. 检查目录“/opt/gmbackup/db”下的备份文件个数是否大于所配置的自动备份文件个数。
- 大于，执行27。
- 不大于，执行29。
27. 执行以下命令，删除序号最小的备份文件。
rm -r 序号最小的备份文件的名字
备份文件的序号查看方法请参见本文末尾的参考信息。
28. 执行12。
29. 执行以下命令，进入目录“/opt/gmbackup/db/manualbk”。
cd /opt/gmbackup/db/manualbk
30. 使用ls命令查看目录“/opt/gmbackup/db/manualbk”下是否存在备份文件。
- 存在，执行31。
- 不存在，执行54。
31. 执行以下命令，删除序号最小的备份文件。
rm -r 序号最小的备份文件的名字
32. 执行12。
处理故障3
33. 使用“winscp”，以“galaxmanager”用户登录Service OM节点主机。
34. 进入目录“/opt/gmbackup/db”，查看是否存在序号大于4294967295的备份文件。
- 存在，删除序号大于4294967295的备份文件后，执行9。
- 不存在，执行9。
备份文件的序号查看方法请参见本文末尾的参考信息。
处理故障4
35. 使用“winscp”，以“galaxmanager”用户登录Service OM节点主机。
36. 进入目录“/opt/gmbackup/db”，查看是否存在序号大于4294967295的备份文件。
- 存在，删除序号大于4294967295的备份文件后，执行37。
- 不存在，执行37。
备份文件的序号查看方法请参见本文末尾的参考信息。
37. 进入目录“/opt/gmbackup/db/manualbk”，查看是否存在序号大于4294967295的备份文件。
- 存在，删除序号大于4294967295的备份文件后，执行9。
- 不存在，执行9。
处理故障6
38. 准备第三方服务器的IP和第三方服务器ftps服务设置指导书。
39. 使用“PuTTY”，登录Service OM节点主机。
用“galaxmanager”用户，以Service OM节点主机的管理IP登录。
40. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
41. 执行以下命令，查看Service OM节点主机和第三方服务器之间的网络连接是否正常。
ping第三方服务器IP，如ping *************
返回信息如下所示，则通信正常。
ping ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 ttl=64 time=0.035 ms
64 bytes from *************: icmp_seq=2 ttl=64 time=0.028 ms
64 bytes from *************: icmp_seq=3 ttl=64 time=0.025 ms
42. 通信是否正常。
- 是，执行43。
- 否，执行54。
43. 检查第三方服务器ftps服务是否正常。
- 正常，执行9。
- 不正常，请参考第三方服务器ftps服务设置指导书，进行恢复。
请参考第三方服务器ftps服务设置指导书，检查第三方服务器ftps服务是否正常。
44. 执行9。
处理故障7
45. 使用“PuTTY”，登录Service OM节点主机。
用“galaxmanager”用户，以Service OM节点服务器的管理浮动IP登录。
46. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
47. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
48. 执行以下命令，进入目录“/opt/gmbackup/db”。
cd /opt/gmbackup/db
49. 执行以下命令，查看配置的自动备份文件个数。
modConfig backup -l | grep "MAX_AUTO_BACKUP_NUM"
如果用户不进行配置，自动备份文件个数默认为7。
50. 执行以下命令，查看目录“/opt/gmbackup/db”下的备份文件。
ls
备份文件名的格式请参见本文末尾的参考信息。
51. 检查目录“/opt/gmbackup/db”下的备份文件个数是否大于所配置的自动备份文件个数。
- 是，执行52。
- 否，执行9。
52. 执行以下命令，删除序号较小的备份文件，直至备份文件个数等于所配置的自动备份文件个数。
rm -r 序号较小的备份文件的名字
备份文件的序号查看方法请参见本文末尾的参考信息。
53. 执行9。
处理故障8
54. 请联系技术支持工程师协助解决。
##### 参考信息
55. 备份文件名的格式为：gmdb-年-月-日-序号.dump，其中年为4位数，月和日都为两位数，序号为不大于4294967295的正整数。例如备份文件为gmdb-2012-07-12-1.dump，该备份文件的生成日期为2012-07-12，序号为1。
56. 本地自动备份目录/opt/gmbackup/db下合法的目录为：manualbk
57. 本地自动备份目录/opt/gmbackup/db下合法的文件为：
- gmdb-年-月-日-序号.dump
- lftp.out
58. 本地手动备份目录/opt/gmbackup/db/manualbk下合法的文件为：
- gmdb-年-月-日-序号.dump
- lftp.out