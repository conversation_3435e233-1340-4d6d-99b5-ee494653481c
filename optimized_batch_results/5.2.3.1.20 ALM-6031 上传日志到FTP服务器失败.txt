# 5.2.3.1.20 ALM-6031 上传日志到FTP服务器失败

##### 告警解释
OpenStack周期上传日志到FTP服务器，当上传失败时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6031 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |
##### 对系统的影响
日志无法上传至FTP服务器，如果未设置本地备份，则会导致该次日志丢失。
##### 可能原因
- 主机与FTP服务器网络连接异常。
- Apacheproxy服务状态异常。
- 日志配置文件中FTP服务器IP、端口未正确设置。
- 没有权限上传FTP服务器。
- FTP服务器上指定的日志上传空间已满。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 导入环境变量，具体操作请参见导入环境变量，使用CPS鉴权方式。
Apacheproxy服务状态异常
5. 执行如下操作查看apacheproxy服务是否正常。
cps template-instance-list --service apacheproxy apacheproxy
回显如下类似信息：
+------------+---------------+---------+--------------------------------------+---------------+
| instanceid | componenttype | status  | runsonhost                           | omip          |
+------------+---------------+---------+--------------------------------------+---------------+
| 0          | apacheproxy   | active  | 304BA4EF-98F8-8F94-E811-852B82CB59EF | ************* |
| 1          | apacheproxy   | standby | 6A7554E9-410C-61A7-E811-6D0CD8E2B1D4 | ************* |
+------------+---------------+---------+--------------------------------------+---------------+
- 如有服务状态显示为fault，执行6。
- 如服务状态显示为active和standby，执行9。
6. 执行如下命令停止apacheproxy服务。
cps host-template-instance-operate --service apacheproxy apacheproxy --action stop
回显如下类似信息：
+-------------+--------------------------------------+---------+
| template    | runsonhost                           | result  |
+-------------+--------------------------------------+---------+
| apacheproxy | D4B110B6-919D-0000-1000-1DD2000016E0 | success |
| apacheproxy | CCCC8175-8F6C-0000-1000-1DD200002250 | success |
+-------------+--------------------------------------+---------+
查看操作结果是否为success。
- 是，执行7。
- 否，执行13。
7. 执行如下命令启动apacheproxy服务。
cps host-template-instance-operate --service apacheproxy apacheproxy --action start
在apacheproxy主备部署的情况下，回显如下类似信息：
+-------------+--------------------------------------+---------+
| template    | runsonhost                           | result  |
+-------------+--------------------------------------+---------+
| apacheproxy | D4B110B6-919D-0000-1000-1DD2000016E0 | success |
| apacheproxy | CCCC8175-8F6C-0000-1000-1DD200002250 | success |
+-------------+--------------------------------------+---------+
查看操作结果是否为success。
- 是，执行8。
- 否，执行13。
8. 执行如下命令查看apacheproxy服务是否正常。
cps template-instance-list --service apacheproxy apacheproxy
回显如下类似信息：
+------------+---------------+---------+--------------------------------------+---------------+
| instanceid | componenttype | status  | runsonhost                           | omip          |
+------------+---------------+---------+--------------------------------------+---------------+
| 0          | apacheproxy   | active  | 304BA4EF-98F8-8F94-E811-852B82CB59EF | ************* |
| 1          | apacheproxy   | standby | 6A7554E9-410C-61A7-E811-6D0CD8E2B1D4 | ************* |
+------------+---------------+---------+--------------------------------------+---------------+
- 如显示没有服务fault，执行9。
- 如仍有服务显示为fault，执行13。
日志配置文件中FTP服务器IP、端口未正确设置
9. 执行如下命令查看FTP服务器配置信息。
log ftp-policy-get
查看是否回显如下类似信息：
+-------------------------+------------------------------------------------+
| Property                | Value                                          |
+-------------------------+------------------------------------------------+
| ftp_enable              | True                                           |
| ftp_ip                  | ************                                   |
| ftp_password            | 1#Sr90Ce3s+nH2yG3D4KiXCD4+SfwiYz6WVFfGSi14/v0= |
| ftp_path                | root                                           |
| ftp_port                | 2121                                           |
| ftp_schema              | FTPS                                           |
| ftp_username            | admin                                          |
| policy_ftp_export_begin | 4                                              |
| policy_ftp_export_end   | 5                                              |
+-------------------------+------------------------------------------------+
- 是，核对回显信息中的FTP服务器信息是否与实际使用的一致：
- 是，执行11。
- 否，执行10。
- 否，执行13。
10. 执行如下命令重新设置FTP服务器地址(ftp_ip)，用户名(ftp_username)，端口(ftp_port)等信息。
log ftp-policy-set --parameter ftp_ip=IP ftp_username=USERNAME ftp_port=PORT
查看是否回显如下类似信息：
+--------------+--------------+
| Property     | Value        |
+--------------+--------------+
| ftp_ip       | ************ |
| ftp_port     | 2121         |
| ftp_username | admin        |
+--------------+--------------+
- 是，执行11。
- 否，执行13。
没有权限上传FTP服务器/FTP服务器上指定的日志上传空间已满
11. 执行如下操作查看FTP服务器是否可用。
- 查看对应的端口是否在FTP服务器上开启。
- 检查使用的用户是否有足够的权限。
- 检查FTP服务器对应路径下的空间是否足够。
12. 等待下次自动触发上传FTP，查看告警是否恢复。自动触发周期大约24小时。
- 是，处理完毕。
- 否，执行13。
13. 请联系技术支持工程师协助解决。
##### 参考信息
无。