# 5.2.17.12.1 ALM-2000266 节点系统时钟跳变超过一分钟

告警解释
云平台仲裁服务使用的Etcd组件强依赖于时间，当节点系统时钟跳变超过一分钟时，本节点业务不可用，系统产生此告警。
此告警提示系统故障或风险，需要处理。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000266 | 紧急 | 否 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
产生告警的节点业务不可用。
可能原因
手动修改了节点的系统时间。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：arbiter，默认密码：*****。
6. 执行如下命令，获取NTP服务器信息。
ntpq -p
回显信息中，“remote”列即为NTP服务器IP。
7. 使用“PuTTY”，通过6中获取的NTP节点管理IP地址登录NTP节点。
默认帐号：untp，默认密码：*****。
8. 执行以下命令，确认本节点与上级时钟源的NTP连通性。
ntpdate -q 时钟源地址
- 显示如下信息，确认本节点与上级时钟源的NTP连通。
- [root@TDNS-TNTP02 fsp]# ntpdate -q **********
- server **********, stratum 11, offset -0.000138, delay 0.02583
25 Jan 10:34:02 ntpdate[4956]: adjust time server ********** offset -0.000138 sec
- 否则，表示无法与上级时钟源的NTP节点连通，请检查网络配置及防火墙后重试。
9. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：arbiter，默认密码：*****。
10. 执行如下命令，重启NTP进程。
service ntpd restart
11. 执行如下命令，同步节点系统时间。
ntpdate NTP服务器IP
12. 执行如下命令，写入硬件时钟。
hwclock -w
13. 执行如下命令，重启故障节点。
reboot
14. 节点启动后，等待3分钟，查看告警是否自动清除。
- 是，处理完成。
- 否，执行15。
15. 请联系技术支持工程师协助解决。
参考信息
无。