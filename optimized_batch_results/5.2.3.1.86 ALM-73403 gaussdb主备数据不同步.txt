# 5.2.3.1.86 ALM-73403 gaussdb主备数据不同步

##### 告警解释
gaussdb主备机间会周期性（1min）检查数据同步状态，如果连续3min数据不能正常同步，产生此告警。同步状态正常，告警消除。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73403 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。 |
| 附加信息 | 详细信息：sync_abnormal<br>本端地址：本端同步IP地址<br>对端地址：对端同步IP地址 |
##### 对系统的影响
此告警产生，gaussdb数据不能进行同步，进而造成数据丢失。
##### 可能原因
- 数据库所在主机下电。
- 使用非正常数据库的启停命令操作数据库。
- 网络故障。
- 备节点gaussdb分区占满。
##### 处理步骤
1. 通过告警的定位信息，获取产生告警的组件名，定位信息中的服务即为组件名。
2. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
6. 输入“2”，选择使用CPS鉴权，按照提示输入“CPS_USERNAME”的用户名和密码。
用户帐户：cps_admin，默认密码：*****。
检查数据库所在主机是否被下电及网络是否正常
7. 执行以下命令，获取产生告警的服务名。
cps template-list | grep 组件名
5F0B42C1-EFD0-C685-E811-16AD2A2D1793:/home/<USER>
| gaussdb_keystone    | gaussdb_keystone               | DataBase for Keystone service.            |
| gaussdb_cinder      | gaussdb_cinder                 | DataBase for Cinder service.              |
| gaussdb_nova        | gaussdb_nova                   | DataBase for Nova service.                |
| gaussdb_neutron     | gaussdb_neutron                | DataBase for Neutron service.             |
| gaussdb             | gaussdb                        | DataBase for OpenStack service.           |
如上方回显为例，第一列为服务名，第二列为组件名，根据组件名获取对应的服务名。
8. 执行以下命令查看数据库所在主机。
cps template-instance-list --service 服务名 组件名
9. 执行以下命令，检查主机状态是否正常。
cps host-list | grep fault
回显信息中的ID是否包含8中回显的ID。
- 是，执行10排查主机故障原因。
- 否，执行11。
10. 检查主机是否被下电。
- 是，上电该主机。等待主机正常上电后，执行18。
- 否，执行11继续排查。
11. 执行以下命令检查网络状态是否正常。
- IPV4环境执行: ping manageip
- IPV6环境执行: ping6 manageip
manageip是否可以ping通。
- 是，执行16。
- 否，则说明故障节点网络不可达，需要先排查网络问题后，执行18。
manageip: 故障gaussdb所在节点的manageip。
检查备节点磁盘分区是否占满
12. 执行以下命令获取gaussdb备节点manageip。
cps template-instance-list --service 服务名 组件名 | grep standby
13. 使用PuTTY，登录gaussdb备节点manageip。
14. 执行以下命令，查看gaussdb分区是否占满。
df -h
A68692F3-DE60-11B4-E811-FC323C32F17C:~ # df -h
Filesystem                              Size  Used Avail Use% Mounted on
/dev/mapper/cpsVG-rootfs                7.8G  5.2G  2.2G  71% /
devtmpfs                                220G  4.0K  220G   1% /dev
tmpfs                                   220G   92K  220G   1% /dev/shm
tmpfs                                   220G   77M  220G   1% /run
tmpfs                                   220G     0  220G   0% /sys/fs/cgroup
/dev/mapper/cpsVG-fsp                   976M  4.1M  905M   1% /home/<USER>
/dev/mapper/cpsVG-data                  488M   15M  438M   4% /opt/fusionplatform/data
/dev/mapper/cpsVG-log                    20G  990M   18G   6% /var/log
/dev/sda2                               471M   72M  371M  17% /boot
/dev/sda1                               493M  152K  493M   1% /boot/efi
/dev/mapper/cpsVG-backup                 20G   45M   19G   1% /opt/backup
/dev/mapper/cpsVG-repo                   20G   45M   19G   1% /opt/fusionplatform/data/fusionsphere/repo
/dev/mapper/cpsVG-database               20G  1.4G   18G   8% /opt/fusionplatform/data/gaussdb_data
/dev/mapper/cpsVG-zookeeper             4.8G  121M  4.5G   3% /opt/fusionplatform/data/zookeeper
tmpfs                                    44G     0   44G   0% /run/user/1002
/dev/mapper/cpsVG-upgrade               3.4G   15M  3.2G   1% /opt/fusionplatform/data/upgrade
/dev/mapper/cpsVG-image                 276G  9.4G  255G   4% /opt/HUAWEI/image
/dev/mapper/cpsVG-image--cache           50G  8.9G   39G  19% /opt/HUAWEI/image_cache
/dev/mapper/cpsVG-rabbitmq              3.9G   19M  3.6G   1% /opt/fusionplatform/data/rabbitmq
tmpfs                                    44G     0   44G   0% /run/user/2001
tmpfs                                    44G     0   44G   0% /run/user/1008
tmpfs                                    44G     0   44G   0% /run/user/0
/dev/mapper/zk_vol_1                     59G  400M   56G   1% /opt/dsware/agent/zk/data
/dev/mapper/extend_vg-ceilometer--data  118G  1.8G  110G   2% /var/ceilometer
/dev/mapper/extend_vg-swift             500G   57G  444G  12% /opt/HUAWEI/swift
查询回显信息中/opt/fusionplatform/data/gaussdb_data的Use%的值，当为100%时，即为占满。
- 是，按照数据库状态异常处理中的“主机磁盘空间占满”部分处理。
- 否，执行16。
15. 等待1分钟~3分钟，查看告警台，确认告警恢复。
- 告警恢复，处理完毕。
- 告警不恢复，执行16。
检查是否使用非正常数据库的启停命令操作数据库
16. 执行以下命令将故障的数据库停止。
cps host-template-instance-operate --service 服务名 组件名 --action stop --host HOSTID
- 执行成功，执行17。
- 执行失败，间隔1分钟，重新执行；如果3次尝试都失败，执行19。
HOSTID: 故障gaussdb所在的节点ID。
17. 执行如下操作将故障的数据库启动。
cps host-template-instance-operate --service 服务名 组件名 --action start --host HOSTID
- 执行成功，执行18。
- 执行失败，间隔1分钟，重新执行；如果3次尝试都失败，执行19。
18. 等待1分钟~3分钟，查看告警台，确认告警恢复。
- 告警恢复，处理完毕。
- 告警不恢复，执行19。
19. 请联系技术支持工程师协助解决。
##### 参考信息
无。