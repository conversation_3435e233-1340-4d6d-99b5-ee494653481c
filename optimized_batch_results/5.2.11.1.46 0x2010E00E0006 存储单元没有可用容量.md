# 5.2.11.1.46 0x2010E00E0006 存储单元没有可用容量

##### 告警解释
存储单元（名称：[Brick_name]，路径：[Brick_path]）的可用容量已为空。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E00E0006 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Brick_name | 存储单元名称。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
不涉及。
##### 可能原因
存储单元的空间被占满。
##### 处理步骤
- 可能原因1：存储单元的空间被占满。
- 请参考《华为云Stack 6.5.1 扩容指南》中的“新增备份存储的容量”章节对存储单元进行扩容，或联系技术支持工程师进行数据离线迁移。
- 等待8分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无