# 5.2.3.1.58 ALM-73012 磁盘分区inode资源不足告警

##### 告警解释
系统按周期检测磁盘分区inode使用率，当磁盘分区inode使用率大于等于告警阈值（默认为90%）时，系统产生告警。当磁盘inode使用率小于告警阈值（默认为80%），告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73012 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>异常磁盘分区目录：异常磁盘名称。 |
| 附加信息 | 异常信息：告警的异常信息。<br>说明： <br>disk partition=%s：表示为磁盘分区挂载目录，如/var/log等。<br>inode usage=%d：表示为磁盘当前inode使用率。<br>alarm: %d%：表示告警阈值，值为90%。<br>resume: %d%：表示恢复阈值，值为80%。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
- 如果是根分区满可能导致系统运行异常。
- 如果是其它分区满会导致无法创建文件等。
##### 可能原因
磁盘inode利用率过高。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行cd ${PATH}命令进入告警详细信息中所报磁盘分区，如果能确定该分区下哪些文件可以删除，执行rm ${FILE_NAME}命令按需删除以释放inode。
- PATH：告警附加信息中disk partition的值。
- FILE_NAME：PATH目录下，获取的待删除文件的名称。
建议删除对应磁盘分区下的人为放置的大文件，或者未清理的日志文件以释放inode。
告警是否自动清除。
- 是，任务结束。
- 否，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。