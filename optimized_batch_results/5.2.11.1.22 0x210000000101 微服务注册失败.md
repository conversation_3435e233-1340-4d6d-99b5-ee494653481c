# ********.22 0x210000000101 微服务注册失败

##### 告警解释
微服务（名称：[Name]，IP：[IP_Address]，端口：[Port]）注册失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000101 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |
##### 对系统的影响
该微服务无法正常接收和处理请求，可能导致系统整体性能下降。
##### 可能原因
微服务注册失败。
##### 处理步骤
- 可能原因1：微服务注册失败。
- 使用PuTTY，通过告警上报的IP地址登录微服务注册失败所在服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行ps -ef | grep 微服务名称命令，检查微服务进程是否存在。
- 是，执行1.f。
- 否，执行1.d。
- 执行netstat -lp | grep 微服务端口号命令，检查该微服务相关端口是否处于“LISTEN”状态。
- 是，执行1.e。
- 否，执行1.f。
- 找到“LISTEN”状态后面的进程ID，执行kill -9 进程ID命令，释放该端口。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/script命令，进入微服务脚本目录，其中，“ebk_xxx”是微服务名称。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止该微服务。等待1分钟左右，微服务会自动启动。
- 是，执行1.i。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，执行1.j。
- 执行service hcp status命令，检查ebk_governance和ebk_iam微服务是否在运行。
- 是，执行1.k。
- 否，参考1.f~1.h重新启动未运行的ebk_governance或ebk_iam，之后转到1.i。
- 执行ps -ef | grep ebk_lb命令，检查ebk_lb的nginx进程是否正常运行。
- 是，执行1.l。
- 否，参考1.f~1.h重新启动ebk_lb。之后转到1.i。
- 执行cat /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf/hcpconf.ini | grep Loadbalance命令，获取浮动IP地址。
- 如果是IPv4，执行ping 浮动IP地址命令，如果是IPv6，执行ping6 浮动IP地址，检查微服务所在服务器的网络通信是否正常。
- 是，请联系技术支持工程师协助解决。
- 否，修复网络。之后转到1.i。
##### 参考信息
无