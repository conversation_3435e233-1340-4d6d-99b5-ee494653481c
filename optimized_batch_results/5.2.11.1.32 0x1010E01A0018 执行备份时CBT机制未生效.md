# 5.2.11.1.32 0x1010E01A0018 执行备份时CBT机制未生效

##### 告警解释
对受保护对象（[Machine_name]）中磁盘（ID：[Disk_id]）执行了磁盘预置大小的完全备份而非基于CBT机制的备份。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1010E01A0018 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Machine_name | 受保护对象名称。 |
| Disk_id | 磁盘ID。 |
##### 对系统的影响
- 目标磁盘如果为精简盘则可能会多占用备份存储空间。
- 如果用该备份副本进行恢复则会占用该磁盘预置大小的生产存储空间。
##### 可能原因
生产端主机掉电重启后或者受保护对象进行过恢复操作后生产端CBT机制未生效。
##### 处理步骤
- 可能原因1：生产端主机掉电重启后或者受保护对象进行过恢复操作后生产端CBT机制未生效。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 查看任务详情中是否包含获取CBT失败的信息。
- 是，执行1.c。
- 否，请联系技术支持工程师协助解决。
- 单击“受保护环境 > VMware”，根据受保护对象的名称找到受保护环境vCenter的IP。
- 登录vCenter Client，根据受保护对象的名称（Machine_name）找到目标受保护对象。
- 单击“受保护对象”，然后选择“快照 > 快照管理器”。
- 删除受保护对象存在的所有快照。
- 手动对该受保护对象执行全量备份并查看任务详情观察问题是否解决。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。