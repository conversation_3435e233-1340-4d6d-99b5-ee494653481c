# 5.2.3.1.4 ALM-6015 NTP服务器与外部时钟源网络故障或外部时钟源故障

##### 告警解释
在配置外部时钟源之后，ntp-server（NTP服务器）会周期性（默认为2min）检查主ntp-server所在节点与外部时钟源的连接状态，当与所有外部时钟源出现网络故障或者外部时钟源本身故障，且持续时间超过30分钟后，产生此告警。当任意一个外部时钟源故障恢复或者网络故障恢复后，该告警消除。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6015 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>Region：产生告警的region名。<br>Object：产生告警的服务。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：NTP客户端ip。<br>对端地址：外部时钟源ip。 |
##### 对系统的影响
如果本地主ntp-server所在节点与外部时钟源网络故障或者外部时钟源故障，则不与外部时钟源进行时间同步。
##### 可能原因
- 本地ntp与外部时钟源连接网络故障。
- 外部时钟源未启动或者故障。
- 外部时钟源所在机器防火墙不允许ntpd访问。
- 配置外部时钟源时，外部时钟源和所选的网络平面不在同一网段下，且未配置NTP网关。
如果ManageOne运维面上，存在多个此告警，且资源标识相同，但对象类型不一致，需要将对象类型为host的告警手动清除。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 选择“运维 > 组件维护”，进入“组件维护”页面。
3. 在“组件”页签的组件列表中，“全部服务”选择“ntp”，“全部组件”选择“ntp-server”。单击“搜索”，记录“组件状态”为“active”的主机ID。
4. 在“概要”界面，根据获取到的“active”的主机ID，在“管理IP地址”一栏获得“active”的主机的管理IP地址。
5. 选择“配置 > 系统 > NTP”，进入NTP配置页面。在“网络平面”一栏获取“外出网口”信息，在“外部NTP（IP/域名）”获取“外部时钟源”信息。
外部时钟源可能配置了多个，请全部记录。
6. 判断“NTP网关”和“外部NTP（IP/域名）”是否在同一网段下。
- 是，执行9。
- 否，执行7。
7. 重新配置“NTP网关”、“NTP客户端IP”和“NTP子网掩码”，并单击“提交”。
8. 等待5分钟～10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行9。
9. 使用PuTTY，通过4中获取到的IP地址，登录主ntp-server部署所在节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
10. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
11. 执行以下命令，防止系统超时退出。
TMOUT=0
12. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
13. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
14. 执行以下命令，确定主ntp-server节点与外部时钟源之间网络是否畅通。
ping 外部时钟源 -I 外出网口
如：ping *********** -I external_api
- 是，执行15。
- 否，请先排查并修复网络问题，网络问题修复后，等待5-10分钟，查看该告警是否清除，清除则处理完成，未清除请执行15继续排查。
如果存在多个外部时钟源，则对这些时钟源依次确认，必须保证至少一个时钟源畅通。
IPV4场景使用ping，IPV6场景使用ping6。
15. 登录外部时钟源，执行命令service ntpd status，查看外部时钟源是否正常。
- 是，状态为“Active: active (running)”，执行16。
- 否，状态为“Active: inactive (dead)”，执行命令service ntpd start启动外部ntpd服务器，修复外部时钟源故障或设置其它外部时钟源，执行17。
命令service ntpd status仅适用于排查linux系统的外部时钟源，其它服务器类型需要根据服务器自身使用说明书，确保外部时钟源正常。
16. 查看防火墙是否允许ntpd及ntpdate端口访问。
- 是，执行17。
- 否，修改防火墙配置，执行17。
ntpd使用的端口是123，ntpdate使用的端口是124。
17. 切换回主ntp-server部署所在节点，执行以下命令，检测外部时钟源是否可用。
ntpdate -d 外部时钟源ip
42174775-2DA8-B93F-CF47-47902E7AA2B0:~ # ntpdate -d *************
16 Jul 15:41:04 ntpdate[11708]: ntpdate 4.2.6p5@1.2349-o Thu Dec 20 00:00:00 UTC 2018 (1)
Looking for host ************* and service ntp
host found : *************
transmit(*************)
receive(*************)
transmit(*************)
receive(*************)
transmit(*************)
receive(*************)
transmit(*************)
receive(*************)
server *************, port 123
stratum 11, precision -24, leap 00, trust 000
refid [*************], delay 0.02600, dispersion 0.00008
transmitted 4, in filter 4
reference time:    e0d80014.4e9ed783  Tue, Jul 16 2019 15:49:40.307
originate timestamp: e0d80021.d92d4d44  Tue, Jul 16 2019 15:49:53.848
transmit timestamp:  e0d7fe16.32e552fc  Tue, Jul 16 2019 15:41:10.198
filter delay:  0.02612  0.02635  0.02600  0.02661
0.00000  0.00000  0.00000  0.00000
filter offset: 523.6492 523.6490 523.6492 523.6489
0.000000 0.000000 0.000000 0.000000
delay 0.02600, dispersion 0.00008
offset 523.649214
16 Jul 15:41:10 ntpdate[11708]: step time server ************* offset 523.649214 sec
回显信息中最后一行有类似“step time server ************* offset 523.649214 sec”信息，即offset后有具体时差，则外部时钟源可用。
- 是，执行18。
- 否，执行20。
18. 执行以下命令，检测外部时钟源是否对接正常。
- IPV4 环境执行：ntpq -4p
- 是， 执行19。
- 否，执行20。
- IPV6环境执行：ntpq -6p
- 是， 执行19。
- 否，执行20。
以IPV4环境为例，回显信息中外部时钟源IP前有“*”则表示对接成功。
42174775-2DA8-B93F-CF47-47902E7AA2B0:~ # ntpq -4p
remote             refid             st  t     when poll reach   delay       offset           jitter
==============================================================================
LOCAL(0)         .LOCL.          5   l       8       16    3        0.000       0.000        0.000
*************     LOCAL(0)     11 u     7     16      3         0.620      15392.4     0.098
19. 等待5分钟～10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行20。
20. 请联系技术支持工程师协助解决。
##### 参考信息
无。