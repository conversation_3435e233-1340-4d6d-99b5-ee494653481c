# ********.21 0x201000310015 数据库连接失败

##### 告警解释
在服务器（IP：[NodeIP]）上的微服务（[MicroService_Name]）连接数据库（IP：[IP_Address]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310015 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |
##### 对系统的影响
该微服务无法正常运行，可能导致系统整体性能下降。
##### 可能原因
- 数据库服务未启动。
- 网络异常。
##### 处理步骤
- 可能原因1：数据库服务未启动。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“ps -ef | grep gaussdb”命令，查看数据库进程是否存在。
- 是，执行2。
- 否，执行1.d。
- 执行“sh /opt/huawei-data-protection/ebackup/bin/gaussdb_sandbox.sh restart”命令，重启数据库服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
- 可能原因2：网络异常。
- 使用PuTTY，以hcp用户通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf/hcpconf.ini | grep Loadbalance”，获取浮动IP地址。其中“ebk_xxx”为微服务名称。
- 如果是IPv4，执行“ping 浮动IP地址”命令，如果是IPv6，执行“ping6 浮动IP地址”，检查网络通信是否正常。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.e。
- 排查网络相关问题后，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无