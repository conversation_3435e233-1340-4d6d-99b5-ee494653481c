# 5.2.3.1.110 ALM-1316000 OpenStack服务包升级完成后未提交

##### 告警解释
upg-server定时（默认每天一次）检查服务包是否已经提交，如果有服务包升级生效后超过7天没有提交，则上报该OpenStack服务包升级完成后未提交的告警；当该服务包升级提交后，该告警自动恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316000 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。 |
| 附加信息 | 云服务：产生告警的云服务。<br>错误信息：告警相关的错误信息。 |
##### 对系统的影响
系统运行无影响，但是会影响下一次的升级。
下次升级之前，必须要先提交上次的升级服务包。长期不提交，如果配置、角色等发生了变化，可能导致后续提交失败。同时，会占用一部分系统磁盘空间。
##### 可能原因
升级完成后，忘记最后在升级工程上做提交操作。
##### 处理步骤
1. 判断当前版本的升级是否是所有部件已经全部完成。
- 否，忽略本告警， 待升级全部完成后再处理。
- 是，执行2。
2. 当时的升级工程是否还存在。
- 是，执行3。
- 否，执行4。
3. 重新启动升级工程，参考对应版本升级指导书中的“提交工程”章节，在升级工程中做提交操作，提交完成后执行8。
4. 使用“PuTTY”工具登录任意主机。
以fsp帐号登录，默认密码是*****。
执行su - root命令，切换到root帐户。输入root帐户密码，默认密码是*****
执行“Enter”，并导入openstack环境变量。
5. 执行以下命令，查询服务版本号，复制回显中upgrade cmd下的服务及版本信息。
upgrade service-pkg-list
6. 执行以下命令，使用命令行提交服务。
upgrade commit --service-pkg-list service_package_list
其中service_package_list是执行5得到的结果。
如：upgrade commit --service-pkg-list "gcn_network, GCN EVS 1.0.RC7.SPC100B010" "ceilometer, FUSIONSPHERE CEILOMETER 6.5.1" "uvp_hostos, UVP KVM 3.0.RC2.SPC101B010" "upgradeservice, FUSIONSPHERE UPGRADE 6.5.1" "aodh, FUSIONSPHERE AODH 6.5.1" "nova, FUSIONSPHERE NOVA 6.5.1" "heat, FUSIONSPHERE HEAT 6.5.1" "keystone, FUSIONSPHERE KEYSTONE 6.5.1" "gcn-egf, GCN EGF 1.0.RC7.SPC10B010" "ironic, FUSIONSPHERE IRONIC 6.5.1" "cinder, FUSIONSPHERE CINDER 6.5.1" "glance, FUSIONSPHERE GLANCE 6.5.1" "fusionplatform, FUSIONSPHERE FUSIONPLATFORM 6.5.1" "swift, FUSIONSPHERE SWIFT 6.5.1" "drextend, FUSIONSPHERE DREXTEND 6.5.1" "neutron, FUSIONSPHERE NEUTRON 6.5.1"
7. 执行upgrade query查询提交进度。
8. 提交完成后，等待30分钟，查看告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
以"fsp"用户到登录Openstack控制节点，切换到"root"用户并导入环境变量，执行下面命令,可以查看升级生效的时间：
upgrade history