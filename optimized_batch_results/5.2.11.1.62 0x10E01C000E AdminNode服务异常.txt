# ********.62 0x10E01C000E AdminNode服务异常

##### 告警解释
在HA节点（IP：[Node_Name]）上AdminNode服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000E | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能造成业务中断。
##### 可能原因
- 系统发生HA主备倒换。
- AdminNode进程异常。
##### 处理步骤
- 可能原因1：系统发生HA主备倒换。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行service hcp status命令，查看回显信息中是否存在“AdminNode”字段，且命令执行结果为“running”。
- 是，执行2。
- 否，执行1.d。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 可能原因2：AdminNode进程异常。
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行service hcp start命令，启动AdminNode服务。
- 执行service hcp status命令，查看回显信息中是否存在“AdminNode is running”。
- 是，执行2.e。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无