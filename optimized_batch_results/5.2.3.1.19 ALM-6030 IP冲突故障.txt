# *******.19 ALM-6030 IP冲突故障

##### 告警解释
OpenStack周期（默认为60s）对所有主机上的ip进行检查，如果ip和其他节点存在冲突的情况，则上报本host的IP故障告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6030 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 产生告警的主机ID。 |
| 附加信息 | 异常信息：<br>ip：冲突ip。<br>port：冲突端口。<br>mac：冲突mac。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |
##### 对系统的影响
该ip涉及的服务不能正常工作。
##### 可能原因
环境上存在多套控制节点。
##### 处理步骤
1. 根据告警对象和附加信息找出存在ip冲突告警的所有主机hostid列表和冲突的ip。
- 记录下上报告警的多台host的hostid及冲突的ip，执行2。
- 如果只有一台主机上报了ip冲突告警，执行12。
2. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
3. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
4. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
5. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
6. 执行以下命令，防止系统超时退出。
TMOUT=0
7. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
8. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
9. 根据2~8，逐个ssh方式登录到上报告警的多个主机，根据IP地址类型，执行对应的命令，查看当前环境上的所有冲突的IP信息。其中，$ipaddress为冲突的ip。
- IPv4地址，请执行ip addr show | grep 'inet '| grep '$ipaddress/'
- IPv6地址，请执行ip addr show |grep '$ipaddress' -C 5
举例如下：ip addr show | grep 'inet '| grep '***********/'
输出结果类似如下：
inet ***********/20 scope global
secondary brcps:novcprox
10. 根据每台机器上查询出来冲突IP的对应网口名，判断是否属于此范围：brcps、brcps:cps-s、brcps:zk-server、brcps:cbs-s。
- 是，执行11。
- 否，执行12。
11. 根据之前步骤的分析，对于存在冲突的主机，分析这个主机是否属于本AZ（根据用户的规划排查是否属于本AZ，无法区分，请联系技术支持工程师协助解决）。
- 如果所有的主机都属于本AZ，则执行12。
- 如果存在不属于本AZ的主机，则对该主机进行下电，如果是要扩容的主机请重新pxe处理。
12. 请联系技术支持工程师协助解决。
##### 参考信息
无。