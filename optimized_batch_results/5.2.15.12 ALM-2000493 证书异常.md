# 5.2.15.12 ALM-2000493 证书异常

##### 告警解释
自定义监控项，系统每隔一个小时检查一次消息通知服务所使用的证书，证书过期、有效期在30天内、证书无效都会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000493 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
证书过期后系统不可用，无法提供服务，需尽快处理异常。
##### 可能原因
- 阈值为1：证书即将过期。
- 阈值为2：证书过期。
- 阈值为3：证书无效或者证书不存在。
##### 前提条件
- 已获取.jks格式的证书和证书加密密码。证书名称为smn_ps.jks。
- JKS中存在别名（alias）为silvan_server的服务证书。
- 已准备跨平台远程访问工具，如“PuTTY”。
- 已准备网络传输工具，如“WinSCP”。
- 已获取ps节点的管理IP地址、hermes和root帐户的登录密码。
“root”帐户的默认密码是“*****”，“hermes”帐户的默认密码是“*****”。可以使用默认jks证书库的默认密码：*****。
##### 操作步骤
1. 使用网络传输工具（例如“WinSCP”），将证书上传到ps节点的“/opt/hermes/publishServer/config”路径下。
2. 在本地使用解压缩工具解压部署包。
加密工具“safetool-x.x.x-release.tar.gz”在解压后的部署包中。
3. 使用网络传输工具（例如“WinSCP”），将加密工具“safetool-x.x.x-release.tar.gz”上传到ps节点的“ /opt/hermes”路径下
4. 使用PuTTY，以“hermes”帐户登录ps节点。
5. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
6. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
若使用默认密码*****，请跳过7~13。
7. 执行以下命令导入环境变量。
source /etc/profile
8. 执行以下命令，进入证书库所在目录。
cd /opt/hermes/publishServer/config
9. 执行以下命令，修改证书权限。
chmod 400 smn_ps.jks
chown hermes:hermes smn_ps.jks
10. 执行以下命令，加密证书密码。
cd /opt/hermes/safetool/bin
sh safetool -b
显示以下信息：
Please input the path of root key:
11. 输入“/opt/hermes/publishServer/config/rootkey”，按“Enter”。
显示以下信息：
Please input your password:
12. 输入帐户证书加密使用的密码，按“Enter”。
显示以下信息：
Please input your password again:
13. 再次输入帐户证书加密使用的密码，按“Enter”。
生成密码的密文形式，需记录该密文。
14. 执行以下命令，使用vi编辑器打开rest.properties文件。
vi /opt/hermes/publishServer/config/rest.properties
15. 按“i”进入编辑状态。
16. 修改配置文件中的参数“server.rest.ssl.keystore”。
新jks证书库与原证书库名称相同，都是smn_ps.jks，则不需要修改。将参数“server.rest.ssl.keystore”的值修改为“config/smn_ps.jks”，即证书文件的保存路径。
17. 按“Esc”，输入:wq，按“Enter”。
保存修改并退出vi编辑器。
18. 执行以下命令，删除加密工具。
cd /opt/hermes
rm -rf safetool*
19. 执行以下命令删除原有证书：
rm -rf /opt/hermes/publishServer/config/smn_ps.jks
20. 执行以下命令，重启publishServer。
sh /opt/hermes/publishServer/bin/publishServer_monitor.sh restart
publishServer可以正常重启时，表示替换证书成功。
##### 参考信息
无。
< 上一节