# 5.2.3.1.62 ALM-73016 CPU主频异常

##### 告警解释
按15分钟周期检测主机CPU主频，连续五次检测到主机CPU当前主频超出硬件支持的最大值或者低于最小值，系统产生此告警；当主机CPU主频恢复到正常范围时，系统产生恢复告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73016 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID ：产生告警的主机ID。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
- CPU主频过低可能导致系统性能严重下降，业务功能受影响。
- CPU主频过高，可能会损坏CPU。
##### 可能原因
CPU硬件出现故障。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 分别执行以下命令，查询系统的最大频率和最小频率。
cat /sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq
cat /sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_min_freq
6. 执行以下命令，查询/sys/devices/system/cpu目录下CPU的个数。
ls /sys/devices/system/cpu
根据查询结果，针对每个CPU执行如下命令，获取对应CPU的当前频率，例如通过如下命令获取cpu8的当前频率。
cat /sys/devices/system/cpu/cpu8/cpufreq/cpuinfo_cur_freq
7. 根据查询结果，确认是否所有CPU当前频率cpuinfo_cur_freq的数值都在最小频率cpuinfo_min_freq和最大频率cpuinfo_max_freq范围内。
- 是，执行9。
- 否，执行8。
8. 检查是否是CPU硬件故障。
- 是，更换硬件设备，再次执行7。
- 否，执行9。
9. 由于频率可能存在动态变化情况，重复执行至少5次如下命令，确认cpuX 的当前频率是否在系统最小频率和最大频率范围内。
cat /sys/devices/system/cpu/cpuX/cpufreq/cpuinfo_cur_freq
- 是，执行10。
- 否，执行11。
10. 等待15~30分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行11。
11. 请联系技术支持工程师协助解决。
##### 参考信息
无。