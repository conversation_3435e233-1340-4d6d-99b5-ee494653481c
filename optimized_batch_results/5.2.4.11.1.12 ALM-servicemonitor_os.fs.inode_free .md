# 5.2.4.11.1 ALM-servicemonitor_os.fs.inode_free inode空闲率阈值告警

5.2.4.11.1.12 ALM-servicemonitor_os.fs.inode_free inode空闲率阈值告警
##### 告警解释
当被监控对象的inode空闲率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.fs.inode_free | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
当被监控对象的inode空闲率过低时，说明磁盘分区inode即将用满，会导致系统无法继续在该分区下创建新目录或文件，影响系统正常使用。
##### 可能原因
系统磁盘下存在大量的碎文件。
##### 处理步骤
本处理步骤中所呈现的数值、文件、进程等信息仅作为告警消除的示例，可能与实际环境中的信息不一致，具体操作中请以实际环境信息参考如下步骤进行处理。
1. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
2. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
3. 查看inode空闲率，如果有如下异常情况，执行4，否则联系技术支持协助处理。
df -i
如下图，“IUse%”列显示占用率为90%或较高值对应的文件夹，相当于inode空闲率为10%或较低值对应的文件夹。
4. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术支持工程师协助解决。
##### 参考信息
无。