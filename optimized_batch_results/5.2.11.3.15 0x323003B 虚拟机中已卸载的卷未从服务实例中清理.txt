# 5.2.11.3.15 0x323003B 虚拟机中已卸载的卷未从服务实例中清理

##### 告警解释
虚拟机中已卸载的卷未从服务实例中清理。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003B | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
占用系统存储资源。
##### 可能原因
虚拟机的卷被卸载。
##### 处理步骤
1. 登录ManageOne运营面，检查服务实例中是否存在“云硬盘已被卸载”的虚拟机。VHA服务实例具体操作请参见查看受保护的ECS/BMS。云服务器容灾服务和云服务器高可用服务“查看受保护的ECS/BMS”的操作方法与此相同。
- 是，VHA服务实例请参见从VHA实例中取消云硬盘容灾保护，删除不再被虚拟机使用的卷资源。 云服务器容灾服务和云服务器高可用服务“取消云硬盘保护”的操作方法与此相同。
- 否，请转3。
2. 删除后检查告警是否已清除。
- 流程结束。
- 否，请转3。
3. 请联系技术支持工程师协助解决。
##### 参考信息
无。