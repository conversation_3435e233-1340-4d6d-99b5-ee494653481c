# 5.2.6.2.3 5.2.6.2.3.1 排查FusionSphere OpenStack

5.2.6.2.3.1 排查FusionSphere OpenStack
Region Type I场景，本章节指导排查级联层FusionSphere OpenStack。
Region Type I场景，cinder分为级联层和被级联层。当产生错误的时候，需要对级联层和被级联层依次进行排查，主要方式为搜索错误日志，根据具体错误信息进行分析处理。
Region Type II，Region Type III场景，本章节指导排查FusionSphere OpenStack。
- 若是cinder模块发生错误，根据日志即可定位错误原因。
- 若是环境配置错误，参考文档手册，其中列出了常见问题的处理方法。
- 若是周边模块产生问题，则将日志信息传递给相关人员协同定位错误原因。
本文重点探讨cinder模块中错误的情形。