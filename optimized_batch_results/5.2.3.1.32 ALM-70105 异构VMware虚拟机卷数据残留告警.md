# 5.2.3.1.32 ALM-70105 异构VMware虚拟机卷数据残留告警

##### 告警解释
当vCenter处理挂卷比较慢的时候或其他异常场景，导致虚拟机挂卷超时可能会产生虚拟机的卷数据残留，这个时候会触发该告警（该告警目前只针对异构对接VMware场景的虚拟机）。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70105 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>卷ID：产生告警的虚拟机使用的卷ID<br>bdm数据ID：产生告警的卷的bdm数据ID |
| 附加信息 | 详细信息：告警的详细信息<br>主机名：产生告警的主机名称<br>主机ID：产生告警的主机ID<br>可用分区：产生告警的可用分区 |
##### 对系统的影响
虚拟机卷数据残留会影响对用户的数据展示，可能会影响相关数据的统计。
##### 可能原因
- vCenter内部挂卷时间太长导致并发给同一个虚拟机挂多个卷的时候，挂卷超时导致数据残留。
- 其他挂卷异常场景也有可能会导致此类数据残留。
##### 处理步骤
1. 查看告警信息，获取虚拟机id（instance_id），卷id（volume_id）和bdm数据id（bdm_id）。
2. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
6. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
7. 执行命令cinder list --all|grep volume_id，查看该卷是否存在。
- 是，执行8。
- 否，执行9。
8. 执行命令cinder show volume_id，查看该卷的状态status字段是否在中间态（attaching、detaching等状态）。
- 是，清除告警，处理完毕。
- 否，执行9。
9. 登录nova数据库。
- 执行命令cps template-instance-list --service gaussdb gaussdb，获取active节点的管理面ip。
- 执行命令su fsp，切换为fsp用户。
- 执行命令ssh fsp@管理面ip，切换至管理面IP。
- 执行命令su gaussdba -c '/opt/gaussdb/app/bin/gsql nova'，输入密码*****，切换到nova数据库。
10. 执行如下命令，查询结果是否有数据返回。其中，这里的bdm_id请从告警定位信息中“bdm数据ID”获取。
select * from block_device_mapping where id=bdm_id and deleted=0;
- 是，执行11。
- 否，清除告警，处理完毕。
11. 查看返回结果中的connection_info字段是否有值。
- 是，清除告警，处理完毕。
- 否，执行12。
12. 执行\q退出数据库，执行清理脚本：sh /usr/bin/info-collect-script/audit_resume/delete_bdm_byid.sh vm_uuid volume_id bdm_id，这三个id告警信息中都会有提供，清理该数据残留，查看结果是否回显success并且没有error信息。
- 是，清除告警，处理完毕。
- 否，执行13。
13. 请联系技术支持工程师协助解决。
##### 参考信息
无。