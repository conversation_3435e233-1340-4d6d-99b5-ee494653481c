# ********.1 ALM-48401-进程未启动

##### 告警解释
API网关的某些组件进程没有启动。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48401 | 紧急 | 设备告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 故障组件名称 |
| 定位信息 | Node | 故障节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |
##### 对系统的影响
上报告警的组件进程异常，会对连接该组件的相关业务产生影响。
##### 可能原因
组件进程不存在。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示故障节点IP地址。
- Component：表示故障组件名称。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录告警源节点Node。
默认帐号：paas，默认密码：*****。
3. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 执行以下命令，切换到组件对应的用户，如表1所示。
su - 用户名
| 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 |
| --- | --- | --- |
| 组件 | 用户名 | 命令 |
| gaussdb | apigw_db | su - apigw_db |
| adminportal | apigw_portal | su - apigw_portal |
| apigmgr | apigw_apimgr | su - apigw_apimgr |
| cassandra | apigw_scdb | su - apigw_scdb |
| 其他组件（除gaussdb、adminportal、apigmgr、cassandra以外的组件） | apigateway | su - apigateway |
6. 执行以下命令，检查组件状态是否正常。
sh /opt/apigateway/Component/shell/health_check.sh
- normal => 8
- abnormal => 7
7. 执行以下命令，重启组件。该重启操作对系统本身无不良影响。
sh /opt/apigateway/Component/shell/restart.sh
显示“xxx start successfully”，表示组件启动成功。
8. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 确认组件状态正常，可手动清除告警。否则，执行9。
9. 获取相关日志，并联系技术支持。
- 执行如下命令，切到root用户。
exit
- 执行如下命令，切换到日志目录。
- 故障组件（Component对应值）为“shubao” => 执行命令：cd /var/log/apigateway/shubao/run
- 故障组件（Component对应值）为其他组件 => 执行命令：cd /var/log/apigateway/Component/runtime
- 下载该目录中的所有日志到本地，并联系技术支持。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。