# 5.2.10.2 ALM-1223006 ETCD集群健康检查告警

##### 告警解释
ELB管理节点、LVS节点或Nginx节点连接ETCD节点失败，生成此告警。
管理节点服务正常时，每隔15秒会对ETCD节点连接，如果连续三次一半以上的ETCD节点都连接失败，产生此告警。
LVS、Nginx节点服务正常时，每隔5秒会对ETCD节点连接，如果连续三次所有的ETCD节点都连接失败，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223006 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
- 影响正常业务流程处理
- 配置下发失败
- 配置回滚失败
- 配置不一致
- 拉取配置文件失败
- 管理节点推送配置文件失败
- ELB TCP流量异常
- ELB HTTP流量异常
##### 可能原因
多台ETCD服务器异常导致ETCD服务集群异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：对端地址
5. 使用PuTTY，登录异常ETCD节点。
登录地址：4中查出的对端地址。
默认帐户：elb
默认密码：*****。
6. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
若ETCD节点不能正常登录，请联系技术工程师协助解决。
7. 分别在所有ETCD节点上执行以下命令，查看ETCD进程是否正常启动。
ps -ef|grep etcd
命令回显如下，ETCD进程存在，表示ETCD进程正常启动。
- 是，请联系技术工程师协助解决。
- 否，请执行8
8. 执行以下命令，重新启动ETCD进程。
cd /usr/local/NSP/scripts
sh start_etcd.sh
重启ETCD进程不会对其他业务造成影响。
9. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。