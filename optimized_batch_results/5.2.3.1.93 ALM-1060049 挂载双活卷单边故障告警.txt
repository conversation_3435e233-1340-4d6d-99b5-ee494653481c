# 5.2.3.1.93 ALM-1060049 挂载双活卷单边故障告警

##### 告警解释
用户执行虚拟机迁移、虚拟机HA、虚拟机重启等操作后，自动发起挂载双活卷，挂载过程中需要挂载本端卷和远端卷，当两个卷中的一个卷挂载失败时，触发单边故障告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060049 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务名<br>虚拟机名：产生告警的虚拟机名<br>被挂载的双活目标LUN：产生告警的双活目标LUN ID |
##### 对系统的影响
无
##### 可能原因
- CSHA/VHA场景中，本端或者远端阵列掉电或者故障。
- CSHA/VHA场景中，本端或者远端阵列中，业务网iSCSI/FC接口卡故障，或者所有业务网链路故障。
##### 处理步骤
1. 通过告警详情确认如下信息：
- ID
- 定位信息（虚拟机ID）
- 附加信息
- 产生时间
2. 根据告警定位信息虚拟机ID，查询其对应的volume type。
- 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令，防止系统超时退出。
TMOUT=0
- 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
- 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
- 执行nova-show 虚拟机ID | grep volume，查看该虚拟机中卷的“id”字段得到卷的volume_id 。
- F6128CAC-5D50-898F-E811-173EEC1ABA81:~ # nova show 69dab372-fdd8-4a01-b4cb-deb24b2b32dc | grep volume
| image                         | Attempt to boot from volume - no image supplied       |                                                                                  |os-extended-volumes:volumes_attached | [{"id": "7423b611-b198-4386-a0a2-97e927a6c0b2", "delete_on_termination": false}, {"id": "8521f9cd-c181-4a1e-91d0-1f8d359b1819", "delete_on_termination": false}]                                                              |
- 执行cinder list --all-t | grep volume_id 查看卷的详细信息，并从第六列获取对应的volume type。
也可执行命令cinder list --all-t，从“Volume_Type”字段获取volume_id对应的volume type。
F6128CAC-5D50-898F-E811-173EEC1ABA81:~ # cinder list --all-t | grep 7423b611-b198-4386-
a0a2-97e927a6c0b2
| 7423b611-b198-4386-a0a2-97e927a6c0b2 | f9bb363bedea465db31fc7db40f8a0b0 | in-use | qingrui_ecs-979avolume-0000 | 1 | aaa |true | False | 69dab372-fdd8-4a01-b4cb-deb24b2b32dc |
F6128CAC-5D50-898F-E811-173EEC1ABA81:~ # cinder list --all-t
+----+-----------+-------+-----+------+-------------+---------+-------------+-------------+
| ID | Tenant ID | Status|Name | Size | Volume Type |Bootable | Multiattach | Attached to |
+----+-----------+-------+-----+------+-------------+---------+-------------+-------------+
|00ed9691-1eed-4552-89f3-0cffc26d8568 | 19f6373b30f64d1f8ab4be7a3802e7f4 | available|
volume-9e86 | 1 |ggg | false | False | |
3. 登录Service OM界面查看该volume_type对应的后端存储。
登录Service OM具体操作请参见登录和注销Service OM界面。
4. 登录FusionSphere OpenStack安装部署界面，查看3中的后端存储对应存储设备信息。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
进入“配置 > 资源池管理 > KVM > 配置存储集群”界面，查看对应后端存储的配置获取存储设备详细信息，排查修复阵列、接口卡或者其链路故障。
5. 在OpenStack首节点，执行命令nova list --all-t | grep 虚拟机ID，查看该虚拟机详情，其中第二个字段即是该虚拟机的Name。Region Type I场景下，虚拟机ID要参见告警附加信息中的虚拟机名的UUID部分。
也可执行命令nova list --all-t，从“Name”字段获取虚拟机的Name。
F6128CAC-5D50-898F-E811-173EEC1ABA81:~ # nova list --all-t | grep 69dab372-fdd8-4a01-b4cb-deb24b2b32dc
| 69dab372-fdd8-4a01-b4cb-deb24b2b32dc | APIGW-PODLB-01         | 319c29f0410a461fb1274be6264e5c45 | ACTIVE            | -                 | Running     | DMZ_Service=**********; DMZ_Tenant=*********                                 |
F6128CAC-5D50-898F-E811-173EEC1ABA81:~ # nova list --all-t
+---------+-----------+-------------+-----------+--------------+-------------+--------+
| ID      | Name      | Tenant ID   | Status    | Task State   | Power State | Networks   |
+---------+-----------+-------------+-----------+--------------+-------------+--------+
| 69dab372-fdd8-4a01-b4cb-deb24b2b32dc | APIGW-PODLB-01         | 319c29f0410a461fb1274be6264e5c45 | ACTIVE            | -                 | Running     | DMZ_Service=**********; DMZ_Tenant=*********                                 |
6. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
若登录不成功，请使用以下登录地址、eReplication的帐号和密码登录eReplication：
- 登录地址：https://eReplication的IP地址:9443/src/login/view/login.html
eReplication节点的管理平面IP地址，可从导出的部署LLD中查询eReplication_Server01参数获得。
- 默认帐号：admin，默认密码：*****
7. 登录eReplication后，依次查询所有双活实例中的虚拟机，找到目标虚拟机所在双活实例。双活实例中的虚拟机名称即为5中查询出来的虚拟机Name。
8. 单击目标双活实例右侧的“更多 > 重映射”，重新挂载并扫描故障已恢复的本端或远端卷。查看后台任务执行状态，待重映射任务成功后，再到Service OM界面中查看告警，确认告警已恢复。
##### 参考信息
无。