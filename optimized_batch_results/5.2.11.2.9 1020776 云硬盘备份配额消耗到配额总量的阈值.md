# 5.2.11.2.9 1020776 云硬盘备份配额消耗到配额总量的阈值

##### 告警解释
云硬盘备份配额消耗到配额总量的阈值。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020776 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 配额类型 | 消耗到阈值的配额类型。volume_backup_capacity代表备份配额，volume_copy_capacity代表复制配额。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 总量 | 配额的总量。 |
| 已使用 | 配额的已使用量。 |
| 阈值 | 产生告警需要超过配额已使用/总量的百分比。 |
| 清除阈值 | 清除告警需要低于配额已使用/总量的百分比。 |
##### 对系统的影响
不及时扩容租户配额，可能导致手动备份、自动备份失败。
##### 可能原因
配额消耗到配额总量的阈值。
##### 处理步骤
- 可能原因：配额消耗到配额总量的阈值。
- 获取该条告警附加信息中“租户名称”，“项目名称”。
- 联系“租户名称”对应的租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到“项目名称”对应的项目下。
- 进入“云硬盘备份”页面，手动申请空间或者删除不必要的副本。申请空间的详细操作请参见申请备份空间。
##### 参考信息
无。