# ********.1 ALM-48303-无法读写FTP文件

##### 告警解释
API网关ftpfilesync服务异常或者文件权限被篡改，网关其他组件无法读写FTP文件。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48303 | 重要 | 处理错误告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 定位信息 | File_Name | 无法访问的文件名 |
##### 对系统的影响
ftpfilesync服务异常或者文件权限被篡改，authadv或orchestration等无法读写ftp文件，无法从ftpfilesync组件获取最新的文件状态。
##### 可能原因
ftpfilesync服务出现异常或者文件权限被篡改。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Source_Node：表示告警源节点IP地址。
- Fault_Node：表示故障节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录故障节点Fault_Node。
默认帐号：paas，默认密码：*****。
3. 执行以下命令，防止会话超时退出。
TMOUT=0
4. 执行以下命令，登录SFTP服务。
sftp -oPort=port 用户@登录节点IP地址
其中port为FTP服务端口，缺省为“2022”；用户为“ftpapimgr”或者“ftpapigw”。
- 登录正常 => 5
- 登录失败 => 参见ALM-48107-无法访问FTP。
5. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 6
6. 获取相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
sudo su - root
默认密码：*****。
- 执行如下命令，切换到source_component日志目录。
cd /var/log/apigateway/Source_Component/runtime/
- 下载此目录下所有日志到本地，并联系技术支持。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。