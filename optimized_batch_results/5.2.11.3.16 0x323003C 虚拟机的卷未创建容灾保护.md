# 5.2.11.3.16 0x323003C 虚拟机的卷未创建容灾保护

##### 告警解释
服务实例中虚拟机的卷未创建容灾保护，导致该虚拟机不能进行数据一致性保护。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003C | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中未创建容灾保护的卷对应的虚拟机名称 |
| 服务实例类型 | 服务实例的类型 |
##### 对系统的影响
虚拟机不能被一致性保护。
##### 可能原因
虚拟机使用的卷未创建复制保护。
##### 处理步骤
1. 登录ManageOne运营面，添加未保护的卷。VHA服务实例具体操作请参见《华为云Stack 6.5.1 用户指南(Region Type I)》中添加新云硬盘到VHA实例，云服务器容灾服务和云服务器高可用服务“添加新云硬盘”的操作方法与此相同。
2. 添加后检查告警是否已清除。
- 是，流程结束。
- 否，请转3。
3. 请联系技术支持工程师协助解决。
##### 参考信息
无。