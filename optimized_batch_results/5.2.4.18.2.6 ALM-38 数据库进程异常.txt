# 5.2.4.18.2 ALM-38 数据库进程异常

5.2.4.18.2.6 ALM-38 数据库进程异常
##### 告警解释
当数据库进程异常停止时，连续10次启动数据库（间隔10秒）；如果数据库进程重新出现，该告警将会被自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 38 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
数据库进程异常会导致业务访问数据库失败，涉及到数据读取的故障、安全菜单操作都无法进行，只能进行拓扑菜单操作。如果长时间异常会导致告警信息丢失或者业务面不可用。
##### 可能原因
数据库进程异常停止。
##### 处理步骤
1. 检查数据库是否异常。并尝试重启异常数据库。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“产品 > 系统监控”。
- 在“系统监控”页面左上方，光标移至依次选择所有的产品。在界面右上方，检查“关系数据库”和“Redis数据库”是否存在异常资源。
红色数字为异常资源的个数。
- 是，尝试启动异常数据库实例。
- 选择“节点”页签，在异常的数据库实例所在节点所在行，单击右侧的。
- 等待任务结束后，检查数据库状态，如果数据库状态恢复正常，告警清除，则处理完毕。否则，继续排查其他原因。
- 否，表示非数据库运行异常的原因，继续排查其他原因。
2. 尝试登录数据库，检查数据库密码是否正确。具体操作请参见《华为云Stack 6.5.1 故障处理》中的“数据库常用操作”章节。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
3. 产生该告警的节点名称发生了变化。
4. 产生该告警的站点名称发生了变化。
5. 产生该告警的服务器不被监控了。
##### 参考信息
无。