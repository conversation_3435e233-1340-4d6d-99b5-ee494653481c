# *******.1 ALM-1131012 tomcat进程down掉

*******.1.4 ALM-1131012 tomcat进程down掉
##### 告警解释
镜像转换节点tomcat进程异常，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131012 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
通过镜像文件创建镜像的功能异常。
##### 可能原因
tomcat进程异常等。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”。
3. 在页面上方的菜单栏，选择“集中告警”，进入“集中告警”页面。
4. 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
5. 查询以下告警信息：
- 告警ID
- 来源设备名称
- IP地址
6. 使用“PuTTY”工具，登录CPT-SRV01或CPT-SRV02节点。
登录地址：5中获取的附加信息的IP地址
默认帐号：sysadmin，默认密码：*****。
7. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
8. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
9. 执行以下命令，查看tomcat进程是否存在？
ps -ef| grep tomcat| grep imcs
- 是，执行10。
- 否，执行11。
10. 等待3分钟，观察告警通知是否消除？
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
11. 执行如下命令，重启tomcat进程。
sh /opt/imcs/tomcat/bin/startup.sh
12. 参考9，查看tomcat进程是否存在？
- 是，则等待3分钟观察告警是否消除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节