# 5.2.11.2.23 1023276 消息队列产生网络分区

##### 告警解释
网络故障导致rabbitmq节点间无法相互访问，导致数据不一致，功能异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023276 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名称 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
可能导致依赖rabbitmq的服务业务异常。
##### 可能原因
内部面网络故障，rabbitmq节点无法相互访问。
##### 处理步骤
- 可能原因：网络故障，rabbitmq节点无法相互访问。
- 查看是否存在“备份服务节点间网络异常”告警。
- 是，根据告警修复建议修复网络故障。
- 否，请执行1.b。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行命令：stop_service --service rabbitmq停止所有节点rabbitmq。
- 执行命令：start_service --service rabbitmq启动所有节点rabbitmq。操作完成后，如果告警未清除，请执行1.f。
- 请收集日志目录“/var/log/huawei/dj/services/system/rabbitmq”，然后联系技术支持工程师协助解决。
##### 参考信息
无。