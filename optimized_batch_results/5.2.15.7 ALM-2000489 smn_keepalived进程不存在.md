# ******** ALM-2000489 smn_keepalived进程不存在

##### 告警解释
LB节点keepalived进程异常，就会产生此告警 。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000489 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
LB的心跳不起作用，可能会引起LB功能异常，需尽快排查。
##### 可能原因
无
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
6. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****
7. 执行以下命令，防止系统超时退出。
TMOUT=0
8. 执行以下命令，查看keepalived是否正常。
service keepalived status
- keepalived正常，请联系技术支持工程师协助解决。
- keepalived异常，请执行9。
9. 执行以下命令，重启服务，并运行10分钟后检查告警是否恢复。
rm -rf /home/<USER>/keepalived/run/*;
service keepalived start
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。