# 5.2.17.12.2 ALM-2001106 Etcd服务状态异常

##### 告警解释
- Etcd服务状态异常时，本节点业务不可用，系统产生此告警。
- 此告警提示系统故障或风险，需要处理。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2001106 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
产生告警的节点业务不可用。
##### 可能原因
- 节点配置的NTP服务器异常，Etcd服务进程无法启动。
- Etcd配置错误，服务进程无法启动。
- 本节点和其它云平台仲裁服务节点网络中断，导致Etcd服务处于不健康状态。
- 节点异常掉电，Etcd数据库损坏，导致服务进程无法启动。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：arbiter，默认密码：*****。
6. 执行如下命令，查看进程状态是否为“active (running)”。
systemctl status arbitration-etcd
根据不同的回显信息，执行相应步骤。
- 回显中包含“active (running)”，执行14。
- 回显中不包含“active (running)”，执行7。
7. 执行如下命令，查看NTP服务状态。
ntpq -p
根据不同的回显信息，执行相应步骤。
- 回显包含如下信息：“ntpq: read: Connection refused”，表示本节点NTP服务异常，执行8。
- 回显信息中，“remote”列中NTP服务IP前面没有带“*”时，表示NTP服务状态异常，执行10。
- 回显信息中，“remote”列中NTP服务IP前面带“*”时，表示NTP服务状态正常，执行11。
- 其它回显情况，执行8。
8. 执行如下命令，重启NTP服务进程。
systemctl restart ntpd
9. 等待5分钟，执行如下命令，查看NTP服务状态。
ntpq -p
根据不同的回显信息，执行相应步骤。
- 回显信息中，“remote”列中NTP服务IP前面没有带“*”时，表示NTP服务状态异常，执行8。
- 回显信息中，“remote”列中NTP服务IP前面带“*”时，表示NTP服务状态正常，执行11。
- 其它回显情况，执行8。
10. 请联系管理员排查本节点与NTP服务器之间的网络是否正常，检查NTP服务器本身是否正常，连接恢复后，执行8。
11. 执行如下命令，重启Etcd服务进程。
systemctl restart arbitration-etcd
12. 执行如下命令，查看进程状态是否为“active (running)”。
systemctl status arbitration-etcd
- 回显中包含“active (running)”，执行13。
- 回显中不包含“active (running)”，执行11。
13. 等待3分钟，查看告警是否自动清除。
- 是，处理完成。
- 否，执行14。
14. 若systemctl status arbitration-etcd状态正常，执行以下命令查看health接口。
curl http://127.0.0.1:4001/health
- 回显为{"health":"true"}，则表示接口正常，执行17。
- 回显为其他，则表示接口异常，执行15。
15. 执行以下命令，排查网络状态。
python /opt/arbitration-etcd/script/check-etcd.py --etcdips +仲裁虚拟机的IP
仲裁虚拟机的IP，可在《HUAWEI CLOUD Stack Deploy LLD》表格中获取该IP。
- 回显如下，则表示网络状态正常，执行17。
- begin to check etcd cluster health status ...
- *************: {"health": "true"}
- begin to check etcd cluster members ...
- *************: [{"ip": "*************", "id": "73e7360c21a28ca0", "name": "arbitration-etcd0"}, {"ip": "*************", "id": "9771513a03c58460", "name": "arbitration-etcd3"}, {"ip": "*************", "id": "a7f46cba81f618fd", "name": "arbitration-etcd1"}, {"ip": "*************", "id": "b0e78dd83d2274bd", "name": "arbitration-etcd2"}, {"ip": "************", "id": "fd3648d689baa04a", "name": "arbitration-etcd4"}]
check etcd cluster configuration success.
- 若网络不通，请联系网络工程师排查网络。
16. 执行以下命令，查看日志，查看etcd选举状态。
grep "starting a new election" /var/log/arbitration-etcd/arbitration-etcd.log
若网络环境、安防设备及存储情况均正常，但etcd大量发起选举，请联系技术支持工程师。
17. 等待3分钟，查看告警是否自动清除。
- 是，处理完成。
- 否，执行6。
18. 请联系技术支持工程师处理。
##### 参考信息
无。