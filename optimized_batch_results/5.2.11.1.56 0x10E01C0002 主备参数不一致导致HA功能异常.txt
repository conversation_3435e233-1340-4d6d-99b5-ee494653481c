# ********.56 0x10E01C0002 主备参数不一致导致HA功能异常

##### 告警解释
主备参数不一致，HA功能异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0002 | 紧急 | 是 |
##### 对系统的影响
HA功能不可用。
##### 可能原因
主备参数不一致。
##### 处理步骤
- 可能原因1：主备参数不一致。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/ha/module/hacom/script命令，进入script目录。
- 执行sh config_ha.sh -a命令，查看HA主备节点参数是否一致。
- 是，执行1.g。
- 否，请联系技术支持工程师协助解决。
回显类似如下表示HA主备节点参数是一致的。
HA主节点回显类似如下：
[root@eBackup script]# sh config_ha.sh -a
HaMode:       double
HaLocalName:  ha1(active)
HaPeerName:   ha2(standby)
HaProtocol:   ssl
HaArbLk:      **************:5555  --   **************:5555
HaSyncLk:     **************:6666  --   **************:6666
HaRpcLk:      127.0.0.1:61806
HaArpLk:      **************
HaGwLk:       ***********
HA备节点回显类似如下：
linux-MAVRil:/opt/huawei-data-protection/ebackup/ha/module/hacom/script # sh config_ha.sh -a
HaMode:       double
HaLocalName:  ha2(standby)
HaPeerName:   ha1(active)
HaProtocol:   ssl
HaArbLk:      **************:5555  --  **************:5555
HaSyncLk:     **************:6666  --  **************:6666
HaRpcLk:      127.0.0.1:61806
HaArpLk:      **************
HaGwLk:       ***********
- 在当前告警界面手动清除该告警。
##### 参考信息
无