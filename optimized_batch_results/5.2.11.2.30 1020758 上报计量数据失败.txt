# ********.30 1020758 上报计量数据失败

##### 告警解释
上报计量数据失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020758 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点IP | 产生告警的节点IP。 |
| 错误码 | 故障对应的错误码。 |
| Meter地址 | 计量服务接口地址。 |
##### 对系统的影响
云服务器备份无法生成计费信息。
##### 可能原因
- Karbor节点和FusionSphere OpenStack之间的网络中断。
- 对接Ceilometer的连接异常。
##### 处理步骤
- 可能原因：Karbor节点和FusionSphere OpenStack之间的网络中断。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行命令docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep ceilometer_endpoint获取配置的ceilometer的url地址。
- 执行ping ceilometer的url地址命令检查到FusionSphere OpenStack Ceilometer的网络是否连通。
- 是，请执行2。
- 否，请联系网络管理员修复网络。
- 可能原因：对接Ceilometer的连接异常。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Ceilometer”行对应的“Check_Result”值。
- 如果为“OK”，请联系技术支持工程师协助解决。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果为“Error”，执行set_karbor_endpoints --cei_endpoint Ceilometer的url地址命令设置 Ceilometer的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“DMK_g_regions:fsp_Cascading.metering”获取Ceilometer的url地址。再次执行2.c。
- 如果仍未修复与Ceilometer的连接异常，请联系技术支持工程师协助解决。
##### 参考信息
无。