# 5.2.4.14.1 5.2.4.14.1 ALM-MOBackupService_100001 备份失败

5.2.4.14.1 ALM-MOBackupService_100001 备份失败
##### 告警解释
当业务备份失败时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOBackupService_100001 | 重要 | 操作告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 业务类型 | 被备份的业务类型。 |
##### 对系统的影响
业务备份失败，当该业务数据异常时，影响该业务恢复。
##### 可能原因
- 备份数据库实例失败。
- 上传文件到备份服务器失败。
- 下载文件到本地失败。
- 查找数据库实例失败。
- 任务执行超时。
- 获取话单文件失败。
- 下发任务失败。
##### 处理步骤
1. 使用PuTTY工具，通过告警参数里的IP地址、端口和协议登录SFTP备份服务器，检查SFTP备份服务器是否可以正常登录。
默认用户名为“sftpuser”。
SFTP备份服务器的用户名和密码需与ManageOne准备SFTP备份服务器时创建的用户名和密码一致。
- 是，表示备份服务器可以正常登录。
- 否，参见准备第三方SFTP备份服务器检查备份服务器配置。
2. 检查备份服务器磁盘空间是否不足。
- 使用PuTTY工具，通过告警参数里的IP地址、端口和协议登录备份服务器。
默认用户名为“sftpuser”。
备份服务器的用户名和密码需与ManageOne准备备份服务器时创建的用户名和密码一致。
- 执行如下命令，查看磁盘空间是否不足。
df -h 备份目录
备份目录需与配置备份服务器的备份目录一致。
回显示例如下，Avail的值即为剩余磁盘空间。
Filesystem                  Size  Used Avail Use% Mounted on
/dev/mapper/oss_vg-opt_vol  172G  115G   49G  71% /opt
请保证服务器的存储空间不少于10GB。
- 磁盘空间不足，请释放磁盘空间。
- 磁盘空间足够，执行3。
3. 检查用户在备份目录下是否有写权限。
- 使用PuTTY工具，通过告警参数里的IP地址、端口和协议登录备份服务器。
默认用户名为“sftpuser”。
备份服务器的用户名和密码需与ManageOne准备备份服务器时创建的用户名和密码一致。
- 执行如下命令，查看备份目录是否有写权限。
ls -l 备份目录
备份目录需与配置备份服务器的备份目录一致。
回显示例如下，回显权限中有w字母即表示是否有写权限。
-rwxr----- 1 <USER> <GROUP>   3748 Jan  8 18:39 del.py
-rw-r----- 1 <USER> <GROUP> 208474 Jan  8 18:51 log_del.log
-rw-r----- 1 <USER> <GROUP>  87934 Jan  8 18:43 t2
-rw-r----- 1 <USER> <GROUP>  87934 Jan  8 18:44 t3
- 是，执行4。
- 否，参见准备第三方SFTP备份服务器、配置备份策略检查备份服务器配置更换SFTP备份服务器备份目录或重新配置备份服务器参数。
4. 使用PuTTY工具，依次登录ManageOne-Service01/02/03、ManageOne-DB01/02、ManageOne-Deploy01/02节点，检查这七个节点能否正常连接SFTP备份服务器。请在参考信息中查询节点对应的IP。
- 依次登录ManageOne-Service01/02/03、ManageOne-DB01/02、ManageOne-Deploy01/02节点。
sopuser用户的默认密码为“*****”
- 依次以sopuser用户登录ManageOne-Service01/02/03。
sopuser用户的默认密码为“*****”。
执行如下命令切换到ossuser用户
su - ossuser
ossuser用户的默认密码为“*****”。
- 依次以sopuser用户登录ManageOne-DB01/02、ManageOne-Deploy01/02。
sopuser用户的默认密码为“*****”。
执行如下命令切换到ossadm用户。
su - ossadm
ossadm用户的默认密码为“*****”。
- 依次检查ManageOne-Service01/02/03、ManageOne-DB01/02、ManageOne-Deploy01/02节点能否正常连接SFTP备份服务器。
sftp sftpuser@SFTP备份服务器IP地址
SFTP备份服务器的用户名和密码需与ManageOne准备SFTP备份服务器时创建的用户名和密码一致。
- 是，执行5。
- 否，请参见准备第三方SFTP备份服务器排查SFTP服务器设置问题。
5. 执行以下操作，检查话单备份文件。
- 使用PuTTY工具以sopuser用户登录ManageOne-Service01节点。
sopuser用户的默认密码为“*****”。
- 执行以下命令，登录SFTP运营话单服务器。
ssh meteradmin@SFTP运营话单服务器的地址
meteradmin用户的默认密码为“*****”。
- 在运维面主菜单选择“系统管理 > 平台配置 > 系统维护”，在左侧导航栏单击“配置信息管理”，单击“数据上报配置”页签，单击“METERSFTP”组名称，IP地址的值即为SFTP运营话单服务器的地址。
- 命令执行过程中如果回显“Are you sure you want to continue connecting (yes/no)?”，请输入“yes”后按“Enter”。
- 执行以下命令，切换到root帐号。
sudo su root
root用户的默认密码为“*****”。
- 执行以下命令，查看话单备份文件。
cd /opt/meterfiles/uploads/
ll
回显示例如下：
drwxr-x--- 2 <USER> <GROUP> 16384 Mar  1 07:15 bandwidth
drwxr-x--- 2 <USER> <GROUP> 16384 Mar  1 07:15 ip
drwxr-x--- 2 <USER> <GROUP> 16384 Mar  1 07:15 lbaas
drwxr-x--- 2 <USER> <GROUP> 20480 Mar  1 07:15 snapshot
drwxr-x--- 2 <USER> <GROUP> 16384 Mar  1 07:15 vm
drwxr-x--- 2 <USER> <GROUP> 16384 Mar  1 07:15 volume
drwxr-x--- 2 <USER> <GROUP> 20480 Mar  1 07:15 vpnconnection
- 回显中所有文件的所属用户都为meteradmin，表示话单文件正常。
- 回显中所有文件的存在所属用户需要不为meteradmin，表示话单备份文件异常，需联系技术支持工程师协助解决。
6. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
7. 在部署面主菜单中选择“产品 > 系统监控”。
单击切换到“ManageOne”或“CloudSOP-UniEP”页面，单击“关系数据库”页签。
managedbsvr-x-x@x-x数据库实例在“CloudSOP-UniEP”页面，其他数据库实例在“ManageOne”页面。
根据告警参数中的数据库实例名称，检查“角色”列为“Slave”的该实例名称行的状态是否为“正在运行”。
- 是，执行8。
- 否，执行9。
8. 检查并记录是否能正常连接数据库。
- 使用PuTTY工具以sopuser用户通过告警参数中的数据库节点IP登录告警数据库实例所在节点ManageOne-DB01/02、ManageOne-Service03、ManageOne-Deploy01/02。请在参考信息中查询节点对应的IP。
sopuser用户的默认密码为“*****”。
- 执行以下命令，切换到数据库维护帐户dbuser。
su - dbuser
dbuser用户的默认密码为“*****”。
- 执行如下命令，检查能否正常连接上数据库。
- Gauss数据库，针对恢复部署系统定时服务业务、部署系统框架业务、部署系统业务的数据场景，数据库实例与业务的对应关系查询参见查看数据库业务所在数据库实例。
LD_LIBRARY_PATH="/opt/gauss/app/lib" /opt/gauss/app/bin/gsql -d postgres -h 数据库节点IP地址 -p 端口号 -W dbuser用户密码
命令示例：
LD_LIBRARY_PATH="/opt/gauss/app/lib" /opt/gauss/app/bin/gsql -d postgres -h ************** -p 32089 -W test@123
*****为dbuser用户的默认密码，根据告警参数里的数据库节点IP地址和数据库实例名称的查询对应端口号，具体操作参见查看数据库业务所在数据库实例。
- Zenith数据库，针对恢复存储运维管理业务、运营管理业务、IAM业务、统一日志中心业务的数据场景，
数据库实例与业务的对应关系查询参见查看数据库业务所在数据库实例。
LD_LIBRARY_PATH='/opt/zenith/app/lib:/opt/zenith/app/add-ons' /opt/zenith/app/bin/zsql sys/sys用户密码@数据库节点IP地址:端口号
命令示例：
LD_LIBRARY_PATH='/opt/zenith/app/lib:/opt/zenith/app/add-ons' /opt/zenith/app/bin/zsql sys/test@123@************:32081
*****为dbuser用户的默认密码，根据告警参数里的数据库节点IP地址和数据库实例名称的查询对应端口号，具体操作参见查看数据库业务所在数据库实例。
9. 根据告警参数中业务类型，参见手动备份数据重新备份该业务。
- 备份成功，问题解决。
- 备份失败，请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，告警自动清除。
##### 参考信息
以下介绍查找节点对应的管理IP地址的方法。
10. 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
11. 输入用户名、密码，单击“登录”。
12. 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
13. 在“节点名称”列查找待查询管理IP地址的节点。
14. 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。