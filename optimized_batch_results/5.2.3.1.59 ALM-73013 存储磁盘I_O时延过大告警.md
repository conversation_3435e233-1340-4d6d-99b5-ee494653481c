# 5.2.3.1.59 ALM-73013 存储磁盘I/O时延过大告警

##### 告警解释
对于本地磁盘：每5秒中读取一次本地磁盘的I/O时延并保存，每5分钟读取这5分钟内计算的I/O时延平均值，如果该平均值有超过一半（30次以上）大于用户配置的IO延时阈值（默认为100ms），则系统产生告警。当有一半及以上小于等于阈值时，告警恢复。注，不对NVME盘做IO时延监控。
对于多路径磁盘：每分钟读取一次多路径磁盘的I/O读写量，当某一个磁盘I/O量连续5次小于同一多路径设备中I/O量最大磁盘的2%，则系统产生此告警，当某一个磁盘I/O量大于等于同一多路径设备中I/O量最大磁盘的2%，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73013 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>磁盘名称：异常磁盘的名称。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
- 导致处理磁盘I/O读写占用的CPU利用率过高。
- CPU处理业务速度较慢。
##### 可能原因
- 硬件原因导致磁盘I/O读写响应过慢。
- 没有组raid导致性能问题，或者raid卡驱动等有问题。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 使用smart工具自检，确认是否为硬件问题。
- 从上报告警信息中获取磁盘名${DIVICE}。
- 执行命令smartctl -i /dev/${DIVICE}查看硬件是否支持SMART。
- 如果命令回显输出：SMART support is: Enabled，表示支持smart。执行8.c触发samrt自检。
- 如果命令回显输出：SMART support is: Disabled 或 Unavailable，表示不支持smart。
不支持smart的情况，一般是因为配置的raid卡不支持，此时需要使用对应raid卡厂商的检查工具。
- 触发smart自检。执行smartctl -t long /dev/${DEVICE}命令，后台检测硬件，消耗时间较长。
- 执行smartctl -l selftest /dev/${DEVICE}查看自检结果，回显中最近一次结果排在前面。
- 状态为“Completed”则说明结果正常，则可认为磁盘暂时无严重问题。
- 如果报error，且出现多次慢盘场景，建议联系硬盘厂家或更换硬盘。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。