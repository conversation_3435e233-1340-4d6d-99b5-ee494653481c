# 5.2.4.6.4 ALM-30005 用户密码已过期

##### 告警解释
系统中的三方系统接入用户或admin用户密码已经达到有效期后，产生该告警。当系统中的三方系统接入用户或admin用户密码在有效期内时，该告警自动清除。
如果admin用户处于锁定状态，当admin用户的密码已过期时不会产生该告警。
- 用户密码剩余最短时间可在“密码策略”中设置：
- 在主菜单中选择“系统管理 > 安全管理 > 安全策略”。
- 在左侧导航树中选择“密码策略”。
- 设置“密码有效天数”。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 30005 | 紧急 | 时间域告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 用户名 | 密码过期的用户名称。 |
##### 对系统的影响
密码已经过期的用户不能登录ManageOne运维面。
##### 可能原因
三方系统接入用户或者admin用户密码已过期。
##### 处理步骤
1. 查看告警“定位信息”字段的值，确认密码已过期的用户为三方系统接入用户还是admin用户。
- 三方系统接入用户密码已过期，执行2。
- admin用户密码已过期，执行3。
2. 联系安全管理员重置密码。
- 使用安全管理员帐号登录ManageOne运维面。
- 在主菜单中选择“系统管理 > 安全管理 > 用户管理”。
- 在左侧导航树中选择“用户”。
- 在“用户”列表中，单击密码已过期的三方系统接入用户“操作”列的“重置密码”，重置用户密码。
- 重置密码后，执行4。
3. 通过系统提示修改密码。
- 使用admin用户登录ManageOne运维面。
- 在系统提示界面输入“旧密码”、“新密码”和“确认密码”，单击“应用”。
4. 1小时后，查看本告警是否清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。