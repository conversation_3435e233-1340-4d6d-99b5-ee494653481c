# 5.2.3.2.8 ALM-9209 当前资源数量大于License许可上限

##### 告警解释
告警模块按24小时周期检测License状态，当检测到当前资源使用值大于License授权许可值时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9209 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 资源类型：License界面显示的资源使用值大于License授权许可值的License的英文授权名称列表 |
##### 对系统的影响
- 用户登录系统后，主机(上电、下电)、主机组（创建）、裸金属服务器（初始化）、虚拟机（修改规格）的功能将无法继续使用。
- 将无法添加新的KVM服务器到系统中。(默认License不涉及。)
- 将无法分配License资源给其他部件。(默认License不涉及。)
##### 可能原因
当前系统中资源使用量已经超过License授权的资源数。
##### 处理步骤
加载已经申请到的正确的License。
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“系统 >系统管理 > License”。
3. 单击“上传License”，在弹出的窗口中选择新申请的License文件。
4. 单击“打开”。
5. 加载License是否成功。
- 是，执行6。
- 否，执行7。
6. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，进入“OpenStack告警”页面，查看告警是否清除。
- 是，处理完毕。
- 否，执行7。
7. 请联系技术支持工程师协助解决。
如果正确的License文件已经丢失，重新申请License文件，并重新进行加载。
8. 单击“系统”，进入“系统”页面。
9. 单击“系统管理”下的“License”，进入“License”页面。
10. 记录页面上显示的ESN号码。
11. 准备合同号。
12. 联系技术支持工程师申请License文件。
13. 在“License”页面，单击“上传License”，在弹出的窗口中选择新申请的License文件。
14. 单击“打开”。
15. 加载License是否成功。
- 是，执行16。
- 否，执行17。
16. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，进入“OpenStack告警”，查看告警是否清除。
- 是，处理完毕。
- 否，执行17。
17. 请联系技术支持工程师协助解决。
##### 参考信息
无。