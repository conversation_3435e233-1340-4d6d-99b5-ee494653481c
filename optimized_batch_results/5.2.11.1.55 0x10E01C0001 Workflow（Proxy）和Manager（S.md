# ********.55 0x10E01C0001 Workflow（Proxy）和Manager（Server）的版本不兼容

##### 告警解释
Workflow（Proxy）（IP：[IP_addr]）与Manager（Server）的版本不兼容。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0001 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | Workflow（Proxy）IP地址。 |
##### 对系统的影响
Workflow（Proxy）在Manager（Server）上进行注册时将失败。
##### 可能原因
Workflow（Proxy）与Manager（Server）的版本不同。
##### 处理步骤
- 可能原因：Workflow（Proxy）与Manager（Server）的版本不同。
- 使用PuTTY，依次登录告警上报的节点以及该节点的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 在Workflow（Proxy）和Manager（Server）上执行“showsys | grep "Product Version"”命令，检查Workflow（Proxy）与Manager（Server）版本是否一致，如果版本不一致，则升级Workflow（Proxy）版本与Manager（Server）一致。
具体操作方法请参考《OceanStor BCManager xxx eBackup 升级指导书》。
- 检查所有Workflow（Proxy）的版本是否和Manager（Server）一致，针对版本号和Manager（Server）版本不一致的Workflow（Proxy）则进行升级操作，确保和Manager（Server）版本一致。
##### 参考信息
无