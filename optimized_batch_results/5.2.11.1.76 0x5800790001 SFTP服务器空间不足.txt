# 5.2.11.1.76 0x5800790001 SFTP服务器空间不足

##### 告警解释
当前管理数据备份设置的SFTP服务器（路径：[SFtp_path]）空间不足。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x5800790001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| SFtp_path | SFTP服务器路径。 |
##### 对系统的影响
管理数据备份到SFTP服务器失败。
##### 可能原因
SFTP服务器空间不足。
##### 处理步骤
- 可能原因1： SFTP服务器空间不足。
- 使用SFTP客户端工具，通过用户和密码登录SFTP服务器。
- 检查SFTP服务器共享目录下是否存在无用的数据。
- 是，执行1.c。
- 否，请联系管理员对SFTP服务器共享存储进行扩容。
- 执行rm -rf 无用的文件或目录命令，删除无用的数据。
##### 参考信息
无