# 5.2.4.18.2 ALM-53080 部署面服务进程资源占用异常

5.2.4.18.2.9 ALM-53080 部署面服务进程资源占用异常
##### 告警解释
当部署面连续20次检测（检测周期30秒）到部署面服务进程资源的CPU使用率大于等于告警门限值（默认值为90%）时，产生该告警。只要有一次检测到部署面服务进程资源的CPU使用率小于告警门限值时，该告警将会被自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 53080 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 进程名称 | 产生告警的进程名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
业务处理缓慢，可能导致消息堆积或系统崩溃。
##### 可能原因
- 非重要进程未及时释放。
- 有占用系统资源大的进程正在运行。
##### 处理步骤
1. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
2. 在主菜单中选择“系统 > 任务列表”，查看当前是否有任务正在执行。
- 有正在执行的任务。待任务完成后等待半个小时，检查告警是否清除。
- 是，告警处理完毕。
- 否，请执行3。
- 没有正在执行的任务，请执行3。
3. 判断该告警出现频率是否过高。
- 如果该告警仅偶尔出现且会自动清除，可直接忽略，无需处理。
- 如果该告警出现频繁（一个小时内出现5次以上），且不会自动清除，请联系华为技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
4. 产生该告警的节点名称发生了变化。
5. 在告警产生后升级了操作系统或者安装了操作系统补丁。
6. 产生该告警的站点名称发生了变化。
7. 产生该告警的服务器不被监控了。
##### 参考信息
无。