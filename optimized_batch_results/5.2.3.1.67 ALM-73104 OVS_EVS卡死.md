# 5.2.3.1.67 ALM-73104 OVS/EVS卡死

##### 告警解释
当ovs-vswitchd进程存在但出现异常卡死的状态系统会上报此类告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73104 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
影响进程所在主机的软交换，导致数据面不通。
##### 可能原因
系统异常、进程自身异常。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行如下命令查看ovs进程是否处于D状态或T状态。
cat /proc/`pidof ovs-vswitchd`/status | grep State
- 是，请使用"service openvswitch restart"命令重启ovs服务，再执行9。
重启openvswitch服务可能会造成系统已有业务中断。
- 否，执行10。
9. 一段时间后告警是否恢复。
- 是，任务结束。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。