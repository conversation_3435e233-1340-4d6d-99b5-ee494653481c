# 5.2.3.1.10 ALM-6021 主机网口状态异常

##### 告警解释
Openstack周期检测主机被用户注册的物理网口和bond的状态，当检测到主机的网口或bond异常时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6021 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机：产生告警的主机ID<br>网口名： 产生告警的网卡名称 |
| 附加信息 | 主机ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>BMC_IP：产生告警的主机BMC IP地址<br>聚合模式：产生告警网卡的聚合模式<br>聚合名：产生告警网卡的聚合名称 |
##### 对系统的影响
会影响该网口所在网络平面和外部通信的可靠性，可能导致该网口所在主机与其他相连设备通信中断。
##### 可能原因
- 主机某一个网口未被用户注册。
- 主机某一个网口被禁用。
- 网线松动或损坏。
- 主机某一个网口或者网卡故障。
- 主机某一个网口所连交换机端口故障。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行命令cat /usr/bin/ports_info |python -m json.tool，查看告警定位信息中网口名的相关信息。
- 查看回显中bond_info信息，确认告警网口是否组了bond。
- 是，执行5.b，获取bond名对应的mappingtype。如下回显中nic0 和nic1组了bond，bond名为trunk0。
- 否，执行5.b，获取网卡名对应的mappingtype。
- "bond_info": [
- {
- "bond_mode": "nobond",
- "name": "trunk0",
- "slaves": [
- "nic0",
- "nic1"
- ]
- }
]
- 查看回显中ovs信息，确认网口或者bond所属的mappingtype。
- mapping类型为ovs，或者mapping类型为hardware-veb，且"pf_tag": "false" ，执行6。
- 其他类型，执行8。
- "ovs": [
- {
- "interface": "trunk0",
- "mappingtype": "ovs",
- "name": "physnet1",
- "om_vlan": ""
- },
- {
- "interface": "nic2",
- "mappingtype": "ovs",
- "name": "evs_net",
- "om_vlan": ""
- },
- {
- "interface": "nic3",
- "mappingtype": "hardware-veb",
- "mtu": "1800",
- "name": "physnet2",
- "om_vlan": "1598",
- "pf_tag": "false",
- "promisc": "false",
- "reserve_vf": "false",
- "trusted": "true",
- "vf_num": "61"
- }
]
6. 执行ifconfig eth*（其中，eth*为故障的网口，比如eth0），查看网口是否被禁用（查看回显信息中是否“UP”字段，有则表明没有被禁用）。
- 是，执行7。
- 否，执行8。
7. 执行ifconfig eth* up命令，使得网口启动，等待1分钟~2分钟，看告警是否恢复。
- 是，处理完毕。
- 否，执行8。
8. 查看网线是否松动或损坏。
- 是，更换网线，执行9。
- 否，执行10。
9. 等待1分钟~2分钟，看告警是否恢复。
- 是，处理完毕。
- 否，执行10。
10. 参考更换主机及配件相关章节，更换对应的网卡，等待1分钟~2分钟，看告警是否恢复。
- 是，处理完毕。
- 否，执行11。
11. 请联系技术支持工程师协助解决。
##### 参考信息
无。