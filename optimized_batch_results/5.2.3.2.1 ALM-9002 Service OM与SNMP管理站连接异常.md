# *******.1 ALM-9002 Service OM与SNMP管理站连接异常

##### 告警解释
Service OM启动30s定时任务周期检测SNMP管理站是否在位，通过ping对端IP的方式实现，如果连续6次出现ping超时，触发此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9002 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>SNMP管理站ID：SNMP管理站的ID |
| 附加信息 | SNMP管理站名称：产生告警的SNMP管理站名称<br>本端地址：ServiceOM的浮动IP地址<br>对端地址：SNMP管理站中配置的IP地址 |
##### 对系统的影响
当出现此告警时，由于Service OM与SNMP管理站连接异常，则Service OM与对应SNMP管理站之间的业务功能将不可用。
##### 可能原因
- SNMP管理站IP地址配置不正确。
- 网络中断。
- 服务器异常。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 选择“监控 > 告警 > 告警列表 > OpenStack告警”。
进入“OpenStack告警”页面。
3. 单击本条告警所在行的折叠按钮，查看告警详细信息，记录附加信息中的SNMP管理站名称和对端地址。
4. 进入“系统 > 对接 > 告警 > 告警上报 > SNMP管理站”页面。
5. 在“SNMP管理站”页面，检查IP地址配置是否正确。
- 是，执行7。
- 否，修改SNMP配置，配置正确的IP地址。
6. 大约5分钟后，查看告警是否清除。
- 是，处理完毕。
- 否，执行7。
7. 使用“PuTTY”，以“galaxmanager”用户通过管理浮动IP地址登录Service OM主节点。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
8. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
9. 查看/var/log/goku/fault/snmp-authentication-failure.log日志，检查是否存在最新时间点的错误日志。
以下为SNMP对接异常情况下的常见错误：
- error=10003：SNMP管理站的IP地址配置错误，请重新配置SNMP管理站的IP地址。
- error=1408：SNMP管理站的安全用户名或者密码不正确，需要删除OM和网管上的SNMP管理站配置，等待10分钟后重新对接。
- error=1411：SNMP管理侧收到网管发送的报文消息无效，需要删除OM和网管上的SNMP管理站配置，等待10分钟后重新对接。
10. 如果snmp-authentication-failure.log没有错误，通过ping持续观察SNMP管理站配置的IP地址，检查网络是否正常。
- 是，执行12。
- 否，请检查OM和网管之间的网络，确保网络恢复正常后执行12。
11. 大约5分钟后，查看告警是否清除。
- 是，处理完毕。
- 否，执行12。
12. 请联系技术支持工程师协助解决。
##### 参考信息
无。