# 5.2.3.1.85 ALM-73401 rabbitmq服务故障

##### 告警解释
rabbitmq进程可以运行，但内部出现了异常，不能正常对外提供服务或者某个队列连续三次发送消息失败。
紧急：rabbitmq进程可以运行，但内部出现了异常，不能正常对外提供服务。
重要：某个队列连续三次发送消息失败。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73401 | 紧急/重要 | 紧急可自动清除，重要需要手动清除 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>实例名：产生告警的服务所在的实例名。 |
| 附加信息 | 主机名：产生告警的主机名。<br>主机ID：产生告警的主机ID。<br>详情：告警的详细信息。 |
##### 对系统的影响
此告警产生，部分业务将无法收发消息，进而造成业务无法正常进行。
##### 可能原因
- 主备rabbitmq节点在相近时刻发生了多次重启。
- 网络频繁重连导致某一队列发送消息失败。
##### 处理步骤
1. 参考《华为云Stack 6.5.1 扩容指南》中“调整RabbitMQ的内存水位线”章节，检查内存水位线配置是否正确。
- 是，执行8。
- 否，请根据本步骤提供的参考章节，调整内存水位线配置，执行2。
2. 等待10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行3。
3. 查看告警参数中附加信息是否存在以下信息“Failed to publish message to topic XXX”，其中XXX为rabbitmq消息队列名。
- 是，记录告警参数中定位信息的内容，如happen on (['xxx.xxx.xxx.xxx'])，其中xxx.xxx.xxx.xxx为rabbitmq节点ip，然后执行4。
- 否，执行8。
4. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
5. 执行以下命令，跳转到rabbitmq所在节点。
ssh <EMAIL>，其中xxx.xxx.xxx.xxx为3中获取的rabbitmq的ip。
6. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
7. 查询可疑进程数量。
执行命令：/usr/local/lib/rabbitmq/sbin/rabbitmqctl eval 'rabbit_diagnostics:maybe_stuck().' |grep XXX，其中XXX为3中rabbitmq消息队列名。并查看返回结果。
回显如下类似信息：
<<"q-agent-notifier-update_fanout_af5c781c060d4c2dbcd16abbf38857c4">>}}},
- 如果回显中出现XXX队列名，执行15。
- 如果查询无结果，执行8。
8. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
9. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
10. 执行以下命令，防止系统超时退出。
TMOUT=0
11. 导入环境变量，具体操作请参见导入环境变量。
12. 获取组件信息。
- 如果在告警台出现该告警。可在告警信息的附加信息中，获取发生故障的主机名称。例如主机名=XXX。
- 如果在FusionSphere OpenStack安装部署界面，执行“运维 > 系统检查”时，在检查rabbitmq服务状态的结果中提示参见该文档。可在检查结果中获取发生故障的主机名字。例如‘location’:{‘XXX’}，则主机名为XXX。
运行命令：cps host-template-instance-list XXX | grep rabbitmq 从执行结果中得到AAA.BBB的组件信息，例如rabbitmq.rabbitmq。
13. 查询可疑进程数量。
重复执行8~10，登录到12中查询到的主机。
执行命令：/usr/local/lib/rabbitmq/sbin/rabbitmqctl eval 'rabbit_diagnostics:maybe_stuck().'，并查看返回结果。
回显如下类似信息：
There are 1122 processes.
Investigated 0 processes this round, 5000ms
to go.
……
Investigated 0 processes this round, 500ms
to go.
Found 0 suspicious processes.
ok
- 如果回显中出现类似"Found XXX suspicious processes."的提示信息，请重复执行该命令3次，查看是否每次都能出现该提示信息。
- 是，执行14。
- 否，执行15。
- 如果命令超过1分钟未执行完成或者回显中未出现类似"Found XXX suspicious processes."的提示信息，请执行15。
14. 等待10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行15。
15. 请联系技术支持工程师协助解决。
##### 参考信息
无。