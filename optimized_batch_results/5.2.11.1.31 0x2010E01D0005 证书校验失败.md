# ********.31 0x2010E01D0005 证书校验失败

##### 告警解释
OpenStack（组件：[Module]，IP：[IP_Address]）没有可匹配的证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01D0005 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Module | OpenStack组件名 |
| IP_Address | OpenStack组件的IP |
##### 对系统的影响
与Openstack组件连接存在安全风险。
##### 可能原因
- 系统中不存在该OpenStack组件的证书。
- 该OpenStack组件的证书已过期。
##### 处理步骤
- 可能原因1：系统中不存在该OpenStack组件的证书。
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看OpenStack类型的证书是否存在。
- 是，2。
- 否，执行1.c。
- 请参考导入eBackup周边组件证书章节，获取Openstack组件证书并导入。
- 可能原因2：该OpenStack组件的证书已过期。
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看OpenStack类型的证书是否过期。
- 是，执行2.c。
- 否，执行3。
- 请参考导入eBackup周边组件证书章节，获取Openstack组件证书并导入。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无