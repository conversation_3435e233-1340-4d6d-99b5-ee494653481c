# 5.2.4.2.2 ALM-100553 SNMP连通性检测失败告警

##### 告警解释
当对接系统SNMP连通性检测失败时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100553 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | SNMP认证失败的云服务名称。 |
| 服务 | SNMP连通性检测失败的服务名称。 |
| 对接系统类型 | SNMP连通性检测失败的对接系统类型。 |
| 系统名称 | SNMP连通性检测失败的对接系统的名称。 |
| 对接地址 | SNMP连通性检测失败的对接系统的IP地址或域名。 |
##### 对系统的影响
SNMP连通性检测失败，会导致对接系统的同步告警无法正常上报或对接系统相关业务无法下发。
##### 可能原因
- SNMP对接的IP地址或端口信息错误。
- SNMP对接参数信息错误，如用户名和密码等错误。
- 网络故障。
- SNMP对接系统故障。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
2. 在主菜单中选择“集中告警 > 当前告警”。
3. 单击目标告警所在行的，展开当前告警详细信息。
- 查看并记录告警情页面中的附加信息和告警可能原因。
- 根据告警可能原因执行对应的操作。
| 告警可能原因 | 操作 |
| --- | --- |
| SNMP对接的IP地址或端口信息错误 | 请联系对接系统的系统管理员获取并重新填写正确的IP地址和端口信息。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在左侧导航树中选择“驱动生命周期管理 > 配置管理”。<br>查看“本端IP地址”，根据部署区域信息单击对应对接系统的卡片查看“本端端口”，并重新填写正确的“本端IP地址”和“本端端口”。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行5<br>请联系技术工程师协助解决。 |
| SNMP对接参数信息错误，如用户名和密码等错误 | 请联系对接系统的系统管理员获取并重新填写正确的系统信息。<br>在主菜单中选择“系统管理 > 系统设置 > 接入管理”。<br>在左侧导航树中选择“驱动生命周期管理 > 配置管理”。<br>根据部署区域信息单击对应对接系统的卡片，在对应对接系统的“系统信息”右上方单击，并在页面中单击重新添加正确的系统信息。<br>单击对接系统“操作”列的进行连通性测试。<br>成功，结束。<br>失败，执行5<br>请联系技术工程师协助解决。 |
| 网络故障 | 使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP。<br>sopuser用户的默认密码为“*****”。<br>执行如下命令，切换到ossadm帐号。<br>su - ossadm<br>默认密码：*****<br>执行如下命令，是否能ping通对接系统的IP地址即告警参数中的“对接地址”。<br>ping IP地址<br>是，则表明SNMP对接系统故障，参考“SNMP对接系统故障”处理中的1~2处理。<br>否，请联系技术支持工程师协助解决。 |
| SNMP对接系统故障 | 根据告警定位信息的中系统名称和对接地址信息，按如下步骤排查对接系统的后台节点和ManageOne的后台节点时间是否一致。<br>使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP。<br>sopuser用户的默认密码为“*****”。<br>使用以下命令查询ManageOne系统时间。<br>date<br>回显信息：<br>Wed Aug 28 16:29:24 CST 2019<br>获取对接系统的节点IP地址。<br>对接系统的节点IP地址：生产中心安装时导出的参数信息汇总文件《xxx_export_all_CN.xlsm》的“2.1 工具生成的IP参数 ”页签中的查找对应的对接系统的后台节点IP地址，如eSight：ManageOne-eSight-01-Ip、ManageOne-eSight-02-Ip。<br>使用PuTTY工具以sopuser用户通过1.c中的获取到的IP地址登录对接系统的后台节点。<br>sopuser用户的默认密码为“*****”。<br>使用以下命令查询系统时间。<br>date<br>回显信息：<br>Wed Aug 28 16:29:24 CST 2019<br>对接系统和ManageOne的系统时间一致，执行2。<br>对接系统和ManageOne的系统时间不一致，执行1.f。<br>请联系技术工程师协助解决。<br>排查LVS是否故障。<br>按照如下步骤排查LVS是否异常。<br>使用PuTTY工具以sopuser用户通过regionAlias-ManageOne-Service01/Service02的IP地址分别登录service01和service02节点。请在参考信息中查询节点对应的IP。<br>sopuser用户的默认密码为“*****”。<br>执行以下命令切换到root用户<br>sudo su root<br>root用户的默认密码为“*****”。<br>执行以下命令查看LVS转发状态。<br>/opt/envs/RCLVSService/lvs_install/bin/ipvsadm -ln --stats<br>主节点回显信息：<br>IP Virtual Server version 1.2.1 (size=4096)<br>Prot LocalAddress:Port               Conns   InPkts  OutPkts  InBytes OutBytes<br>  -> RemoteAddress:Port<br>TCP  **************:27337               52    28479        0 17457284        0<br>  -> *************:27337                26    16714        0 10657454        0<br>  -> *************:27337                26    11766        0  6799882        0<br>TCP  **************:27400                0        0        0        0        0<br>  -> **************:27400                0        0        0        0        0<br>  -> **************:27400                0        0        0        0        0<br>TCP  **************:27402                0        0        0        0        0<br>  -> **************:27402                0        0        0        0        0<br>  -> **************:27402                0        0        0        0        0<br>UDP  **************:27367                1       64        0    21876        0<br>  -> *************:27367                 0       61        0    19398        0<br>  -> *************:27367                 1        3        0     2478        0<br>UDP  **************:27401                0        0        0        0        0<br>  -> **************:27401                0        0        0        0        0<br>  -> **************:27401                0        0        0        0        0<br>备节点回显信息：<br>IP Virtual Server version 1.2.1 (size=4096)<br>Prot LocalAddress:Port               Conns   InPkts  OutPkts  InBytes OutBytes<br>主节点、备节点回显信息如上所示不一致则表示LVS转发状态正常，主节点、备节点回显信息一致则表示LVS转发状态异常。<br>LVS转发状态正常，执行2.e。<br>LVS转发状态异常，执行2.d。<br>执行以下命令重启进程。<br>/opt/envs/RCLVSService/lvs_install/script/mgr_keepalived.sh restart<br>回显信息：<br>Shutting down Keepalived for LVS:                          [  OK  ]<br>Starting Keepalived for LVS:                               [  OK  ]<br>重启进程成功后，单击对接系统“操作”列的进行连通性测试。<br>连通性测试失败，执行2.e。<br>连通性测试成功，结束。<br>请联系技术工程师协助解决。 |
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
以下介绍查找节点对应的管理IP地址的方法。
- 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
- 输入用户名、密码，单击“登录”。
- 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
- 在“节点名称”列查找待查询管理IP地址的节点。
- 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。
< 上一节