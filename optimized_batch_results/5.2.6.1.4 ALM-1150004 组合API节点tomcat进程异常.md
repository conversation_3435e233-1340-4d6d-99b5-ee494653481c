# *******.4 ALM-1150004 组合API节点tomcat进程异常

告警解释
组合API节点tomcat进程异常时触发该告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150004 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |
对系统的影响
服务不可用。
可能原因
tomcat进程异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，登录4中确认的虚拟机。
默认帐号：apicom，默认密码：*****。
6. 执行以下命令，查看tomcat进程是否存在。
ps -ef | grep tomcat | grep apicom
apicom 3087 1 2 Apr25 ? 00:39:02 /opt/common/jre/bin/java
回显中的“3087”即为tomcat进程的进程号。
- 是，执行7。
- 否，执行8。
7. 执行以下命令，停止tomcat进程。
kill -9 进程号
其中进程号为6中获取的进程号。
8. 执行以下命令，重新启动进程。
sh /opt/apicom/tomcat/bin/startup.sh
9. 观察1分钟，查看该告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无。