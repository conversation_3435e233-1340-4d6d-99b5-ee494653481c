# 5.2.3.2.10 ALM-9215 系统磁盘使用率过大

##### 告警解释
告警模块按15分钟周期检测磁盘分区空间使用率，当检测到有系统分区空间使用率超过70%时，产生重要告警，当检测到有系统分区空间使用率超过90%时，产生紧急告警。当检测到系统磁盘分区的使用率低于70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9215 | 紧急/重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM的浮动IP地址 |
| 附加信息 | 阈值：重要告警阈值为70%，紧急告警阈值为90%<br>使用量：超过阈值的磁盘分区和磁盘分区对应的使用率信息列表 |
##### 对系统的影响
可能影响业务的正常运行。
/opt/goku/data/db分区的使用率大于等于98%时，系统会自动停机。
##### 可能原因
- 系统和用户进程生成的临时文件过多等原因。
- 人为操作目标系统，并创建大文件。
##### 处理步骤
1. 使用“PuTTY”，登录Service OM节点。
用“galaxmanager”用户，以Service OM节点的管理浮动IP登录。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止PuTTY超时退出。
TMOUT=0
4. 执行以下命令，查看分区使用率。
df -h
Filesystem                     Size  Used Avail Use% Mounted on
/dev/vda1                      6.0G  1.3G  4.7G  22% /
devtmpfs                       4.8G     0  4.8G   0% /dev
tmpfs                          4.8G     0  4.8G   0% /dev/shm
tmpfs                          4.8G  505M  4.4G  11% /run
tmpfs                          4.8G     0  4.8G   0% /sys/fs/cgroup
/dev/mapper/vg_om-lv_home      5.0G   33M  5.0G   1% /home
/dev/mapper/vg_om-lv_sysback  1014M   33M  982M   4% /sysback
/dev/mapper/vg_om-lv_var       4.0G   98M  3.9G   3% /var
/dev/mapper/vg_om-lv_tmp       3.0G   33M  3.0G   2% /tmp
/dev/mapper/vg_om-lv_log       6.0G   87M  6.0G   2% /var/log
/dev/mapper/vg_om-lv_goku      8.0G   81M  8.0G   1% /var/log/goku
/dev/mapper/vg_om-lv_opt        10G  2.0G  8.0G  20% /opt
/dev/mapper/vg_om-lv_gmbackup  6.0G   55M  6.0G   1% /opt/gmbackup
/dev/mapper/vg_om-lv_data       21G   43M   21G   1% /opt/goku/data
/dev/mapper/vg_om-lv_db        6.0G  418M  5.6G   7% /opt/goku/data/db
tmpfs                          983M     0  983M   0% /run/user/0
tmpfs                          983M     0  983M   0% /run/user/2000
回显显示每个分区的空间使用情况，其中第6列（Mounted on）是分区名称，第5列（Use%）是分区的使用率。
| 表1 分区占满的影响 | 表1 分区占满的影响 |
| --- | --- |
| 分区名称 | 分区占满的影响 |
| /opt/goku/data/db | 数据库无法正常运行，Service OM不能正常提供服务 |
| /opt/goku/data | Service OM业务不能正常运行 |
| 其他 | OS和Service OM不能正常运行 |
5. 参考查找并处理主机或者虚拟机系统中的大文件章节，搜索并处理使用率超过70％的分区下的大文件。
6. 执行以下命令，查看分区使用率。是否还有分区的使用率超过70%。
df -h
- 是，执行5。
- 否，执行7。
7. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
8. 单击“监控”，进入“告警 > 告警列表 > OpenStack告警”页面，在本条告警所在行单击“清除”手动清除告警。
9. 清除告警是否成功。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。