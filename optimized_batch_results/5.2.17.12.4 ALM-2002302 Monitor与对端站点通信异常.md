# 5.2.17.12.4 ALM-2002302 Monitor与对端站点通信异常

##### 告警解释
- Monitor与对端站点配对的节点通信异常，系统产生此告警。
- 此告警提示系统故障或风险，需要处理。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2002302 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
产生告警的节点业务不可用。
##### 可能原因
- 和对端站点网络中断
- 对端站点中存在不可用的云平台仲裁服务节点
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：arbiter，默认密码：*****。
6. 执行如下命令，获取与本节点配对的对端站点的节点信息。
python /opt/arbitration-monitor/script/check-monitor.py
回显中与本节点IP以“<->”相连的即为对端站点配对的节点IP。
7. 请联系管理员排查本节点与对端站点配对的节点之间的网络是否正常，对端站点配对的节点是否运行正常，连接恢复后，执行8。
8. 等待3分钟，查看告警是否自动清除。
- 是，处理完成。
- 否，执行9。
9. 请联系技术支持工程师处理。
##### 参考信息
无。