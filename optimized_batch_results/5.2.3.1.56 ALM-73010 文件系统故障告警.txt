# 5.2.3.1.56 ALM-73010 文件系统故障告警

##### 告警解释
文件系统产生只读故障时，系统产生此告警。
目前支持ext3, ext4文件系统。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73010 | 重要 | 系统分区重启后可自动清除，其余需要手动清除。 |
本文提到的告警“自动清除”，都需要重启服务器。如果文件系统故障没有被完全修复，告警会再度上报。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>磁盘：异常文件的名称。 |
| 附加信息 | 异常信息：告警异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
- 根分区只读会导致该服务器上大部分服务进程异常，虚拟机出现严重故障。
日志分区会被错误日志填满，日志无法及时转储，部分日志可能丢失。
- /var/log分区只读会导致日志写入，日志转储等相关操作失败。
- /var/ceilometer 分区只读会导致MongoDB服务异常。对应文件系统如果为/dev/hioa1则表示MongoDB使用SSD。
##### 可能原因
- 服务器异常掉电，导致写磁盘文件的IO丢失或者写错扇区。
- 服务器磁盘或者RAID卡硬件损坏或未配置电池。
- 磁盘驱动版本与硬件不匹配。
- 若为非本地存储，主机与存储之间连接断开。
- 磁盘数据损坏。
在执行以下步骤过程中，如发现命令执行失败，可能由于根分区被损坏，请联系技术支持工程师协助解决。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行以下命令，查看是否可以获取上报告警的磁盘分区在主机上的设备号，如253:2。
ll /sys/dev/block/ | grep -w xxx$ | awk '{print $9}'
xxx为上报告警的磁盘分区，如dm-12、sda1等。
- 是，执行9。
- 否，磁盘已经不存在，远端存储已经被卸载，可以手动清除告警，处理完毕。
9. 执行以下命令获取上报告警分区所属的物理磁盘符。
lsblk
例如，8获取的设备号为253:2，则所属的物理磁盘符为sda , 如图所示：
10. 执行如下命令，确认上报告警的分区是否为本地存储。
lsscsi -t | grep -w xxx
xxx为9获取的物理磁盘符。确认回显的第三列字符串是否以"fc:"（FCSAN）或者"iqn."（IPSAN）开头。
- 是，说明是远端存储，执行16。
- 否，说明有可能是本地存储，也可能是华为多路径存储或者FusionStorage存储，执行11。
11. 执行如下命令，查看回显中是否包含VBS fileIO。
lsscsi | grep -w xxx
xxx为9获取的物理磁盘符。
- 是，说明是FusionStorage存储，执行16。
- 否，说明可能是本地存储或者华为多路径存储，执行12。
12. 执行如下命令，查看回显信息中是否可以看到xxx磁盘。
upadmin show vlun | grep -w xxx
- 是，说明是华为多路径存储，执行16。
- 否，无xxx磁盘信息，或者提示command not found，说明服务器未安装华为多路径包，说明是本地存储，执行13。
13. 执行命令last reboot，检查上报告警后主机是否已重启。
- 是，执行14。
- 否，执行15。
14. 查看告警是否清除。
- 是，说明是系统分区故障，可自动清除，处理完毕。
- 否，说明是非系统分区故障，执行20查看文件系统是否正常。
- 是，手动清除告警，并执行21。
- 否，继续定位执行15。
15. 检查磁盘或RAID卡是否损坏。
在对应节点的BMC上检查是否有对应的硬件告警。
- 是，参考管理节点中相关章节 ，更换告警的磁盘或RAID卡。如果告警依然没有恢复，执行19。
- 否，执行19。
16. swift xfs格式分区只读检测与修复。
- swift xfs格式分区只读检测。
执行如下命令，检查swift分区是否正常。
ll /opt/HUAWEI/swift
ls: cannot access /opt/HUAWEI/swift: Input/output error
回显包含“Input/output error”，则swift分区处于只读状态，执行修复操作。如回显显示文件列表则分区正常，无需执行修复操作。
- Swift xfs格式分区只读修复，如存在命令执行失败，请联系技术支持工程师协助解决。
- 执行如下命令，检查swift的挂载目录。
cat /proc/mounts | grep swift
/dev/mapper/extend_vg-swift /opt/HUAWEI/swift xfs rw,relatime,attr2,inode64,noquota 0 0
在本例中，分区为/dev/mapper/extend_vg-swift，挂载路径为/opt/HUAWEI/swift。
- 执行如下命令，停止swift-store服务。
cps host-template-instance-operate --service swift swift-store --action stop --host 主机ID
主机ID可以从告警的附加信息上获取。
- 执行如下命令，去挂载swift分区，如去挂载成功则无回显。
umount /opt/HUAWEI/swift
- 执行如下命令，修复分区。
xfs_repair /dev/mapper/extend_vg-swift
Phase 7 - verify and correct link counts...
done
- 执行如下命令，挂载分区。
mount /dev/mapper/extend_vg-swift /opt/HUAWEI/swift
- 执行如下命令，启动swift-store服务。
cps host-template-instance-operate --service swift swift-store --action start --host 主机ID
主机ID可以从告警的附加信息上获取。
17. extX格式分区只读检测与修复（以mongodb为例）。
- extX格式分区只读检测。
执行如下命令，检查分区是否只读。
cat /proc/mounts | grep extend_vg
/dev/mapper/extend_vg-ceilometer--data /var/ceilometer ext4 ro,relatime,data=ordered 0 0
ext4分区处于“ro”状态，则该分区处于只读状态，如非只读状态则无需执行修复操作。在本例中，mongodb分区为/dev/mapper/extend_vg-ceilometer--data，挂载路径为/var/ceilometer。
- extX格式分区只读修复，如存在命令执行失败，请联系技术支持工程师协助解决。
- 执行如下命令，停止mongodb服务。
cps host-template-instance-operate --service mongodb mongodb --action stop --host 主机ID
主机ID可以从告警的附加信息上获取。
- 执行如下命令，去挂载分区，如去挂载成功则无回显。
umount /var/ceilometer
- 执行如下命令，修复分区。
fsck.ext4 /dev/mapper/extend_vg-ceilometer--data
Pass 5: Checking group summary information
如果前面只读检测分区的文件系统格式是ext3，请使用ext3相关的修复命令：
fsck.ext3 /dev/mapper/extend_vg-ceilometer--data
- 执行如下命令，挂载分区。
mount /dev/mapper/extend_vg-ceilometer--data /var/ceilometer
- 执行如下命令，启动mongodb服务。
cps host-template-instance-operate --service mongodb mongodb --action start --host 主机ID
主机ID可以从告警的附加信息上获取。
18. 在FusionSphere Openstack安装部署界面，查看mongodb与其他组件是否已恢复正常运行，如图所示。
- 是，手动清除告警并结束。
- 否，执行19。
19. 对故障主机重做操作系统并重建虚拟机。系统恢复后在/etc/fstab中默认加载磁盘的相关告警会自动清除；其他磁盘告警请重启，并执行20。如无法修复，请联系技术支持工程师协助解决。
20. 手动确认文件系统是否正常。
- 执行df -h，查看是否可以找到告警信息中故障磁盘（比如“/dev/sda1”）的挂载点（比如“/boot/efi”）。
- 是，执行20.c。
- 否，执行20.b。
- 执行ll /dev/mapper，找到对应磁盘的映射（比如“cpsVG-image”），再执行df -h查找挂载点（比如“/opt/HUAWEI/image”）。
- 执行touch /挂载点/测试文件名，对挂载点执行写文件操作，比如touch /opt/HUAWEI/image/test，其中test是新增测试的文件名。
- 命令执行正常，说明文件系统恢复正常，需要手动清除告警，并执行命令rm /挂载点/测试文件名，删除上一步创建的文件，比如rm /opt/HUAWEI/image/test，其中test是20.c中的测试写文件操作的新增文件名，并执行21。
- 命令执行失败，说明执行失败故障没有恢复，继续执行19，如仍然无法修复，请联系技术支持工程师协助解决。
21. 重启主机，观察告警是否重现。
- 是，可能是罕见文件系统故障，需要专业分析，请联系技术支持工程师协助解决。
- 否，处理完毕。
##### 参考信息
无。