# 5.2.15.4 ALM-2000456 memcached进程不存在

##### 告警解释
SMN服务使用memcached提供缓存服务，当部署memcached的节点检测到memcached进程不存在时，系统产生此告警。
此告警提示系统故障或者风险，告警处理完毕后，会自动恢复。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000456 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
节点无法正常提供服务，需尽快处理。
##### 可能原因
- 系统crontab任务异常没有拉起。
- Memcached配置文件错误无法拉起该进程。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：hermes，默认密码：*****。
6. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
7. 执行以下命令切换到root用户。
sudo su - root
默认密码：*****
8. 执行以下命令，检查crontab是否被注销。
cat /etc/crontab | grep memcached
[root@SMN-PS-NS-DB-MEM01 ~]# cat /etc/crontab |grep memcached
*/1 * * * * root su onframework -c "/opt/onframework/memcached/bin/memcached_monitor.sh check"
当回显信息最前面有“#”，表示crontab被注销。
- 是，请删除“#”取消注销。
- 否，执行9。
9. 切换到onframework用户下，手动执行重启进程命令。
su - onframework
sh /opt/onframework/memcached/bin/memcached_monitor.sh restart
10. 重启完成后执行以下命令检查memcached进程运行是否正常。
ps -ef | grep "/memcached" |grep -v grep
- 存在回显，执行11。
- 否，请联系技术支持工程师协助解决。
11. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。