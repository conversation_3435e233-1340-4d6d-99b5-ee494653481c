# 5.2.3.1.79 ALM-73208 对接外部仲裁服务异常

##### 告警解释
当管理面双活配置中的仲裁服务异常时，发出告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73208 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 本端地址：告警发送节点的地址。<br>对端地址：产生告警的主机地址。<br>错误信息：告警相关的错误信息。 |
##### 对系统的影响
仲裁服务不可用，本站点配对的zookeeper会自动停止服务。
如果还有其他节点的zookeeper服务故障，会导致zookeeper断链，继而引发整体服务不可用。
##### 可能原因
- 仲裁服务参数有误。附加信息为“params_error”。
- 仲裁服务有一半（含）以上不可连通。附加信息为失效的服务器IP地址。
##### 处理步骤
1. 检查仲裁服务器的参数配置是否有误。
- 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令，防止系统超时退出。
TMOUT=0
- 执行以下命令，导入环境变量。具体操作请参见导入环境变量，使用CPS鉴权方式。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
- 执行以下命令，获取仲裁服务器对应的规则组名；
cps hostcfg-list --type site
回显如下类似信息：
056919C0-3730-11E5-807B-027C1B9697E0:/home/<USER>
+------+---------+---------------------------------------------+------------------+
| type | name    | hosts                                       | metadata           |
+------+---------+---------------------------------------------+------------------+
| site | default | hostid:                                     |                    |
|      |         |                                             |                    |
| site | dc001   | hostid:056919C0-3730-11E5-807B-027C1B9697E0 | grouptype:compute, |
|      |         |                                             | cpufamily:x86_64   |
|      |         |                                             |                    |
| site | dc002   | hostid:CCD8C060-360D-11E5-9941-167D1B9697E0 | grouptype:compute, |
|      |         |                                             | cpufamily:x86_64   |
|      |         |                                             |                    |
+------+---------+---------------------------------------------+------------------+
- 执行以下命令，获取仲裁服务器配置的参数，检查该参数是否与实际规划的参数一致。
cps hostcfg-show --type site site_rule_name
site_rule_name为1.e中获取的规则组名。
- 是，执行3。
- 否，重新配置FusionSphere OpenStack对接仲裁服务。
2. 先检查是否存在如下告警，如果有，请先排查如下告警：
- ALM-2000266 节点系统时钟跳变超过一分钟
- ALM-2001106 Etcd服务状态异常
- ALM-2002101 Monitor进程异常
- ALM-2002302 Monitor与对端站点通信异常
- ALM-2002501 站点网络状态异常
- ALM-2001107 第三方站点异常
3. 检查仲裁服务是否失效，执行以下操作。
- 登录仲裁服务虚拟机，检查仲裁服务是否正常。
- 根据 1.fFusionSphere OpenStack对接仲裁服务ip，登录云平台仲裁服务节点。
使用PuTTY工具，以“arbiter”用户登录节点。
执行如下命令，并按提示输入“root”用户的密码，切换到“root”用户。
sudo su - root
执行以下命令，防止PuTTY超时退出。
TMOUT=0
- 检查仲裁服务是否正常。
执行以下命令，检查仲裁服务是否正常。
systemctl status arbitration-etcd
回显如下，仲裁服务正常运行，若非正常，执行5。
[arbiter@ASVM01 ~]$ systemctl status arbitration-etcd
arbitration-etcd.service - ArbitrationService etcd daemon
Loaded: loaded (/usr/lib/systemd/system/arbitration-etcd.service; enabled)
Active: active (running) since Fri 2018-04-13 21:47:40 CST; 3 days ago
Process: 6791 ExecStop=/bin/sh /opt/arbitration-etcd/script/service.sh stop (code=exited, status=0/SUCCESS)
Process: 6850 ExecStart=/bin/sh /opt/arbitration-etcd/script/service.sh start (code=exited, status=0/SUCCESS)
Main PID: 6985 (etcd)
CGroup: /system.slice/arbitration-etcd.service
6985 /opt/arbitration-etcd/foss/2.2.4/etcd -name arbitration-etcd1 -data-dir /opt/arbitration-etcd/arbitration-etcd1 -cert-file=/opt/arbitration-etcd/keystore/server.crt -key-file=/opt/arbitr...
- 登录OpenStack首节点，检查告警主机与仲裁服务虚拟机的连通情况。
- 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令，防止系统超时退出。
TMOUT=0
- 执行以下命令，导入环境变量。具体操作请参见导入环境变量，使用CPS鉴权方式。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
- 执行以下命令，检查告警主机与仲裁服务虚拟机的连通情况。
cps-etcdCheck
回显如下，即表示情况正常。若非正常，执行5。
slot4:/opt/arbitration-etcd/foss/2.2.4 # cps-etcdCheck
============   read etcd configuer   ============
total etcd servers is 1
ip is: **********
============ 1. checking etcd health ============
result: healthy etcd server num is 1
check healthy passed!
============2. checking read value ============
result: read from etcd succ!
dc001 = ok
dc002 = ok
all status = ok	read value passed!
====================================
all tests passed!
4. 查看告警是否恢复。
- 是，告警处理结束。
- 否，执行5。
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。