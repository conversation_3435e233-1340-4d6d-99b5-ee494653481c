# 5.2.11.2.3 1020791 云服务器备份策略自动调度失败

##### 告警解释
云服务器备份策略自动调度失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020791 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 策略ID | 调度失败的策略ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
| 策略名称 | 调度失败的策略名称。 |
##### 对系统的影响
备份未按照设定好的策略自动生成，影响后续的恢复操作。
##### 可能原因
- 组件状态异常。
- 对接nova的连接异常，无法获取虚拟机信息。
- 对接cinder的连接异常，无法获取卷信息。
- 备份配额不足。
##### 处理步骤
- 可能原因：组件状态异常。
- 检查是否存在告警“组件状态异常”。
- 是，根据告警修复建议修复组件异常。
- 否，请执行2。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应的策略手动执行备份。
- 可能原因：对接nova的连接异常，无法获取虚拟机信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Nova”行对应的“Check_Result”值。
- 如果为“OK”，执行3。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行2.c。
- 如果为“Error”，执行set_karbor_endpoints --nova_endpoint nova的url地址命令设置nova的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索DMK_g_regions:fsp_Cascading.nova获取nova的url地址。再次执行2.c。
- 如果仍未修复与nova的连接异常，请联系技术支持工程师协助解决。
- 可能原因：对接cinder的连接异常，无法获取卷信息。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“Cinder”行对应的“Check_Result”值。
- 如果为“OK”，执行4。
- 如果为“SSLError”，请参见通过ManageOne界面方式单个或批量更换证书更新FusionSphere部件的“FusionSphere-API”证书。再次执行3.c。
- 如果提示endpoint错误，执行set_karbor_endpoints --cinder_endpoint cinder的url地址命令设置cinder的endpoint，可在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索DMK_g_regions:fsp_Cascading.cinder获取cinder的url地址。再次执行3.c。
- 如果仍未修复与cinder的连接异常，请联系技术支持工程师协助解决。
- 可能原因：备份配额不足。
- 查看附加信息中“错误码”值是否为CSBS.9006。
- 是，联系附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，申请备份空间。申请空间的详细操作请参见申请备份空间。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。