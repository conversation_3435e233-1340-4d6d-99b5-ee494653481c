# 5.2.4.5.1 ALM-157 当前告警数量达到阈值

##### 告警解释
当前告警数量达到“集中告警 > 告警设置”中“当前告警阈值提示”界面中设定的产生阈值（缺省为85%）时，产生此告警。
当前告警数量低于设定的恢复阈值（缺省为85%）时，则告警自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 157 | 重要 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警阈值 | 用户在配置“当前告警阈值提示”中，设置的该告警的产生阈值。<br>该参数显示在“定位信息”中。 |
| 当前占用量 | 当前告警的数量与总容量的占比。<br>该参数显示在“定位信息”中。 |
##### 对系统的影响
当产生该告警的时候，若不及时确认或清除不关注的告警，可能会导致用户关注的告警被系统转为历史告警。
##### 可能原因
- 该告警的产生阈值设置过低。
- 当前告警中积压太多已清除未确认的告警。
- 上报大量告警。
##### 处理步骤
1. 在主菜单中选择“集中告警 > 告警设置”。
2. 在左侧导航树中选择“当前告警阈值提示”，查看告警产生阈值是否设置过低，建议使用缺省阈值。
- 是：调整告警的产生阈值。执行3。
- 否：执行4。
3. 1分钟后检查“当前告警数量达到阈值”告警是否清除。
- 是：处理完毕。
- 否：执行4。
4. 在主菜单中选择“集中告警 > 当前告警”。根据已清除状态、级别等条件进行过滤，手工确认不关注的已清除告警。
如果未确认已清除的告警数量过多，检查是否设置了自动确认规则，如果没有，建议设置自动确认规则，以便对已清除的告警进行自动确认。自动确认规则查看与设置入口：在主菜单中选择“集中告警 > 告警设置”。在左侧导航树中选择“自动确认规则”。
5. 1分钟后检查“当前告警数量达到阈值”告警是否清除。
- 是：处理完毕。
- 否：执行6。
6. 请联系华为技术支持工程师。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。