# 5.2.3.1.61 ALM-73015 大页内存不足

##### 告警解释
collect-alarm会周期性（默认为300s）检查当前生效的大页内存和用户配置的期望大页内存值的差异，当生效的大页内存值小于用户配置的期望值，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73015 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 异常信息：大页内存不足。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |
##### 对系统的影响
- 可能导致部分业务虚拟机无法正常启动。
- 可能导致无法创建新的大页虚拟机。
##### 可能原因
内存故障，主机的总内存减少。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 进入“配置 > 内核参数”，观察所有主机组的“配置状态”中是否存在“配置失败”。
- 是，执行3。
- 否，执行6。
3. 单击“大页内存页数”的配置框，会弹出大页内存数量可配置的范围，比较可配置的最大值是否小于已配置的大页内存页数。
- 是，执行4。
- 否，执行6。
4. 勾选“启用自动大页内存”，或者将“大页内存页数”配置为FusionSphere OpenStack安装部署界面上提示的可配最大值。配置之后，单击提交。提交后，如果“配置状态”中显示“重启后生效”，请按提示重启对应的主机。重复执行3-4，检查所有“配置失败”的主机组。
5. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“BMC IP地址”一栏获得主机的BMC地址。
6. 登录BMC，观察“告警与事件”中“当前告警”页面中是否存在检查主机硬件内存相关故障告警。
- 是，执行7。
- 否，执行10。
7. 主机下电，参照更换内存更换新内存，再启动主机。
8. 登录主机，确认正常重启成功。
先用帐号“fsp”登录，然后使用“su - root”切换到帐号“root”。
帐号“fsp”的初始密码为“*****”。
帐号“root”的初始密码为“*****”。
9. 等待5分钟~10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。