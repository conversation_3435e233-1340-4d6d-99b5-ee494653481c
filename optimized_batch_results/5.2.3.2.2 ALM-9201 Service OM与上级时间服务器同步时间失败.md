# 5.2.3.2.2 ALM-9201 Service OM与上级时间服务器同步时间失败

##### 告警解释
时间管理模块按设置的同步周期检测上级时间服务器状态，当持续30分钟检测到上级时间服务器不可用时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9201 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM的浮动IP地址<br>对端地址：上级时间服务器的地址 |
##### 对系统的影响
此告警产生时，Service OM节点不能正常的将上级时间服务器作为时间服务器，从而难以获得精准的时间。
##### 可能原因
- Service OM节点与上级时间服务器时间差异大于1分钟。
- Service OM节点与上级时间服务器网络连接异常。
- 上级时间服务器时间同步服务异常。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“系统 > 系统管理 > 时间”。
3. 查看时间服务器的IP，即为上级时间服务器的IP。
4. 使用“PuTTY”，登录Service OM节点。
用“galaxmanager”用户，以Service OM节点的管理浮动IP登录。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
5. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
6. 执行以下命令，查看通信状况。
ping 上级时间服务器的IP，如ping ***********
返回信息如下所示，则通信正常。
ping *********** (***********) 56(84) bytes of data.
64 bytes from ***********: icmp_seq=1 ttl=64 time=0.035 ms
64 bytes from ***********: icmp_seq=2 ttl=64 time=0.028 ms
64 bytes from ***********: icmp_seq=3 ttl=64 time=0.025 ms
7. 通信是否正常。
- 是，执行8。
- 否，检查网络连接，确保网络通畅。
8. 执行以下命令，查看上级时间服务器时间同步服务是否可用。
ntpdate -d 上级时间服务器的IP，如ntpdate -d ***********
返回信息如下所示，则服务正常。
allinonefm:/opt # ntpdate -d ***********
6 Sep 12:13:23 ntpdate[14344]: ntpdate 4.2.8p9@1.3265-o Tue Nov 29 09:18:51 UTC 2016 (1)
Looking for host *********** and service ntp
host found : ***********
transmit(***********)
receive(***********)
transmit(***********)
receive(***********)
transmit(***********)
receive(***********)
transmit(***********)
receive(***********)
server ***********, port 123
stratum 11, precision -24, leap 00, trust 000
refid [***********], delay 0.02594, dispersion 0.00002
transmitted 4, in filter 4
reference time:    dd59f464.9d65b276  Wed, Sep  6 2017 12:13:24.614
originate timestamp: dd59f469.8bc9aecc  Wed, Sep  6 2017 12:13:29.546
transmit timestamp:  dd59f469.8bb6a04c  Wed, Sep  6 2017 12:13:29.545
filter delay:  0.02594  0.02594  0.02600  0.02603
0.00000  0.00000  0.00000  0.00000
filter offset: 0.000017 0.000002 -0.00004 -0.00003
0.000000 0.000000 0.000000 0.000000
delay 0.02594, dispersion 0.00002
offset 0.000017
6 Sep 12:13:29 ntpdate[14344]: adjust time server *********** offset 0.000017 sec
9. 上级时间服务器时间同步服务是否可用。
- 是，执行11。
- 否，执行10。
10. 修复上级时间服务器，确保同步服务正常。
11. 查看上级时间服务器的时间和Service OM节点的时间差是否小于一分钟。
- 是，执行17。
- 否，执行12。
12. 在“系统”页面单击“系统管理”，进入“时间”页面。
13. 在“时间”界面，单击“强制时间同步”。
强制时间同步过程中，会重启系统服务，耗时约20分钟。
14. 等待大约20分钟后，重新登录Service OM，进入“时间”页面，查看强制时间同步是否成功。
- 是，执行15。
- 否，执行18。
15. 查看告警是否清除。
- 是，处理完毕。
- 否，执行16。
16. 按照系统时间错误处理系统时间错误。
17. 等待10个～15个时间同步间隔，查看告警是否自动清除。
- 是，处理完毕。
- 否，执行18。
18. 请联系技术支持工程师协助解决。
##### 参考信息
无