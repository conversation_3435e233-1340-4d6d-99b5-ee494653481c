# ********.9 ALM-48320-证书即将过期 未知章节

##### 告警解释
API网关某些组件使用的证书即将在10天内过期。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48320 | 重要 | 处理错误告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Certificate_Information | 证书的名称以及到期时间 |
##### 对系统的影响
证书过期后将存在安全风险，且某些服务证书过期可能拒绝连接，影响这些服务的业务功能。
##### 可能原因
当前证书的剩余使用天数少于10天。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Certificate_Information：组件的证书名称和证书到期时间。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”章节，替换APIG证书，以免影响相关业务的使用。
3. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 4
4. 保存告警相关信息，并联系技术支持。
- 在告警列表中，勾选当前处理的告警记录，导出并保存告警信息压缩包。
- 保存上述步骤中创建的API网关证书，并联系技术支持。
##### 告警清除
系统每24小时检查一次证书信息，此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
< 上一节