# ********.4 ALM-2000909 haproxy浮动IP端口不可达

##### 告警解释
当执行命令wget VIP:port检测不通时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000909 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
该浮动IP对应的服务对接HAProxy配置不成功，无法通过HAProxy正常转发消息。
##### 可能原因
- 该浮动IP与HAProxy节点的IP不在同一个网段。
- 该浮动IP及端口只在HAProxy的一个节点进行配置，在HAProxy的另一个节点未配置。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，登录4中确认的虚拟机。
默认帐号：ulb，默认密码：*****。
6. 执行以下命令，防止PuTTY超时退出。
TMOUT=0
7. 执行以下命令切换到root用户。
sudo su - root
默认帐号：root，默认密码：*****。
8. 执行以下命令，检查浮动IP是否绑定成功。
ip a
- 是，执行9。
- 否，重新配置浮动IP。
9. 检查浮动IP是否与HAProxy节点在同一个网段。
- 是，执行10。
- 否，重新分配浮动IP后执行10。
10. 分别在HAProxy两个节点执行以下命令，检查是否都配置了该浮动IP。
cat /home/<USER>/keepalived/etc/keepalived/keepalived.conf
! Configuration File for keepalived
vrrp_script chk_http_port {
script "/home/<USER>/keepalived/script/check_haproxy.sh"interval 2weight 20
}
vrrp_instance VI_1 {
state BACKUP
nopreempt
interface eth0
virtual_router_id 162
priority 100
advert_int 1
virtual_ipaddress {
IP地址
……
}
track_script {
chk_http_port
}
notify_master "/home/<USER>/keepalived/script/notify.sh master"
notify_backup "/home/<USER>/keepalived/script/notify.sh backup"
}
- 是，执行12。
- 否，执行11。
11. 在keepalived.conf文件配置修改浮动IP。
12. 执行以下命令，检查浮动IP是否与其他IP冲突。
arping 浮动IP
如果出现两个MAC地址，则表示浮动IP冲突。
- 是，重新分配浮动IP后执行13。
- 否，执行13。
13. 执行以下命令，检查haproxy.conf和keepalived.conf配置文件中对应服务的浮动IP和端口是否一致。
cat /etc/haproxy/haproxy.cfg
cat /home/<USER>/keepalived/etc/keepalived/keepalived.conf
- 是，请联系技术支持工程师协助解决。
- 否，执行14。
14. 执行以下命令，编辑haproxy.conf文件，找到对应的服务，修改为规划后的IP端口。
vim /etc/haproxy/haproxy.cfg
listen 服务名称 浮动IP:端口号
15. 执行以下命令，重启进程后观察是否仍有告警。
service keepalived restart
service haproxy restart
- 是，请联系技术支持工程师协助解决。
- 否，处理完毕。
##### 参考信息
无。
< 上一节