# ********.22 1023277 消息队列卡死

##### 告警解释
部分或全部消息队列卡死，服务无法正常生产和消费消息。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023277 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
可能导致依赖rabbitmq的服务业务异常。
##### 可能原因
rabbitmq长时间运行有概率导致卡死。
##### 处理步骤
- 可能原因：rabbitmq长时间运行有概率导致卡死。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行命令：stop_service --service rabbitmq停止所有节点rabbitmq。
- 执行命令：start_service --service rabbitmq启动所有节点rabbitmq。操作完成后，如果告警未清除，请执行1.e。
- 请收集日志目录“/var/log/huawei/dj/services/system/rabbitmq”，然后请联系技术支持工程师协助解决。
##### 参考信息
无。