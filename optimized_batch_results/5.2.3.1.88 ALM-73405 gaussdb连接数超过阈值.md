# 5.2.3.1.88 ALM-73405 gaussdb连接数超过阈值

##### 告警解释
gaussdb服务端连接数是有限的，如果组件连接数据库达到最大连接数的90%，会产生此告警；当连接数据库的连接数降低到最大连接数的85%，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73405 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。 |
| 附加信息 | 详细信息：告警的详细信息。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>阈值：连接数的使用阈值。 |
##### 对系统的影响
此告警产生时，gaussdb连接数接近最大连接数，而未到达上限，则对系统无影响。如果连接数继续增加，达到上限，则组件无法申请到新连接访问数据库。
##### 可能原因
- 业务下发超过规格。
- 连接数设置不满足规格要求。
##### 处理步骤
1. 确认是否正在进行大量业务操作。
- 是，执行2。
- 否，执行3。
2. 待业务量减少后，等待1分钟~3分钟，查看告警台，确认告警恢复。
- 告警恢复，执行3。
- 告警不恢复，执行11。
3. 业务量增加的情况下，是否频繁出现连接数达到阈值告警。
- 否，完毕退出。
- 是，执行4，请适度调整gaussdb最大连接数大小满足业务需求。
4. 从告警附加信息中，获取上报的告警阈值、当前连接数、总连接数、当前连接数的使用率。从告警定位信息“服务”中，获取产生告警的组件名。
5. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
6. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
7. 执行以下命令，防止系统超时退出。
TMOUT=0
8. 导入环境变量，具体操作请参见导入环境变量。
9. 执行以下命令，获取产生告警的服务名。
cps template-list | grep 组件名
564D7942-B752-7955-C0B0-B762D66E841D:/etc/keystone/domains # cps template-list | grep gaussdb
| gaussdb_keystone    | gaussdb_keystone              | DataBase for Keystone service.                     |
| gaussdb_cinder      | gaussdb_cinder                | DataBase for Cinder service.                       |
| gaussdb_nova        | gaussdb_nova                  | DataBase for Nova service.                         |
| gaussdb_neutron     | gaussdb_neutron               | DataBase for Neutron service.                      |
| gaussdb             | gaussdb                       | DataBase for OpenStack service.                    |
如上方回显为例，第一列为服务名，第二列为组件名，根据组件名获取对应的服务名。
10. 执行以下命令，更改数据库配置项。
cps template-params-update --service 服务名 组件名 --parameter database_max_connections=number;
然后执行：cps commit
number表示openstack组件连接gaussdb的总连接数，取值范围为800~5000。请根据4中获取的连接数相关信息，适当增大总连接数的数值，使“当前连接数 / 总连接数 < 阈值*（100% - 偏移量）”，偏移量默认为5%。
执行结果：
- 修改成功，完毕退出。
- 修改失败，执行11。
11. 请联系技术支持工程师协助解决。
##### 参考信息
无。