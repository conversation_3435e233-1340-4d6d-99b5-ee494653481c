# 5.2.4.11.1 ALM-servicemonitor_cpu.percent CPU使用率阈值告警

5.2.4.11.1.3 ALM-servicemonitor_cpu.percent CPU使用率阈值告警
##### 告警解释
当被监控对象的CPU使用率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_cpu.percent | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
被监控对象的CPU使用率过高会导致系统吞吐量下降，系统响应延迟增加。
##### 可能原因
被监控系统的CPU使用率过高。
##### 处理步骤
1. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
2. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
3. 查看CPU使用率，如果进程CPU使用率大于85%，执行4，否则联系技术支持协助处理。
top -c
键盘输入shift + p，按CPU使用率降序排序。
4. 请联系技术支持工程师协助解决。
##### 参考信息
无。