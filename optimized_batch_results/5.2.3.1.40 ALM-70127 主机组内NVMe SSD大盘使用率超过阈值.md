# 5.2.3.1.40 ALM-70127 主机组内NVMe SSD大盘使用率超过阈值

##### 告警解释
OpenStack周期性（默认5分钟）检测主机组内NVMe SSD大盘使用率，当检测到NVMe SSD大盘占用个数达到主机组内所有NVMe SSD大盘总个数的85%时，系统产生此告警。
当检测到NVMe SSD大盘占用率降低到70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70127 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：NVMe SSD大盘所在主机组ID<br>SSD卡类型：NVMe SSD大盘类型 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>总量：NVMe SSD大盘总量<br>使用量：NVMe SSD大盘使用量<br>阈值：NVMe SSD大盘阈值 |
##### 对系统的影响
可能会造成之后创建NVMe SSD大盘虚拟机时，由于NVMe SSD大盘资源不够而创建失败。
##### 可能原因
主机组内NVMe SSD大盘使用率过高。
##### 处理步骤
1. 在服务器上新增与原有NVMe SSD大盘类型相同的NVMe SSD盘或NVMe SSD卡进行扩容，或者释放掉多余的占用大盘的虚拟机以释放大盘资源。
参考用户指南中对应Region Type的“超高I/O型云服务器配置方案”章节，进行NVMe SSD配置。
2. 当释放大盘资源或者扩容完毕后，NVMe SSD大盘使用率低于70%时，告警是否恢复。
- 是，处理完成。
- 否，执行3。
3. 如果上述操作出现问题，请联系技术支持工程师协助解决。
##### 参考信息
无。