# *******.84 ALM-73303 VRM服务器连接失败

##### 告警解释
FusionSphere OpenStack无法成功连接FusionCompute的VRM节点。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73303 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>对端地址：VRM服务器的IP地址 |
| 附加信息 | 详细信息：Failed to connect to VMware vCenter Server，表示连接VRM服务器失败 |
##### 对系统的影响
FusionSphere OpenStack节点无法成功对接FusionCompute的管理节点VRM。
##### 可能原因
- 外部交换机转发丢包或延时过大。
- 主机网卡或交换机带宽不足。
- 网卡故障。
- VRM管理节点不能正常工作。
- VRM帐号密码错误。
- FusionSphere OpenStack与VRM之间网络配置错误。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行以下命令查看fc-nova-compute服务所在节点，使用PuTTY，通过回显中omip登录节点。
cps template-instance-list --service nova fc-nova-compute
9. 通过ping命令查看是否与VRM的IP互通。
- 是，执行10。
- 否，联系实验室管理员，检查物理网络是否连接和配置正确。确认配置正确后，执行10。
10. 执行cps template-instance-list --service apacheproxy apacheproxy，在回显中找到状态为active的apacheproxy服务所在节点的OMIP，并通过该IP登录节点。然后通过ping VRM_IP -I APACHEPROXY_IP命令检查正向代理和VRM的IP是否互通, VRM_IP为VRM管理IP，APACHEPROXY_IP为安装部署界面配置正向代理的external_api平面的IP。
- 是，执行11。
- 否，联系实验室管理员，检查物理网络是否连接和配置正确。确认配置正确后，执行13。
11. 在FusionSphere OpenStack安装部署界面，选择“配置 > 资源池管理”。
12. 在“资源池”区域单击HUAWEI资源池下的，查看配置的FusionCompute对接参数是否正确。
13. 设置参数正确后，单击“提交”按钮。等待几分钟后，通过FusionSphere OpenStack运维管理界面的告警页面查看告警是否被清除。
14. 如果告警仍旧未清除，请联系技术支持工程师协助解决。
##### 参考信息
无。