# 5.2.3.1.38 ALM-70113 NVME SSD盘或卡故障

##### 告警解释
OpenStack周期检查主机上NVME SSD盘或卡的健康状态，当检查到盘或卡的状态不健康时，系统就产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70113 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>pci地址：产生告警的SSD卡的pci地址<br>异常类型：告警的异常类型 |
| 附加信息 | 主机ID：告警虚拟机所在主机ID<br>本地NvmeSSD盘：告警虚拟机使用的NvmeSSD盘<br>虚拟机id：产生告警的虚拟机ID |
##### 对系统的影响
会影响当前盘或卡上虚拟机的业务，如果进行更换故障盘或卡，则影响当前主机上所有虚拟机的业务。
##### 可能原因
NVME SSD盘或卡硬件故障。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 打开告警详情查看主机ID和PCI地址。
3. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 查询NVME SSD盘或卡对应的系统槽位。
- 执行命令cps host-list | grep 主机ID，获取主机的管理面IP。
- 执行命令su fsp，切换为fsp用户。
- 执行命令ssh fsp@管理面ip，切换至管理面IP。
- 重复执行4~5。
- 执行命令 ls /sys/bus/pci/slots/，获取所有的系统槽位号。
- 重复执行如下命令，直到回显信息与2中获取的PCI地址一致。此时$slot的取值，就是NVME SSD盘或卡对应的系统槽位。
cat /sys/bus/pci/slots/$slot/address
- 命令中$slot为系统槽位号，取值依次从6.e获取。
- 理论上，6.e中获取的系统槽位号和服务器表面的槽位号是一致的。
7. 通知该主机上所有租户关闭虚拟机进行更换硬盘操作，故障虚拟机数据丢失，其他虚拟机数据不丢失。
8. 待所有虚拟机都关闭后，执行如下命令安全移除盘或卡。
echo 0 > /sys/bus/pci/slots/$slot/power
9. 观察NVMe PCIe 固态硬盘指示灯，如果NVMe PCIe 固态硬盘Fault指示灯处于慢闪状态（0.5Hz/s），表示该硬盘允许拔出。
10. 下电主机，更换同等规格（包括磁盘规格、磁盘类型、安装槽位等）的盘或卡，具体请参考虚拟机NVMe SSD直通盘更换章节。
11. 在Service OM界面清除告警。
12. 上电主机，待所有虚拟机启动完毕，通知所有租户虚拟机可用。
##### 参考信息
无。