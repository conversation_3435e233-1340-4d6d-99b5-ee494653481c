# ********.88 0x10E01A0014 卸载FusionStorage卷失败

##### 告警解释
Proxy（管理平面IP address：[IP_addr]）中，卸载卷(卷名：[Vol_name]，FusionStorageManager：[DSWare_IP])失败，显示错误信息（[errMsg]）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0014 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | 备份代理管理平面地址。 |
| DSWare_IP | FusionStorageManager地址。 |
| Vol_name | 卷名称。 |
| errMsg | 错误信息。 |
##### 对系统的影响
造成备份或恢复任务失败。
##### 可能原因
Proxy与受保护环境所在服务器间物理连接中断。
##### 处理步骤
- 可能原因1：Proxy与受保护环境所在服务器间连接中断。
- 在当前告警界面查看ID为0x10E0140000（连接受保护环境失败）或0x10E0140001（扫描受保护环境失败）的告警是否上报。
- 是，请先处理1.a所述告警，保证Proxy与生产端网络连接正常。
- 否，执行1.b。
- 根据告警中的卷信息执行命令“/usr/bin/vbs_cli -c detachwithip -v VolumeName -i DSWareIP -p 0”手动卸载卷，其中VolumeName为告警详细信息中的卷名称，DSWareIP为FusionStorageManager的IP。查看回显是否为“ret_code=0”。
- 是，在告警界面手动清除告警。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。