# 5.2.3.1.46 ALM-70135 内存复用率超过阈值告警

##### 告警解释
主机开启内存复用后，系统周期性（默认为5分钟）检查内存复用率，如果主机内存复用率超过默认的告警阈值（默认为120%），则会上报此告警。
主机内存复用率计算方法如下：
主机内存复用率=（已使用内存 - 虚拟机预留内存 - 已使用大页内存）/（主机物理内存 - I层预留内存 - 大页内存）
其中，虚拟机预留内存 = 虚拟机规格 * 内存预留率。
内存预留率可以在FusionSphere OpenStack安装部署界面进行配置调整，默认为20%。
直通类型虚拟机（GPU直通、SSD直通、SRIOV直通、PF直通、FPGA直通、NVDIMM直通）和大页内存虚拟机不支持内存复用，内存预留率为100%。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70135 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机 ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>复用内存总量：主机的复用内存总量<br>复用内存已使用量：主机的复用内存已使用量<br>内存复用率：主机的内存复用率<br>阈值：主机的内存复用告警阈值 |
##### 对系统的影响
可能会造成主机上运行的虚拟机的性能下降。
##### 可能原因
主机上运行的虚拟机个数过多。
##### 处理步骤
1. 将该主机上的部分虚拟机迁移到其他主机上，降低该主机内存复用率。
具体操作请参考迁移虚拟机。
2. 等待约5分钟后，查看告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。