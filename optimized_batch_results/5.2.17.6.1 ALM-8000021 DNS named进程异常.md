# ********.1 ALM-8000021 DNS named进程异常

##### 告警解释
系统每隔60秒检查一次DNS服务的named进程，如果进程异常则产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 8000021 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
产生告警的节点DNS服务异常，无法提供服务，需尽快处理异常。
##### 可能原因
named进程不存在或异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的告警节点管理IP地址登录产生告警的节点。
默认帐户：udns，默认密码：*****。
6. 检查是否可以登录成功。
- 是，执行7。
- 否，请联系技术支持工程师协助解决。
7. 执行以下命令，切换到root用户，默认密码：*****。
sudo su - root
8. 执行以下命令，查看named服务是否正常。
service named status
- 回显中包含“active (running)”，请联系技术支持工程师协助解决。
- 回显中不包含“active (running)”，执行9。
9. 执行以下命令，重启服务。
service named restart
10. 等待2分钟，执行以下命令，查看named服务是否正常。
service named status
- 回显中包含“active (running)”，执行11。
- 回显中不包含“active (running)”，请联系技术支持工程师协助解决。
11. 等待10分钟，检查告警是否清除。
- 是，处理结束。
- 否，请收集上述告警处理过程中的信息，请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。