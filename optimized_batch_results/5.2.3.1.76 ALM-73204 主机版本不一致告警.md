# 5.2.3.1.76 ALM-73204 主机版本不一致告警

##### 告警解释
OpenStack周期（默认为300s）检查每台主机版本状态，如果该主机版本与系统版本不一致，产生此告警。
主机版本不一致包括主机所安装的系统的内部版本号，以及PXE安装主机时为主机设置的系统时间戳。
因此，即使两套系统使用同一个内部版本号的系统安装了主机，主机也不能在两套系统之间随意混用。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73204 | 重要 | 不同场景不同，删除节点场景需要手动清除 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 产生告警的主机ID。 |
| 附加信息 | 第一个参数：<br>alarmInfo：告警详情。<br>hostosversion：产生告警的主机的os版本。<br>hosttimetag：产生告警的主机时间戳。<br>sysosversion：产生告警的主机上系统的os版本。<br>systimetag：产生告警的主机上系统的时间戳。<br>第二个参数：产生告警的主机ID。<br>第三个参数：产生告警的主机名。 |
##### 对系统的影响
主机版本不一致可能导致主机故障，进而导致组件异常。
##### 可能原因
- 其他环境主机连入此环境。
- 非首节点的主机在PXE安装未成功时，重启了该主机，并且主机之前安装过其他版本的OpenStack主机操作系统。
- 部分主机处于下电或故障状态时，对系统进行了升级或安装了补丁。
##### 处理步骤
1. 先检查是否其他环境主机连入此环境。
- 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
- 在“概要”页面，查看是否存在“误接入的主机”列表。
- 是，执行2。
- 否，执行3，排查下一个可能原因。
- 删除误接入的主机，具体请参照AZ内误接入主机的处理方法章节。
手动清除告警，处理完毕。
- 排查是否非首节点的主机在PXE安装未成功时，重启了该主机，并且主机之前安装过其他版本的OpenStack主机操作系统。
根据环境规划信息判断该主机是否规划内主机。
- 是，执行4。
- 否，执行5，排查下一个可能原因。
- 重新通过PXE安装该主机即可。主机安装成功后，告警自动清除。
处理完毕。
- 排查是否部分主机处于下电或故障状态时，对系统进行了升级或安装了补丁。
- 是，执行6。
- 否，执行7。
- 按主机状态选择处理方法。
- 主机未故障可继续使用，迁移该主机上的虚拟机到其他主机，具体操作请参见迁移虚拟机。可重新参考手动PXE方式安装主机安装主机。主机安装成功后，告警自动清除。
- 主机故障，具体请参照更换主机。更换成功后，然后手动清除告警。
处理完毕。
- 如果存在其他问题无法处理，请联系技术支持工程师协助解决。
##### 参考信息
无。