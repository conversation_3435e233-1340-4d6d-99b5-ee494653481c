# 5.2.3.1.45 ALM-70132 主机组内存分配率超过阈值

##### 告警解释
OpenStack周期性（默认5分钟）检测主机组内存分配率，当检测到内存已分配数达到主机组内存总数的85%时，系统产生此告警。
当检测到已分配内存数低于主机组总内存的70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70132 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：产生告警的主机组ID |
| 附加信息 | 云服务：产生告警的云服务名称<br>服务：产生告警的服务名称<br>主机组名称：产生告警的主机组名称<br>内存总量：产生告警的主机组内存总量<br>内存使用量：产生告警的主机组内存使用量<br>内存复用比：产生告警的主机组内存复用比<br>内存门限量：产生告警的主机组内存门限量 |
##### 对系统的影响
主机组内存分配率过高，导致该主机组内虚拟机创建失败或者HA失败。
##### 可能原因
主机组总体资源过少、虚拟机个数过多或者存在故障主机。
##### 处理步骤
1. 通过告警详细信息，获取主机组名称、内存总量、内存使用量、内存门限量。
2. 在Service OM界面，单击“资源 > 计算资源 > 主机”，获取告警的主机名称。
3. 通过以下三种方式，增加主机组总内存资源，使得主机组总内存使用率低于70%，告警自动清除：
- 通过删除多余虚拟机方式，进行内存资源清理。
- 参考主机配置管理，增加该主机组中主机，从而增加该主机组中主机的内存资源。
- 参考更换主机，修复主机组中存在的故障主机。
4. 若告警仍未清除，请联系技术支持工程师协助解决。
##### 参考信息
无。