# 5.2.3.2.12 ALM-9217 软件订阅与保障年费已经过期

##### 告警解释
告警模块按24小时周期检测License状态，当检测到当前系统时间较之License软件订阅与保障年费截止期限已经过期时，系统产生此告警。
当主机加载新License，且新License软件订阅与保障年费截止期限大于系统时间时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9217 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 过期时间：License软件订阅与保障年费的过期时间 |
##### 对系统的影响
将无法享受软件订阅与保障年费。
##### 可能原因
现有软件订阅与保障年费已经过期。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“系统 >系统管理 > License”。
3. 记录页面上显示的ESN号码。
4. 准备合同号。
5. 联系技术支持工程师申请License文件。
6. 在“License”页面，单击“上传License”，在弹出的窗口中选择新申请的License文件。
7. 单击“打开”。
8. 加载License是否成功。
- 是，执行9。
- 否，执行10。
9. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，进入“OpenStack告警”，查看告警是否清除。
- 是，处理完毕。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。