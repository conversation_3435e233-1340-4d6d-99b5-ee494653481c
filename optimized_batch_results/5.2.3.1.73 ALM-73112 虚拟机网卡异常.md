# 5.2.3.1.73 ALM-73112 虚拟机网卡异常

##### 告警解释
系统周期性检测正在运行中虚拟机的网卡状态，当检测到虚拟机网卡异常时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73112 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的虚拟机所在主机ID<br>虚拟机网卡：产生告警的虚拟机网卡名称 |
| 附加信息 | 异常信息：产生告警的虚拟机网卡异常信息<br>主机 ID：产生告警的虚拟机所在主机ID<br>主机名：产生告警的虚拟机所在主机名称<br>主机IP：产生告警的虚拟机所在主机IP |
##### 对系统的影响
虚拟机对应异常网卡的网络通信异常。
##### 可能原因
- 误操作导致虚拟机网卡后端设备状态为down。
- 误操作导致虚拟机网卡后端设备被删除。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 执行命令nova list --all-t | grep 虚拟机ID，查看是否有回显。
虚拟机ID：告警附加信息中UUID的取值。
- 是，执行7。
- 否，查看页面告警列表是否存在ALM-70100 虚拟机审计告警。
- 是，参考ALM-70100 虚拟机审计告警处理完成后，执行8。
- 否，执行9。
7. 执行命令nova live-migration 虚拟机ID 主机ID，将该虚拟机迁移到其他主机上。
主机ID： 一个可用的主机ID，此主机的资源需要满足该虚拟机的规格。注意该主机ID并不是告警详情中的主机ID，具体获取方式如下。
- 执行命令cps host-list，获取所有的主机ID。
- 执行命令nova hypervisor-show 主机ID，确认该主机资源是否满足。
8. 等待3分钟~5分钟，查看告警是否自动清除。
- 是，任务结束。
- 否，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。