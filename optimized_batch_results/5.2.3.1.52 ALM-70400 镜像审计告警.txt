# 5.2.3.1.52 ALM-70400 镜像审计告警

##### 告警解释
当执行系统审计时发现存在非正常状态的镜像时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70400 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 云服务：产生告警的云服务<br>详细信息：告警的详细信息 |
##### 对系统的影响
- 此告警产生时，系统中存在不正常状态的镜像。影响系统对镜像的管理。
- queue状态镜像不占用系统资源，但是镜像不可用。
- saving状态镜像会有残留的镜像文件，占用存储系统的空间。
##### 可能原因
系统存在处于中间状态的镜像。
##### 处理步骤
1. 根据告警详情中“附加信息”参数中的“详细信息”取值，并参考表1，获取对应的审计报告名称。
| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| audit_stucking_images | stucking_images.csv |
2. 确定当前环境部署的场景，获取审计报告。
- Region Type I：
- 判断部署的场景是级联层还是被级联层的方法：在OpenStack首节点，执行命令cps productinfo-show，查看product_type的取值，cascading表示级联层，cascaded表示被级联层。
- 如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。
- 级联层：收集审计报告
- KVM虚拟化（被级联层）：收集审计报告
- Region Type II&Region Type III：
- FusionCompute虚拟化：收集审计报告
- KVM虚拟化：收集审计报告
3. 确定当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。
- Region Type I：
- 级联层：审计结果定位
- KVM虚拟化（被级联层）：审计结果定位
- Region Type II&Region Type III：
- FusionCompute虚拟化：审计结果定位
- KVM虚拟化：审计结果定位
4. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。