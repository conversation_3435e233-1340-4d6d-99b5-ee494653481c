# 5.2.3.2.9 ALM-9210 当前License已失效

##### 告警解释
告警模块按24小时周期检测license状态，当检测到当前License已失效时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9210 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 错误原因：License失效的错误原因 |
##### 对系统的影响
License失效后，系统还可用60天。License失效60天后，限制如下功能的使用：
- 用户登录系统后，主机(隔离、解隔离、上电、下电)、主机组（创建、增/删主机、删除、修改、标签管理）、裸金属服务器（初始化）、虚拟机（创建、启动、关闭、重启、强制重启、重置状态、冷迁移、热迁移、修改规格、修改启动方式）、镜像（注册、修改、同步、下载、清除、删除、上传）、规格（创建、删除、复制、标签管理、查看使用情况）、SNMP管理站（添加）的功能将无法继续使用。
- 将无法添加新的KVM服务器到系统中。(默认License不涉及。)
- 已经分配给其他部件的License资源最多60天后将失效。(默认License不涉及。)
- 将无法分配License资源给其他部件。(默认License不涉及。)
##### 可能原因
人为修改了数据库中的License信息或者Service OM所在服务器更换或者Service OM虚拟机重新安装，导致ESN变化。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“系统 >系统管理 > License”。
3. 在“License”页面，单击“上传License”，在弹出的窗口中选择之前申请的License文件。
4. 单击“打开”。
5. 加载License是否成功。
- 是，执行12。
- 否，自动回到“License”页面。
6. 记录页面上显示的ESN号码。
7. 准备合同号。
8. 联系技术支持工程师获取新License文件。
9. 在“License”页面，单击“上传License”，在弹出的窗口中选择新获取的License文件。
10. 单击“打开”。
11. 加载License是否成功。
- 是，执行12。
- 否，执行13。
12. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，进入“OpenStack告警”，查看告警是否清除。
- 是，处理完毕。
- 否，执行13。
13. 请联系技术支持工程师协助解决。
##### 参考信息
无。