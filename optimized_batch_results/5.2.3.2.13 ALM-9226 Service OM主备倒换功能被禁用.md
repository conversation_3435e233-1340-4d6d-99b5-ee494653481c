# *******.13 ALM-9226 Service OM主备倒换功能被禁用

##### 告警解释
Service OM中某些操作会调用命令禁用Service OM的主备倒换功能，此时系统会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9226 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 浮动IP：ServiceOM虚拟机的浮动IP地址 |
##### 对系统的影响
可能影响系统的安全性和稳定性。
##### 可能原因
系统中某些操作需要执行一些命令暂时禁用Service OM的主备倒换功能。
##### 处理步骤
1. 如果当前处于升级过程中，Service OM的主备倒换功能会被禁用。升级结束后等待三十分钟，查看此告警是否被清除。
- 是，处理完毕。
- 否，执行2。
2. 使用PuTTY，通过管理浮动IP地址登录Service OM服务器。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 执行以下命令，启用主备倒换功能：
ha_switch -c MODIFY_DB
6. 等待五分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行7。
7. 请联系技术支持工程师协助解决。
##### 参考信息
无