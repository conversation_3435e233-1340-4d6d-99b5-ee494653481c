# ********.69 0x10E0140000 连接受保护环境失败

##### 告警解释
Server与受保护环境（IP：[Protected_Env_IP]）连接失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E0140000 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Protected_Env_IP | 受保护环境IP地址。 |
##### 对系统的影响
在受保护环境下的受保护对象的备份以及恢复操作将失败。
##### 可能原因
- Server与受保护环境间网络连接中断。
- 受保护环境的登录配置无效。
##### 处理步骤
- 可能原因1：Server与受保护环境间网络连接中断。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏上单击“受保护环境 > VMware”或者“受保护环境 > 存储设备”界面，找到对应的受保护环境并且获取IP地址。
- 参考登录eBackup服务器登录Server。
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 检查Server与受保护环境之间的网路连通性。如果是IPv4，执行“ping 受保护环境IP”，如果是IPv6，执行“ping6 受保护环境IP”，检查是否可ping通。
- 是，执行1.f。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
- 在Server的左侧导航栏中单击对应受保护环境后的扫描符号，重新扫描受保护环境，查看扫描结果是否成功。
- 是，处理结束。
- 否，执行2。
- 可能原因2：受保护环境的登录配置无效。
- 联系受保护环境管理员获取用户名和密码。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 如果受保护环境是“VMware”或者“存储设备”，执行2.d。
- 在导航栏中单击“受保护环境 > VMware”或者“受保护环境 > 存储设备”，单击左侧导航栏的修改符号，编辑该受保护环境，并输入最新的用户名和密码，重新扫描该受保护环境，查看结果是否成功。
- 是，处理结束。
- 否，联系技术支持工程师协助解决。
##### 参考信息
无。