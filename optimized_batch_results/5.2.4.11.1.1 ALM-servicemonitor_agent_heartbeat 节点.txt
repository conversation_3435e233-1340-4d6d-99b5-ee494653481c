# 5.2.4.11.1 ALM-servicemonitor_agent_heartbeat 节点连续中断

5.2.4.11.1.1 ALM-servicemonitor_agent_heartbeat 节点连续中断
##### 告警解释
云服务、ManageOne所在节点部署了MOICAgent服务，用于监控云服务以及ManageOne自身服务、收集云服务日志、备份ManageOne数据库。正常情况下，MOICAgent定时上报心跳消息，当超过30分钟未上报心跳时，上报此告警，表示监控节点通讯异常。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_agent_heartbeat | 次要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 被监控节点IP | 通讯异常的被监控节点的IP地址。 |
| 监控节点IP | MOICAgentMgmtService服务的节点IP地址。 |
##### 对系统的影响
- 在通讯异常节点，无法监控云服务、收集云服务日志、备份ManageOne数据库。
- 若断连节点为netcluster_elb_lvs_vm_x_x，则会造成ELB流量中断10s。
- 若断连节点为netcluster_elb_nginx_vm_x_x，则会造成ELB流量中断15s。
##### 可能原因
- 被监控节点故障。
- 被监控节点和ManageOne的MOICAgentMgmtService服务所在节点之间网络故障或者进程异常。
- 被监控节点和ManageOne的MOICAgentMgmtService服务所在节点之间证书不一致。
##### 处理步骤
1. 获取被监控节点和MOICAgentMgmtService服务的IP地址，如图1所示。
- 如果是根源告警，在ManageOne运维面打开告警对应的“告警详情”查看“定位信息”，获取被监控节点的主机IP信息和MOICAgentMgmtService服务所在节点IP地址。
- 如果是汇聚告警，在ManageOne运维面打开告警对应的“被汇聚告警”查看“定位信息”，获取被监控节点的主机IP信息和MOICAgentMgmtService服务所在节点IP地址。
图1 告警详情
2. 判断是否网络故障。
- 使用Putty工具以具有登录权限的系统用户登录MOICAgentMgmtService服务所在节点IP地址。切换到root用户。
- 使用sopuser用户登录MOICAgentMgmtService服务所在节点IP地址。
- 执行如下命令，切换root用户。
sudo su root
- 执行如下命令检查MOICAgentMgmtService服务节点和被监控节点之间的通信是否存在网络阻塞。
ping 被监控节点IP
回显信息如下，表示网络正常，执行3。否则，网络阻塞，执行5。
64 bytes from 被监控节点IP
3. 收集进程信息，检查进程是否正常。
- 使用Putty工具以具有登录权限的系统用户登录上报告警的被监控节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的被监控节点。
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
- 执行如下命令查询MOICAgent进程是否正常。
ps -ef | grep moicagent | grep python
无回显信息，表示进程异常，否则进程正常。
- 进程异常，执行3.c。
- 进程正常，执行6。
- 执行如下命令拉起MOICAgent进程。
- ManageOne节点：
su ossadm -c ". /opt/oss/manager/agent/bin/engr_profile.sh;ipmc_adm -cmd restartapp -tenant manager -app MOICAgent"
- 非ManageOne节点：
sh /home/<USER>/bin/manual/mstart.sh
- 再次执行3.b查询MOICAgent进程是否正常。
- 正常，等待5~10分钟后查看告警是否自动清除。
- 清除，结束。
- 未清除，执行6。
- 异常，执行4。
4. 安装MOICAgent服务。
详细操作参考《华为云Stack 6.5.1 扩容指南》中“安装MOICAgent至新增物理服务器”章节。
- 安装成功，等待5~10分钟后查看告警是否自动清除。
- 清除，结束。
- 未清除，执行6。
- 安装失败，执行6。
5. 检查被监控节点是否故障，并清除故障。
- 查找被监控节点IP对应的参数名称。
在参数信息汇总文件《xxx_export_all_CN.xlsm》的“2.1 工具生成的IP参数”页签中搜索被监控节点IP对应的“参数名称”。如果参数名称前缀为ManageOne，则执行5.b，否则，请执行6。
- 查看节点连接状态是否正常。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在菜单中选择“产品 > 系统监控”。
- 在“节点”页签，查看5.a的参数名称所对应的节点连接状态是否正常。如果正常，执行6，否则，请参考常用故障章节清除故障。
- 清除故障成功，等待5~10分钟后查看告警是否自动清除。
- 清除，结束。
- 未清除，执行6。
- 清除故障失败，执行6。
6. 请联系技术支持工程师协助解决。
##### 告警清除
当MOICAgent上报新的心跳消息时，自动清除此告警。
##### 参考信息
无。