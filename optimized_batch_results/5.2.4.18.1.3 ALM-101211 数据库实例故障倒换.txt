# 5.2.4.18.1 ALM-101211 数据库实例故障倒换

5.2.4.18.1.3 ALM-101211 数据库实例故障倒换
##### 告警解释
当数据库实例故障倒换成功时，产生此告警。此告警修复后需要手工清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101211 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 数据库故障实例的主机名。 |
| 数据库服务 | 产生告警的数据库实例名称。 |
| 数据库类型 | 数据库实例类型。 |
##### 对系统的影响
数据库实例复制状态不正常，可能会有数据丢失。
##### 可能原因
- 数据库主实例所在节点故障或者下电。
- 数据库主实例故障或者停止运行。
##### 处理步骤
1. 在“当前告警”页面，单击本告警名称查看发生故障倒换的数据库实例的主机名、实例名称和类型。
2. 检查是否有“数据库本地主备复制异常”的告警。
- 是，根据该告警对应的修复建议进行处理，具体操作请参见ALM-101210 数据库本地主备复制异常。
- 否，则执行3。
3. 在“当前告警”页面，选择“数据库实例故障倒换”告警，单击“清除”，手工清除此告警。
##### 告警清除
在“告警监控”页面手动清除此告警。
##### 参考信息
无。