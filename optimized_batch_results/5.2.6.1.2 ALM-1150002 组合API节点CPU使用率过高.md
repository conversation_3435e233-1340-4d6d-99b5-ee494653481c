# 5.2.6.1.2 ALM-1150002 组合API节点CPU使用率过高

告警解释
系统每隔1分钟检查一次节点tomcat进程cpu占用是否超过设定的阈值，如果在3分钟内持续超过阈值，产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150002 | 重要 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |
对系统的影响
对系统正常运行无影响，但后续如果业务量增加，可能导致服务不可用。
可能原因
- 业务压力上升。
- 组合API节点系统异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，登录4中确认的虚拟机。
默认帐号：apicom，默认密码：*****。
6. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
7. 执行下面命令，获取组合API服务的tomcat进程信息。
ps -ef |grep tomcat |grep apicom|grep -v grep
apicom 3087 1 2 Apr25 ? 00:39:02 /opt/common/jre/bin/java
回显中的“3087”即为tomcat进程的进程号。
8. 执行以下命令，停止tomcat进程。
kill -9 进程号
其中进程号为7中获取的进程号。
9. 执行以下命令，重新启动进程。
sh /opt/apicom/tomcat/bin/startup.sh
10. 观察10分钟，查看该告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
参考信息
无。