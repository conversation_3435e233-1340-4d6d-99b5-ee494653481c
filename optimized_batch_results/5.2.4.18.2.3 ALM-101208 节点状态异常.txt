# 5.2.4.18.2 ALM-101208 节点状态异常

5.2.4.18.2.3 ALM-101208 节点状态异常
##### 告警解释
当部署面连续16次检测（检测周期为15秒）到节点脱管时，将发送该告警。当节点恢复正常时，该告警将自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101208 | 重要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
无法登录节点，或者在节点上执行操作时可能报错。
##### 可能原因
- 该节点操作系统无法登录或无响应。
- 该节点所属的虚拟机被下电或虚拟机网络连接异常。
- 该节点的ProductMonitorAgent进程异常。
- 该节点的IR证书过期，系统内部通信异常。
- 如果该节点上存在数据库，则可能是数据库实例的数据库复制状态异常，导致节点异常。
##### 处理步骤
1. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
2. 依次执行表1中的检查项及其检查方法，按照对应的故障排除方法修复故障节点。
导致产品节点故障的因素复杂，本节提供该告警基本的排查方法，如果按照以下操作仍然无法清除该告警，请收集告警处理过程中的信息，联系华为技术支持工程师协助解决。
| 表1 产品节点故障排查 | 表1 产品节点故障排查 | 表1 产品节点故障排查 | 表1 产品节点故障排查 |
| --- | --- | --- | --- |
| 序号 | 检查项 | 检查方法 | 故障排除方法 |
| 1 | 网络连接 | 联系管理员检查网络是否异常。 | 请联系管理员修复网络。 |
| 2 | 虚拟机运行状态 | 联系管理员检查虚拟机是否异常，例如虚拟机是否被下电或者被删除。 | 请联系管理员重启并修复虚拟机。 |
| 3 | 操作系统运行状态 | 重启虚拟机，尝试使用PuTTY工具以sopuser用户通过SSH方式是否能登录故障节点。 | 如果不能正常登录或无响应，则说明故障节点的操作系统异常，请联系技术支持工程师处理。 |
| 4 | ProductMonitorAgent进程运行状态 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行如下命令，检查ProductMonitorAgent进程是否运行正常。<br>> ps -ef |grep ProductMonitorAgent<br>系统提示如下类似回显信息表示ProductMonitorAgent进程正在运行：<br>ossadm    21501      1  2 16:47 ?        00:01:18 /opt/oss/envs/ProductMonitorAgent/service/rtsp/python/bin/python /opt/oss/envs/ProductMonitorAgent/service/tools/pyscript/icAgent.pyc -DNFW=productmonitoragent-0-0 | 如果ProductMonitorAgent进程未运行，执行以下命令启动进程。<br>> . /opt/oss/manager/bin/engr_profile.sh<br>> ipmc_adm -cmd startapp -app ProductMonitorAgent -tenant manager<br>系统提示如下回显信息表示ProductMonitorAgent进程启动成功，否则请联系华为技术支持工程师处理。<br>Starting process productmonitoragent-0-0 ... success |
| 5 | IR证书 | 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。<br>sopuser的默认密码为*****。<br>执行以下命令，切换到ossadm用户。<br>su - ossadm<br>ossadm的默认密码为*****。<br>执行以下命令，查看IR证书有效期。<br>> cd /opt/oss/manager/etc/ssl/internal<br>> openssl x509 -in server.cer -noout -dates<br>回显以下类似信息，“notAfter”后所显示的时间即为IR证书的到期时间。<br>notBefore=Oct 18 00:00:00 2018 GMT<br>notAfter=Oct 13 00:00:00 2038 GMT<br>如果IR证书已过期，请更新CA证书。<br>如果IR证书没有过期，则表示不是证书过期导致该故障。 | 更新CA证书，具体操作请参见《华为云Stack 6.5.1 安全管理指南》中的“更新CA证书”章节。 |
| 6 | 数据库复制状态 | 请参见ALM-101210 数据库本地主备复制异常。 | 请参见ALM-101210 数据库本地主备复制异常。 |
3. 重新登录部署面，查看节点的状态。
- 如果恢复的节点的状态为“正常”，则告警已修复。
- 如果恢复的节点的状态不为正常，请联系华为技术支持工程师。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
4. 产生该告警的节点名称发生了变化。
5. 产生该告警的站点名称发生了变化。
6. 产生该告警的服务器不被监控了。
##### 参考信息
无。