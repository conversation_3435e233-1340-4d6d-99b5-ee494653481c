# ********.21 0x3230042 连接日志服务器异常

##### 告警解释
服务器连接日志服务器异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230042 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 日志类型 | 上报的日志类型 |
| 服务器IP地址 | 上报日志的服务器IP地址 |
| 日志服务器IP地址或域名 | 日志服务器的IP地址或域名 |
| 端口 | 日志服务器的监听端口 |
##### 对系统的影响
连接日志服务器异常，可能导致需要上报的日志无法上报。
##### 可能原因
- BCManager与日志服务器间网络连接异常。
- 日志服务器运行异常。
##### 处理步骤
1. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
2. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
3. 执行ping命令检查BCManager与日志服务器间网络连接是否正常。
- 是，请转5。
- 否，请修复网络连接，然后转4。
4. 待网络连接恢复正常，等待5分钟，系统重试上报后，检查告警是否清除。
- 是，处理结束。
- 否，请转5。
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。