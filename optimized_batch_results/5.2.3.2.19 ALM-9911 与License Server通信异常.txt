# 5.2.3.2.19 ALM-9911 与License Server通信异常

##### 告警解释
告警模块按24小时周期检测License状态，当检测到与License Server通信中断时间超过30天，系统产生此告警。
当与License Server通信恢复正常或主机强制加载新License文件，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9911 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>对端地址：License Server的IP地址 |
| 附加信息 | 本端地址：ServiceOM的浮动IP地址<br>对端地址：License Server的IP地址 |
##### 对系统的影响
无影响。
##### 可能原因
与License Server通信中断时间超过30天。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“系统 >系统管理 > License”。
3. 记录页面上显示的ESN号码。
4. 准备合同号。
5. 联系技术支持工程师申请License文件。
6. 在“License”界面，单击“上传License”，在弹出的窗口中选择新申请的License文件。
7. 单击“打开”。
8. 加载License是否成功。
- 是，执行9。
- 否，执行10。
9. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，进入“OpenStack告警”，查看告警是否清除。
- 是，处理完毕。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。