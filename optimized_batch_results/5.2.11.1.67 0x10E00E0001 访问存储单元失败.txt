# ********.67 0x10E00E0001 访问存储单元失败

##### 告警解释
Proxy（IP：[Node_IP]）访问存储单元（路径：[Brick_path]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00E0001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_IP | 备份代理的IP地址。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
Proxy将不再运行访问此存储单元的任务，除非运行恢复正常。
##### 可能原因
- Proxy与存储单元之间的网络连接中断。
- 网络性能差。
##### 处理步骤
- 可能原因：Proxy与存储单元之间的网络连接中断。
- 通过告警上报的IP地址登录Proxy。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 存储单元IP地址”，如果是IPv6，执行“ping6 存储单元IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络，确保网络连接正常。
- 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证Proxy与存储单元之间通信稳定。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无