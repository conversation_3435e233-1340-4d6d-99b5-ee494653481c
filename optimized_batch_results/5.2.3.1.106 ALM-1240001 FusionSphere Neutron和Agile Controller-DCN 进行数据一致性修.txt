# 5.2.3.1.106 ALM-1240001 FusionSphere Neutron和Agile Controller-DCN 进行数据一致性修复时失败

##### 告警解释
Agile Controller-DCN的云平台插件定时或者手动触发云平台Neutron数据和Agile Controller-DCN数据进行数据一致性对比修复，失败时上报告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1240001 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 云服务：产生告警的云服务<br>主机ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>本端地址：产生告警的主机地址信息<br>对端地址：产生告警的主机对接的Agile Controller-DCN的地址信息<br>详细信息：产生告警的详细信息 |
##### 对系统的影响
无。
##### 可能原因
- 白名单文件大小超过10MB。
- 正在执行数据一致性对比任务，新的任务失败。
- 向Agile Controller-DCN控制器请求数据超时。
- 向Agile Controller-DCN请求数据，Agile Controller-DCN返回失败。
- 数据一致性对比修复时，向Agile Controller-DCN下发请求超时。
- 从Agile Controller-DCN下载的文件格式错误或者内容错误。
- Agile Controller-DCN处理下发的修复请求时，长时间没有异步返回结果确认。
- 插件发生其他内部错误。
##### 处理步骤
1. 查看告警的详细信息是否为I/O error occurred on whitelist file。
- 是，执行2。
- 否，执行9。
2. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
6. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
7. 执行 vim /etc/neutron/whitelist.ini，删除whitelist.ini中多余的白名单配置，保证该文件大小小于10MB。
8. 查看告警是否清除。
- 是，处理完毕。
- 否，执行9。
9. 登录Agile Controller-DCN业务面界面，网址是https://{Agile Controller-DCN北向浮动IP}:31943。
10. 选择“集成 > 云平台 > OpenStack”，进入OpenStack页面。
11. 查看该FusionSphere节点对应的云平台详情。
查看“北向链路状态”和“Websocket链路状态”是否在线。
- 是，则通信业务下发链路和控制器处理业务的状态上报链路正常，执行12。
- 否，则链路不通，请执行16。
12. 重复执行2~6。
13. 执行如下命令，查看是否有一致性对比修复任务正在执行。
neutron acctrl-neutron-sync-status
若回显显示如下，说明有一致性对比修复任务正在执行。
+------------+-------------------+----------+
| server     | state             | sync_res |
+------------+-------------------+----------+
| controller | consistency_check | complete |
+------------+-------------------+----------+
- 是，执行14。
- 否，执行16。
14. 等待5~10分钟，重新手动执行数据一致性对比操作，或者等待下次定时任务启动。
15. 查看告警是否清除。
- 是，处理完毕。
- 否，执行16。
16. 请联系技术支持工程师协助解决。
##### 参考信息
无