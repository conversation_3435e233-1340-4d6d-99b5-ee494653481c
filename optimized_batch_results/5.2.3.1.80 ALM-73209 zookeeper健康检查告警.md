# 5.2.3.1.80 ALM-73209 zookeeper健康检查告警

##### 告警解释
Openstack周期（默认为24小时）检查所有zookeeper的数据是否一致，如果存在不一致的数据，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73209 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的服务名称。 |
| 附加信息 | 故障zookeeper的ip列表：产生告警的zookeeper的ip列表。<br>详细信息：<br>XX has diff data with leader XX : zookeeper的数据不一致。<br>XX has diff path with leader XX ：zookeeper的路径数据不一致。 |
##### 对系统的影响
生效的配置数据与用户的预期不一致。
##### 可能原因
双活容灾场景出现过主站点网络隔离。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。具体操作请参见导入环境变量，使用CPS鉴权方式。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 执行如下命令cps zookeeper-data-check，查看命令查询结果。
- 回显如下所示：
- +------------+--------------------------+
- | result     | data                     |
- +------------+--------------------------+
- | consistent | diff_data:{},            |
- |            | diff_path:{},            |
- |            | leader:[u'172.28.8.123'] |
+------------+--------------------------+
6. 查看回显结果是否包含consistent ：
- 是 ，则表明则说明zookeeper数据是一致的，可手动消除该告警或者待下次告警检查周期，告警会自动消除。
- 否，执行6。
7. 请联系技术支持工程师协助解决。
##### 参考信息
无。