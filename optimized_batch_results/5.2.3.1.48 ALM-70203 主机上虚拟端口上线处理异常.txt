# 5.2.3.1.48 ALM-70203 主机上虚拟端口上线处理异常

##### 告警解释
虚拟机端口上线时，网络设置失败产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70203 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。<br>端口信息：产生告警的组件名/触发动作/端口类型。 |
| 附加信息 | 端口列表：产生告警的虚拟端口列表。<br>失败原因：Virtual Interfaces Fail to Be Brought Online，表示虚拟端口上线失败。 |
##### 对系统的影响
虚拟机对应端口的网络不通。
##### 可能原因
- Rabbitmq故障，后端agent通过rpc获取端口信息异常，导致无法正常下发端口网络配置。
- 其它场景：neutron-server内部系统异常，如数据库表项更新/删除并发操作，或第三方driver处理异常等，后端agent查询或更新端口信息失败，引起端口上线失败。
##### 处理步骤
1. 参考ALM-73401 rabbitmq服务故障，检查rabbitmq是否正常，如果rabbitmq正常（或恢复正常），则执行2处理，否则执行7。
2. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 导入环境变量。
6. 根据告警详细信息获取到虚拟机端口uuid，并通过命令行更新port属性触发端口上线处理（示例如下）。等待告警清除，若5分钟告警清除则结束，否则执行7。
neutron port-update port_uuid --admin-state-up False //中断该端口网络通信
neutron port-update port_uuid --admin-state-up True //恢复该端口网络通信
7. 请联系技术支持工程师协助解决。