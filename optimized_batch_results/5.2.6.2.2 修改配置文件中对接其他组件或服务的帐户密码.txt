# 5.2.6.2.2 5.2.6.2.2 修改配置文件中对接其他组件或服务的帐户密码

5.2.6.2.2 修改配置文件中对接其他组件或服务的帐户密码
##### 操作场景
为了确保组合API与对接的组件或服务正常通信，当对方修改了对接的帐户密码，用户需要同步修改组合API业务节点的配置文件中记录的对接组件或服务的帐户密码。
当以下组件或服务修改了帐户密码时，需要同步修改组合API业务节点的配置文件中记录的信息。
- ServiceOM的告警对接帐户
- IAM对接帐户
##### 前提条件
- 已准备跨平台远程访问工具，如“PuTTY”。
- 已获取对接组件或服务上配置的对接帐户密码。
- 已获取组合API业务节点的管理IP地址，apicom帐户的登录密码。
##### 操作步骤
1. 使用PuTTY，依次以“CPT-SRV01”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV01”节点，以“CPT-SRV02”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录“CPT-SRV02”节点。
默认帐户：apicom，默认密码：*****
2. 执行以下命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
sudo su root
3. 执行以下命令，运行加密工具。
cd /opt/apicom/tomcat/taskmgr/WEB-INF/classes/tools
source /etc/profile
java -jar wcc_crypter.jar -t encrypt 用户密码
4. 输入重置后的对接组件或服务的对接密码，按“Enter”，系统会自动生成密码密文。
请保存密文。
5. 执行以下命令，打开配置文件。
vi /opt/apicom/tomcat/taskmgr/WEB-INF/classes/taskmgr-config.properties
6. 按“i”进入编辑状态。
7. 修改配置文件中的参数。
修改OperationCenter的告警对接帐户的密码，将参数“alarm.value”修改为加密后的密文。
修改IAM对接帐户的密码，将参数“iam.auto.password”修改为加密后的密文。
8. 按“Esc”，输入:wq，按“Enter”。
保存修改并退出vi编辑器。
9. 执行以下命令，查询Tomcat进程ID。
ps -ef | grep tomcat
10. 执行以下命令，根据9查询到的Tomcat进程ID，停止Tomcat进程。
kill -9 $PID
例如查询到的Tomcat进程ID为“62666”，则将“$PID”替换为“62666”。
11. 执行以下命令，重启服务。
cd /opt/apicom/tomcat/bin
sh startup.sh
zabbix监控服务处于开启状态时，如果监测到tomcat进程停止，会自动重启tomcat进程，无须手动执行本步骤重启tomcat。