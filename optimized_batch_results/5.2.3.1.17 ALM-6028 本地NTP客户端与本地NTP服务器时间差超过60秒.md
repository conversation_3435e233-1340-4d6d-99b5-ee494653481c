# 5.2.3.1.17 ALM-6028 本地NTP客户端与本地NTP服务器时间差超过60秒

##### 告警解释
ntp-client（NTP客户端）会周期性（默认为2min）检查本节点与主ntp-server（NTP服务器）所在节点的时间差，当时间差超过60s时，产生此告警。当时间差修复后，告警消除。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6028 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：主机管理ip。<br>对端地址：ntp-server浮动ip。 |
##### 对系统的影响
- 如果ntp-client所在节点与本地主ntp-server所在节点时间差超过60s，则不进行时间同步。
- 可能导致合法的token未超过有效期不可用或超过有效期却可用。
- 可能导致时间不同步节点服务不可用。
- 可能导致系统数据采样不正确。
- 可能导致数据丢失。
##### 可能原因
- 本地主ntp-server所在节点时间被修改。
- ntp-client所在节点时间被修改。
- 备ntp-server所在节点与主ntp-server长期网络不通时间未同步，且ntp-client所在节点与本地主ntp-server时间同步，此时触发主备倒换。
- ntp-client所在节点与主ntp-server长期网络不通后恢复网络。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 选择“运维”，进入“日常维护”。
强制时间同步是高危操作，操作前请慎重考虑，操作时需先停止系统服务，然后进行时间同步，同步成功后再重启业务，该动作建议在业务部署前或者业务稳态时执行，否则可能会严重影响业务。该命令不支持远程使用。
3. 稍等2分钟，在“时间同步”标签下，提示由“系统时间状态查询中”变为“系统时间异常”，如图所示。
4. 按提示，单击右侧红色图标，弹出提示窗，稍等2分钟,提示窗列出时钟异常单板。
5. 选择需要同步时间的单板，在提示窗中单击“同步修复”。
强制时间同步过程中管理面会中断10-15分钟，业务发放会失败，已发放业务不会受影响。请根据实际业务判断是否要执行该操作。
强制时间同步是高危操作，操作前请慎重考虑，该动作建议在业务部署前或者业务稳态时执行，否则可能会严重影响业务。强制时间同步过程中会重启主机上除了ntp-server，ntp-client，dns-server，cps-monitor，haproxy服务外的所有服务，因此该命令不支持远程登录使用。
成功对接外部时钟源且时间已同步一致之后，后续如果没有人为修改主ntp-server所在节点时间，产生了此告警，需要排查外部时钟源时间跳变根因，时间强制同步只是规避手段。例如存在多个外部时钟源，且外部时钟源间存在60s以上时差，则需要移除不准确的外部时钟源，单纯通过强制时间同步无法消除告警。
6. 等待5分钟～10分钟，在“时间同步”标签下，提示变为“系统时间正常”，表示时间同步成功。
若提示“同步时间异常”，表示主机强制时间同步异常。
按提示，单击右侧红色图标，弹出提示窗，稍等2分钟,提示窗列出同步时钟失败单板。如图所示。
是否有主机强制时间同步异常。
- 是，参考如何处理强制时间同步失败进行处理。
- 否，执行7。
7. 等待5分钟～10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行8。
8. 请联系技术支持工程师协助解决。
##### 参考信息
无。