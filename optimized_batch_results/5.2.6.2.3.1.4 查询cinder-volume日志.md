# *******.3 *******.3.1.4 查询cinder-volume日志

*******.3.1.4 查询cinder-volume日志
若日志查询到了卷对应的后端存储，即host字段不为None，说明cinder-volume组件报错。
##### 操作步骤
1. Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
2. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
3. 如果该卷未被删除，执行如下命令，查询卷详情中的host信息，判断后端存储类型，如图1所示。
cinder show volume_id | zgrep host
图1 卷详情中的host信息
4. 如果该卷已被删除，则需要在数据库查询host信息，判断后端存储类型。
- 执行如下命令，查询数据库所在节点的IP地址，即“omip”对应的IP地址。如图2所示。
cps template-instance-list --service gaussdb gaussdb
图2 数据库信息
- 使用PuTTY，通过4.a中查询到的IP地址（“status”为“active”的IP地址）登录数据库主节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”。
- 执行如下命令，登录数据库。
su gaussdba
- 执行如下命令，进入数据库。
gsql -d cinder
数据库的默认密码为*****。
- 执行如下命令，查询该volume_id的host信息，如图3所示。
select host from volumes where id = 'volume_id';
图3 volume_id的host信息
5. 通过host的字段信息查询所有cinder-volume控制节点的IP地址，即使“omip”对应的IP地址。如图4所示。
cps template-instance-list --service cinder cinder-volume-xxx
如果4.g中查询的后端存储为cinder-vrm001，则cinder-volume-xxx为cinder-volume-vrm001；如果查询到的是cinder-kvm001，则cinder-volume-xxx为cinder-volume-kvm001。
图4 后端存储节点
6. 通过5中查询到的IP地址依次登录所有cinder-volume控制节点。
默认帐号：fsp，默认密码：*****
7. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
8. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
9. 执行如下命令，查询每个节点上的cinder-volume-xxx日志。
zgrep req_id /var/log/fusionsphere/component/cinder-volume-xxx/*
req_id 为7中查询到的request id。
cinder-volume-xxx与5中的配置相同。
10. 依据查询到的日志信息进行分析和处理。
< 上一节