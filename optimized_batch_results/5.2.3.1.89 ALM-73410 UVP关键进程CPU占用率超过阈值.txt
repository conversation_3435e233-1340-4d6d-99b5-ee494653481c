# 5.2.3.1.89 ALM-73410 UVP关键进程CPU占用率超过阈值

##### 告警解释
当UVP关键进程的CPU使用率超过配置的阈值，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73410 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
UVP管理开销增加，导致UVP管理性能下降。
##### 可能原因
UVP管理进程任务繁忙。
##### 处理步骤
1. 告警是否自动清除。
- 是，任务结束。
- 否，执行2。
2. 使用“PuTTY”登录FusionSphere OpenStack控制节点。
以“fsp”用户，通过反向代理IP地址登录。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 导入环境变量。
6. 执行cps host-list命令查看告警详细信息中所报主机对应的管理IP地址。
7. 执行以下命令跳转到异常服务所在主机。
su - fsp
ssh fsp@HOST_MANAGE_IP
按照提示输入系统私钥密码，默认私钥密码是“*****”。如果已生成并替换了新的公私钥文件，请输入新私钥密码。或者直接按“Enter”后按照提示输入fsp用户的密码登录。
以fsp用户登录后，通过su - root命令切换到root用户。
8. 通过告警拓展信息确定异常进程名称。
- 如果进程名称为libvirtd，执行9。
- 如果进程名称为hirmd、vBMC_agentd、UVPHostd、virtlogd、systemd-journal、dbus-daemon、rsyslogd、sysalarm、sysmonitor、getosstat，请执行10。
- 如果进程名称为systemd，请执行11。
- 如果进程名称为ovs-vswitchd、ovsdb-server，请执行12。
ovs-vswitchd进程CPU占用率超过阈值时，会收集evs mac表项、端口绑核信息并记录到关键进程监控日志/var/log/sysmonitor/process_monitor.log。
- 如果进程名称为虚拟机名称，请执12。
9. 通过PuTTY登录主机，调用“virsh event --all”命令接收libvirtd服务事件，等待1分钟，执行ctrl+c操作停止接收libvirtd服务事件，确认是否接收到超过15条事件。
- 是，请停止在该主机上执行虚拟机生命周期管理操作，然后再次重新执行该步骤。
- 否，请执行10。
10. 通过PuTTY登录主机，执行如下命令，重启进程对应的服务，等待30秒钟确认告警是否自动消除。
systemctl restart ${SERVER_NAME}
${SERVER_NAME}为进程对应的服务名称。
以下几个进程名称对应的${SERVER_NAME}需要重新指定：
- 进程systemd-journal对应的服务名为systemd-journald
- 进程dbus-daemon对应服务名为dbus
- 进程rsyslogd对应的服务名为rsyslog
- 是，执行完毕。
- 否，执行13。
11. 通过PuTTY登录主机，执行如下命令，重新加载systemd（等待30秒钟确认告警是否自动消除）。
systemctl daemon-reload
- 是，执行完毕。
- 否，执行13。
12. 使用“uvplog -o host -t hyp”命令收集日志并联系技术支持。
13. 请联系技术支持工程师协助解决。
##### 参考信息
无。