# 5.2.19.2.7 ALM-48318-证书回退失败 未知章节

##### 告警解释
ManageOne下发证书替换任务后，网关替换失败且自动回退失败。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48318 | 重要 | 处理错误告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 回退失败证书所属组件名称 |
| 定位信息 | File_Name | 回退失败证书名称 |
| 定位信息 | Node | 告警源节点IP地址 |
##### 对系统的影响
组件证书无法使用，导致无法正常提供服务。
##### 可能原因
ManageOne下发证书替换任务，节点在替换证书过程中出错，并且证书回退任务执行失败。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Component：表示组件名称。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录告警源节点Node。
默认帐号： paas，默认密码：*****。
3. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 进入到证书目录。检查是否有该证书文件、私钥文件、及对应以“.bak”结尾的备份文件。
参考《华为云Stack 6.5.1 安全管理指南》“帐户管理 -> 帐户一览表”中的“FusionSphere Service”页签，查看API网关证书文件路径。
- 是 => 6
- 否 => 10
6. 执行cp xxx.bak xxx（xxx表示证书或者私钥文件带扩展名的名称，例如server.crt,server.key.encrypt等）命令，将“.bak”结尾的备份文件覆盖当前路径下的证书和私钥文件。
7. 执行以下命令，切换到相应组件运行用户。
su - 用户名
除apimgr组件使用apigw_apimgr用户外，其他组件使用apigateway用户。
8. 执行以下命令，重启组件。该重启操作不会对系统造成不良影响。
sh /opt/apigateway/Component/shell/restart.sh
显示xxx start successfully，表示服务启动成功。
9. 执行以下命令，检查组件运行状态。
sh /opt/apigateway/Component/shell/health_check.sh
- normal => 手动清除该告警
- abnormal => 10
10. 获取Component组件相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
- 故障组件（Component对应值）为“shubao” => 执行命令：cd /var/log/apigateway/shubao/run
- 故障组件（Component对应值）为其他组件 => 执行命令：cd /var/log/apigateway/Component/runtime
- 下载日志“Component.log”到本地，并联系技术支持。
如果目录下不存在“Component.log”，下载“Component_shell.log”即可。
##### 告警清除
手动清除。
##### 参考信息
无。