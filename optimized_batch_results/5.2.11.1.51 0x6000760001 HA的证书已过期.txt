# ********.51 0x6000760001 HA的证书已过期

##### 告警解释
在eBackup服务器（IP：[NodeIP]）上的HA功能的证书已过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000760001 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | eBackup服务器的IP地址。 |
##### 对系统的影响
HA功能存在安全风险。
##### 可能原因
- HA功能的证书已过期。
- 系统时间不在HA功能的证书的有效期内。
##### 处理步骤
- 可能原因1：HA的证书已过期。
- 请联系管理员获取有效的HA证书文件。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点（角色是“备份服务器（主）”或“备份管理服务器（主）”）和备节点（角色是“备份服务器（备）”或“备份管理服务器（备）”）的备份管理平面的IP地址。
- 分别登录HA的主备节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
- 执行mv /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem.bak和mv /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem.bak命令，备份原有HA证书文件。
- 通过WinSCP工具将cacert.pem和server.pem文件拷贝到“/home/<USER>
默认帐户：hcp，默认密码：*****
- 执行mv /home/<USER>/cacert.pem /opt/huawei-data-protection/ebackup/ha/local/cert/和mv /home/<USER>/server.pem /opt/huawei-data-protection/ebackup/ha/local/cert/命令，将证书文件移动HA的证书路径下。
- 执行chmod 600 /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem和chmod 600 /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem命令，将“cacert.pem”和“server.pem”文件的权限设置为600。
- 执行chown hcpprocess /opt/huawei-data-protection/ebackup/ha/local/cert/cacert.pem和chown hcpprocess /opt/huawei-data-protection/ebackup/ha/local/cert/server.pem命令，将“cacert.pem”和“server.pem”文件的所有者修改为hcpprocess。
- 执行/opt/huawei-data-protection/ebackup/ha/module/hacom/script/stop_ha_process.sh命令，重启HA服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 可能原因2：系统时间不在HA功能的证书的有效期内。
- 使用PuTTY，以管理IP地址登录eBackup-Manager节点（Manager角色所在节点）或Server角色所在节点。
登录地址：
- eBackup-Manager节点的管理IP地址为“Workflow-PublicService-IP0”字段对应的IP地址。
- Server角色所在节点的管理IP地址为“datamover_externalom_iplist”字段对应的IP地址。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行date命令检查系统时间是否与当前时间一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.d。
- 执行date -s "年-月-日 时:分:秒"命令，手动修改系统时间。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无