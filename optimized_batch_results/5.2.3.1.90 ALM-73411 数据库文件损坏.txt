# 5.2.3.1.90 ALM-73411 数据库文件损坏

##### 告警解释
数据库保存数据的文件损坏。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73411 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 主机名：产生告警的主机名。<br>详情：告警的详细信息。 |
##### 对系统的影响
此告警产生，损坏文件所属的数据表将无法访问，可能会导致业务失败。
##### 可能原因
系统发生了异常掉电。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 导入环境变量，具体操作请参见导入环境变量。
5. 获取上报告警的组件名及发生的节点id。
查看告警详情，获取告警定位信息，告警定位信息中的服务为组件名。并查看相同组件名的该告警有几条。
因为文件损坏是跟节点相关的，如果在多个节点上都发生了损坏，则会有多条告警。记录下告警发生的所有节点id。
6. 获取组件的部署及主备信息。
执行如下命令，获取组件的服务名。
cps template-list|grep 组件名 ，执行结果如下所示，其中第一列为服务名。
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps template-list | grep gaussdb
| gaussdb_keystone    | gaussdb_keystone               | DataBase for Keystone service.                     |
| gaussdb             | gaussdb                        | DataBase for OpenStack service.                    |
执行如下命令，获取组件的部署及主备信息。
cps template-instance-list --service 服务名 组件名
如下以gaussdb为例：
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps template-instance-list --service gaussdb gaussdb
+------------+---------------+---------+--------------------------------------+-------------+
| instanceid | componenttype | status  | runsonhost                           | omip        |
+------------+---------------+---------+--------------------------------------+-------------+
| 0          | gaussdb       | active  | 564D7942-B752-7955-C0B0-B762D66E841D | ************* |
| 1          | gaussdb       | standby | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | ************* |
+------------+---------------+---------+--------------------------------------+-------------+
7. 根据5和6的信息，确认属于下面哪种情况，并执行对应的处理。
- 数据文件损坏只发生在备数据库，执行8。
- 数据文件损坏只发生在主数据库，执行9。
- 数据文件损坏发生主数据库和备数据库，执行10。
8. 重建备数据库。
- 登录备数据库所在节点。
- 导入环境变量，具体操作请参见导入环境变量。
- 依次执行以下命令，检查是否需要导入证书相关环境变量。
cps template-params-show --service 服务名 组件名 | grep -E "database_use_ssl|force_cert_check"
回显如下所示：
A074D328-D21D-B211-8FAE-001823E5F68B:~ # cps template-params-show --service gaussdb gaussdb | grep -E "database_use_ssl|force_cert_check"
| database_use_ssl         | true                                               |
| force_cert_check         | true                                               |
根据回显信息，查看返回值是否都为true：
- 是，执行以下命令导入证书相关的环境变量：
export PGSSLCERT="/opt/fusionplatform/data/gaussdb_data/client_cert/client.crt"
export PGSSLKEY="/opt/fusionplatform/data/gaussdb_data/client_cert/client.key"
export PGSSLMODE="verify-ca"
export PGSSLROOTCERT="/opt/fusionplatform/data/gaussdb_data/client_cert/ca.crt"
- 否，执行8.d。
- 执行以下命令，进行数据库重建。
su gaussdba
gs_ctl build
命令成功执行如下所示：
[gaussdba@564DF71C-912D-7C7A-F209-BE12097BD7D4 root]$ gs_ctl build
could not change directory to "/root"
waiting for server to shut down.... done
server stopped
gs_ctl: connect to server, build started.
xlog start point: 0/17000020
gs_ctl: starting background WAL receiver
252775/252775 kB (100%), 1/1 tablespace
xlog end point: 0/1700CFD8
gs_ctl: waiting for background process to finish streaming...
gs_ctl: build completed.
server starting.... done
server started
判断重建是否成功。
- 是， 需重启本节点的数据库，执行12。
- 否，请联系技术支持工程师协助解决。
9. 重建主数据库。
- 登录主数据库所在节点。
- 导入环境变量，具体操作请参见导入环境变量。
- 执行以下命令，进行主备倒换。
cps host-template-instance-operate --service 服务名 组件名 --action swap
- 等待1分钟~3分钟，执行以下命令，查询组件的主备状态，确认倒换是否成功。
cps template-instance-list --service 服务名 组件名
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps template-instance-list --service gaussdb gaussdb
+------------+---------------+---------+--------------------------------------+-------------+
| instanceid | componenttype | status  | runsonhost                           | omip        |
+------------+---------------+---------+--------------------------------------+-------------+
| 0          | gaussdb       | active  | 564D7942-B752-7955-C0B0-B762D66E841D | ************* |
| 1          | gaussdb       | standby | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | ************* |
+------------+---------------+---------+--------------------------------------+-------------+
待原先的standby变为active，原先的active变为standby，即倒换成功。
由于倒换需要一定的时间，如果查询倒换没有完成，再等待一段时间查询倒换是否成功。
- 是， 执行9.e。
- 否，请联系技术支持工程师协助解决。
- 依次执行以下命令，检查是否需要导入证书相关环境变量。
cps template-params-show --service 服务名 组件名 | grep -E "database_use_ssl|force_cert_check"
回显如下所示：
A074D328-D21D-B211-8FAE-001823E5F68B:~ # cps template-params-show --service gaussdb gaussdb | grep -E "database_use_ssl|force_cert_check"
| database_use_ssl         | true                                               |
| force_cert_check         | true                                               |
根据回显信息，查看返回值是否都为true：
- 是，执行以下命令导入证书相关的环境变量：
export PGSSLCERT="/opt/fusionplatform/data/gaussdb_data/client_cert/client.crt"
export PGSSLKEY="/opt/fusionplatform/data/gaussdb_data/client_cert/client.key"
export PGSSLMODE="verify-ca"
export PGSSLROOTCERT="/opt/fusionplatform/data/gaussdb_data/client_cert/ca.crt"
- 否，执行9.f。
- 执行以下命令，进行数据库重建。
su gaussdba
gs_ctl build
命令成功执行如下所示：
[gaussdba@564DF71C-912D-7C7A-F209-BE12097BD7D4 root]$ gs_ctl build
could not change directory to "/root"
waiting for server to shut down.... done
server stopped
gs_ctl: connect to server, build started.
xlog start point: 0/17000020
gs_ctl: starting background WAL receiver
252775/252775 kB (100%), 1/1 tablespace
xlog end point: 0/1700CFD8
gs_ctl: waiting for background process to finish streaming...
gs_ctl: build completed.
server starting.... done
server started
判断重建是否成功。
- 是， 需重启本节点的数据库，执行12。
- 否，请联系技术支持工程师协助解决。
10. 修复主备数据库。
由于主备数据库均异常，该修复操作会导致数据丢失，请谨慎操作，如有疑惑，请联系技术支持工程师协助解决。
如果确认执行修复操作，修复完后建议执行系统审计操作。
- 登录主数据库所在节点。
- 找到发生损坏的database。
查看告警附加信息中的详情，找到存在文件损坏的database，可能会存在多个。
- 执行以下命令，登录异常database，这里以nova database为例。
su gaussdba
gsql nova
执行gsql nova命令时，会提示输入密码，默认密码为*****，如果修改过数据库密码，以实际密码为准。
- 执行以下命令，打开数据表修复开关。
set zero_damaged_pages=on;
- 执行以下命令，对异常数据表进行重构。
vacuum full;
该操作时间跟数据表的数据量大小有关，可能要数分钟至数十多分钟，请耐心等待。
命令执行过程是否报error提示，导致重构中断。
- 否，执行10.f。
- 是，请联系技术支持工程师协助解决。
- 如果有多个database发生损坏，对每一个database重复10.c~10.e。
- 重建备数据库。
- 登录备数据库所在节点。
- 导入环境变量，具体操作请参见导入环境变量。
- 依次执行以下命令，检查是否需要导入证书相关环境变量。
cps template-params-show --service 服务名 组件名 | grep -E "database_use_ssl|force_cert_check"
回显如下所示：
A074D328-D21D-B211-8FAE-001823E5F68B:~ # cps template-params-show --service gaussdb gaussdb | grep -E "database_use_ssl|force_cert_check"
| database_use_ssl         | true                                               |
| force_cert_check         | true                                               |
根据回显信息，查看返回值是否都为true：
- 是，执行以下命令导入证书相关的环境变量：
export PGSSLCERT="/opt/fusionplatform/data/gaussdb_data/client_cert/client.crt"
export PGSSLKEY="/opt/fusionplatform/data/gaussdb_data/client_cert/client.key"
export PGSSLMODE="verify-ca"
export PGSSLROOTCERT="/opt/fusionplatform/data/gaussdb_data/client_cert/ca.crt"
- 否，执行10.g.iv。
- 执行以下命令，进行数据库重建。
su gaussdba
gs_ctl build
命令成功执行如下所示：
[gaussdba@564DF71C-912D-7C7A-F209-BE12097BD7D4 root]$ gs_ctl build
could not change directory to "/root"
waiting for server to shut down.... done
server stopped
gs_ctl: connect to server, build started.
xlog start point: 0/17000020
gs_ctl: starting background WAL receiver
252775/252775 kB (100%), 1/1 tablespace
xlog end point: 0/1700CFD8
gs_ctl: waiting for background process to finish streaming...
gs_ctl: build completed.
server starting.... done
server started
判断重建是否成功。
- 是， 执行11。
- 否，请联系技术支持工程师协助解决。
11. 重启组件所有实例。
停止组件，cps host-template-instance-operate --service 服务名 组件名 --action stop
+----------+--------------------------------------+--------+---------+
| template | runsonhost                           | action | result  |
+----------+--------------------------------------+--------+---------+
| gaussdb  | 564D7942-B752-7955-C0B0-B762D66E841D | stop   | success |
| gaussdb  | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | stop   | success |
+----------+--------------------------------------+--------+---------+
查询状态，cps template-instance-list --service 服务名 组件名，status为fault后，再启动组件。
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps template-instance-list --service gaussdb gaussdb
+------------+---------------+--------+--------------------------------------+-------------+
| instanceid | componenttype | status | runsonhost                           | omip        |
+------------+---------------+--------+--------------------------------------+-------------+
| 0          | gaussdb       | fault  | 564D7942-B752-7955-C0B0-B762D66E841D | ************* |
| 1          | gaussdb       | fault  | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | ************* |
+------------+---------------+--------+--------------------------------------+-------------+
启动组件，cps host-template-instance-operate --service 服务名 组件名 --action start
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps host-template-instance-operate --service gaussdb gaussdb --action start
+----------+--------------------------------------+--------+---------+
| template | runsonhost                           | action | result  |
+----------+--------------------------------------+--------+---------+
| gaussdb  | 564D7942-B752-7955-C0B0-B762D66E841D | start  | success |
| gaussdb  | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | start  | success |
+----------+--------------------------------------+--------+---------+
再查询状态，cps template-instance-list --service 服务名 组件名，等待组件状态变为active、standby。
所有组件实例是否都正常。
- 是，执行13
- 否，请联系技术支持工程师协助解决。
12. 重启组件单个实例。
停止组件，cps host-template-instance-operate --service 服务名 组件名 --host host_id --action stop
host_id需替换为实际的节点id。
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps host-template-instance-operate --service gaussdb gaussdb --action stop --host 564DF71C-912D-7C7A-F209-BE12097BD7D4
+----------+--------------------------------------+--------+---------+
| template | runsonhost                           | action | result  |
+----------+--------------------------------------+--------+---------+
| gaussdb  | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | stop   | success |
+----------+--------------------------------------+--------+---------+
查询组件状态，cps template-instance-list --service 服务名 组件名，待被停止的实例变为fault。
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps template-instance-list --service gaussdb gaussdb
+------------+---------------+--------+--------------------------------------+-------------+
| instanceid | componenttype | status | runsonhost                           | omip        |
+------------+---------------+--------+--------------------------------------+-------------+
| 0          | gaussdb       | active | 564D7942-B752-7955-C0B0-B762D66E841D |************* |
| 1          | gaussdb       | fault  | 564DF71C-912D-7C7A-F209-BE12097BD7D4 |************* |
+------------+---------------+--------+--------------------------------------+-------------+
启动组件，cps host-template-instance-operate --service 服务名 组件名 --host host_id--action start
564D7942-B752-7955-C0B0-B762D66E841D:~ # cps host-template-instance-operate --service gaussdb gaussdb --action start --host 564DF71C-912D-7C7A-F209-BE12097BD7D4
+----------+--------------------------------------+--------+---------+
| template | runsonhost                           | action | result  |
+----------+--------------------------------------+--------+---------+
| gaussdb  | 564DF71C-912D-7C7A-F209-BE12097BD7D4 | start  | success |
+----------+--------------------------------------+--------+---------+
再查询状态，cps template-instance-list --service 服务名 组件名，等待组件状态正常。
组件状态是否正常。
- 是，执行13。
- 否，请联系技术支持工程师协助解决。
13. 等待5分钟～10分钟，查看告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。