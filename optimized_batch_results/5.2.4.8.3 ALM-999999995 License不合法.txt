# *******.3 ALM-999999995 License不合法

##### 告警解释
当License文件与系统的服务器ESN（Equipment Serial Number）不匹配、License文件为失效状态或者License进入宽限期时产生该告警。当更新的License文件在有效期内且ESN匹配或License文件已经过期，该告警会自动清除。
- 该告警适用于License 2.00和License 3.00所有文件类型的License文件。
- ESN（Equipment serial number）：设备序列号，又称“设备指纹”，是唯一标识设备的字符串，用来保证将License授权给指定设备。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 999999995 | 重要 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| LSN | License序列号。 |
##### 对系统的影响
License不合法时，系统提供了一个宽限期，宽限期天数参见License文件。License文件超过宽限期后可能导致系统无法正常使用。
##### 可能原因
- License文件与系统的服务器ESN不匹配。
- 当前License文件已失效。
- 当前License文件已超过有效期。
##### 处理步骤
1. 在主菜单选择“系统管理 > 系统设置 > License管理”。
2. 检查License文件与系统的服务器ESN是否匹配。在左侧导航树中选择“License文件”，根据LSN（License Serial Number）展开对应License文件，查看“ESN是否匹配”。
- 是，执行3。
- 否，执行5。
3. 查看License是否失效。在左侧导航树中选择“License文件”，根据LSN展开对应License文件，查看是否有“失效码”。
- 是，表示License已失效，执行5。
- 否，表示License没有失效，执行4。
4. 查看当前License是否已过有效期。
- 是，执行5。
- 否，联系华为技术支持工程师。
5. 申请License文件。
- 获取License失效码。
- 执行此操作需要具备“失效License”的操作权限。
- 失效License的操作不可恢复。设置当前License失效后，该License进入宽限期，宽限期参见License文件。宽限期后该License将无法使用。
- 在“License文件”页面，单击目标License文件“操作”列的“失效License”。
- 在“确认”对话框中，单击“确定”。
- 在License文件列表中，单击目标License文件的查看并获取“失效码”。
- 联系华为技术支持工程师使用获取的License失效码申请新的License文件。
6. 更新License文件。
- 在左侧导航树中选择“更新License”。
- 在“更新License”页面中，单击“License文件”后面的。
- 选择已申请的License文件，单击“打开”。
- 单击“上传”，查看License更新前后的结果对比。
- 单击“应用”，立即应用新的License文件。
7. 查看本告警是否已清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。