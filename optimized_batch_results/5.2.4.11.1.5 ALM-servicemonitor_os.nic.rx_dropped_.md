# 5.2.4.11.1 ALM-servicemonitor_os.nic.rx_dropped_ps 网卡流入丢包率阈值告警

5.2.4.11.1.5 ALM-servicemonitor_os.nic.rx_dropped_ps 网卡流入丢包率阈值告警
##### 告警解释
当被监控对象的网卡流入丢包率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.nic.rx_dropped_ps | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的IP地址。 |
##### 对系统的影响
系统的网卡流入丢包率过高，将导致系统中进程网络通信延迟增高或无法正常通信，业务响应时间增大。
##### 可能原因
- 系统运行了大量的网络通信进程。
- 系统中的某个进程频繁通信。
- 系统设备故障。
##### 处理步骤
1. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
2. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
3. 查看网卡信息。
cd /sys/class/net/
ll
4. 查看某一个网卡流入丢包数。例如查看“eth0”网卡流入丢包数。
如果“eth0”网卡流入丢包数大于0，执行5，否则联系技术支持协助处理。
cat /sys/class/net/eth0/statistics/rx_dropped
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。