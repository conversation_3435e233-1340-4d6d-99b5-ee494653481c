# *******.3 *******.3.1.2 查看cinder-api日志

*******.3.1.2 查看cinder-api日志
- Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
- 执行如下命令，查询所有cinder-api控制节点的IP地址，即“omip”对应的所有IP地址，如图1所示。
cps template-instance-list --service cinder cinder-api
图1 控制节点信息
- 通过4中查询到的IP地址依次登录所有cinder-api控制节点。
默认帐号：fsp，默认密码：*****
- 由于控制节点是分布式部署，需要对每一个控制节点进行查询。
- 所有控制节点均有cinder-api日志，上层下发的请求可能在任何一个节点上处理。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下步骤根据卷volume_id查找request id，定位处理请求的节点。
- 因为节点处理该请求是随机分配的，所以在每个一个控制节点上都要根据卷volume_id查找request id。
- request id格式为req-xxx。
- 执行如下命令，查看cinder-api文件目录，如图2所示。
ll /var/log/fusionsphere/component/cinder-api
图2 cinder-api文件目录
- 执行如下命令，通过volume_id搜索日志文件查找request id。根据错误发生的时间，匹配查找到的日志信息，获得request id，如图3所示。
zgrep volume_id /var/log/fusionsphere/component/cinder-api/*
图3 查找request id
- 执行如下命令，在该节点根据request id继续查询日志信息，根据ERROR字样，查看报错信息，如图4所示。
zgrep req_id /var/log/fusionsphere/component/cinder-api/*
- 找到报错信息，则根据日志信息解决问题。
- 没有找到报错信息。
- 若是创建空白卷，或者使用镜像创卷产生错误（备份、快照创卷不涉及cinder-scheduler过程），进入查看cinder-scheduler日志继续定位。
- Region Type I场景，若创建的不是空白卷，或者不是使用镜像创卷，进入排查被级联层OpenStack继续定位。
图4 报错信息