# ********.6 ALM-48108-无法访问shubao-orchestration

##### 告警解释
API网关存在后端服务，shubao-orchestration服务出现异常。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48108 | 重要 | 通信告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Fault_Component | 故障组件名称 |
| 定位信息 | Fault_Node | 故障节点IP地址 |
| 定位信息 | Source_Component | 告警源组件 |
| 定位信息 | Source_Node | 告警源节点IP地址 |
| 附加信息 | Alarm_Reason | 告警原因 |
##### 对系统的影响
无法提供API编排功能。
##### 可能原因
shubao-orchestration服务异常，导致orchestration无法正常访问shubao-orchestration。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Source_Node：表示告警源节点IP地址。
- Fault_Node：表示故障节点IP地址。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录故障节点Fault_Node。
默认帐号：paas，默认密码：*****。
3. 执行以下命令，先切换到root用户，再切换到apigateway用户。
sudo su - root
默认密码：*****。
su - apigateway
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 执行以下命令，ping告警源节点IP地址，查看网络通讯是否正常。
ping Source_Node
- 正常 => 6
- 异常 => 请联系网络工程师修复网络通讯后，再执行6。
6. 执行以下命令，检查shubao-orchestration状态是否正常。
sh /opt/apigateway/resty/shell/health_check.sh
- normal => 8
- abnormal => 7
7. 执行以下命令，重启shubao-orchestration组件。该重启操作对系统本身无不良影响。
sh /opt/apigateway/resty/shell/restart.sh
显示“xxx start successfully”，表示组件启动成功。
8. 等待1~3分钟，查看告警是否清除。
- 是 => 处理完毕
- 否 => 9
9. 获取相关日志，并请联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
cd /var/log/apigateway/orchestration/runtime/
- 下载日志“orchestration.log”到本地，并联系技术支持。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。