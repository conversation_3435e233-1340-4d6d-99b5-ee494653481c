# 5.2.3.1.87 ALM-73404 检测到gaussdb恶意访问

##### 告警解释
周期性(60s)检查gaussdb日志中是否包含特定关键字，日志中出现这些关键字意味着gaussdb有可能正在遭受外部的恶意访问，需要维护人员确认并采取相应措施。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73404 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的服务。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 详细信息：告警的详细信息。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |
##### 对系统的影响
此告警产生意味着gaussdb有可能正在遭受外部的恶意访问。
##### 可能原因
- 某些正常场景下的告警。
- gaussdb备份恢复导致的告警。
- 配置错误等异常场景导致的告警。
- gaussdb正在遭受外部的恶意访问。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
检查是否为正常场景下的告警
8. 执行如下命令，进入到gaussdb日志目录：
cd /var/log/fusionsphere/component/gaussdb/
9. 在gaussdb日志中寻找告警检测到的关键字，关键字从告警附加信息字段中获取，这里以"permission denied"为例：
执行命令：zgrep "permission denied" * 在日志中查找关键字。
10. 根据查找到的关键字打印时间点，进一步确认是否当时gaussdb正在遭受外部恶意访问。
- 是，执行12，上报系统安全风险，采取相应措施。
- 否，执行11。
11. 将告警手动清理。
处理完毕。
12. 请联系技术支持工程师协助解决。
##### 参考信息
无。