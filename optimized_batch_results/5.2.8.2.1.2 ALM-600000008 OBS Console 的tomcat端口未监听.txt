# 5.******* ALM-600000008 OBS Console 的tomcat端口未监听

5.*******.2 ALM-600000008 OBS Console 的tomcat端口未监听
##### 告警解释
当被监控对象的Tomcat 7583端口绑定失败时，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000008 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
OBS Console服务无法正常使用。
##### 可能原因
OBS Console服务的Tomcat 7583端口绑定失败。
##### 处理步骤
1. 使用PuTTY，以“CONSOLE01”或“CONSOLE02”字段对应的IP地址登录OBS Console节点。
默认帐户：obs_admin，默认密码：*****。
IP地址请在HUAWEI CLOUD Stack Deploy部署工具配置部署参数后导出的参数信息汇总文件《xxx_export_all_CN.xlsm》的“工具生成的IP参数”页签搜索“CONSOLE01”或“CONSOLE02”获取。
2. 执行以下命令，切换到root用户。
sudo su
在“password for root:”后输入root用户的密码，root用户默认密码为*****。
3. 执行以下命令检查OBS服务是否已正常启动。
sh /etc/obs/console/control.sh status
- 是，OBS服务正常启动，回显显示如下所示，执行4。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh status
Console is running
- 否，OBS服务启动失败，回显显示“Console is not running”，请执行5。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh status
Console is not running
4. 执行以下命令检查Tomcat相应端口是否正常绑定。
netstat -ntl
- 是，OBS服务端口绑定正常，回显显示有OBS Console服务的端口（7583），请执行6。
- [root@CONSOLE02 obs_admin]# netstat -ntl
- Active Internet connections (only servers)
- Proto Recv-Q Send-Q Local Address           Foreign Address         State
- tcp        0      0 **********:22             0.0.0.0:*               LISTEN
- tcp        0      0 **********:21700          0.0.0.0:*               LISTEN
- tcp6       0      0 **********:7563           :::*                    LISTEN
- tcp6       0      0 **********:7443           :::*                    LISTEN
- tcp6       0      0 127.0.0.1:2324          :::*                    LISTEN
tcp6       0      0 **********:7583           :::*                    LISTEN
- 否，OBS服务端口绑定失败，回显没有OBS Console服务的端口（7583），请执行5。
5. 执行以下命令后，根据回显结果确认是否成功重启OBS服务。
sh /etc/obs/console/control.sh restart
- 是，OBS服务重启成功，回显显示“Start console successfully”，请执行6。
- [root@CONSOLE02 obs_admin]# sh /etc/obs/console/control.sh restart
- Stop console successfully
- Starting console component...
- Console started successfully
Start console successfully
- 否，请联系技术支持工程师协助解决。
6. 请检查告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。