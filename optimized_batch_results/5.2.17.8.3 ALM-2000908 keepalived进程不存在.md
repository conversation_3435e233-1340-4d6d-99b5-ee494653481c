# ********.3 ALM-2000908 keepalived进程不存在

告警解释
keepalived进程异常终止时，生成此告警。
告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 2000908 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
对业务影响严重，需紧急处理。
可能原因
keepalived进程无法启动。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，通过4中确认的IP地址登录虚拟机。
默认帐号：ulb，默认密码：*****。
6. 执行以下命令，防止PuTTY超时退出。
TMOUT=0
7. 执行以下命令切换到root用户。
sudo su - root
默认帐号：root，默认密码：*****。
8. 执行以下命令，查看keepalived进程是否正常。
service keepalived status
Checking  for service keepalived                                                                                             running
- 是，执行9。
- 否，执行10。
9. 等待10分钟后，检查告警是否消除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
10. 重启keepalived进程，等待10分钟后，检查告警是否消除。
service keepalived restart
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。