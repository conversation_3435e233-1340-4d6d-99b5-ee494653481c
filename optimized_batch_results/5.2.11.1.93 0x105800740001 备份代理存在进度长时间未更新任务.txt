# ********.93 0x105800740001 备份代理存在进度长时间未更新任务

##### 告警解释
在备份代理（[Node_Ip]）上微服务（[Service_Name]）存在进度长时间未更新任务。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800740001 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | 备份代理IP地址。 |
| Service_Name | 微服务名称。 |
##### 对系统的影响
任务不能结束。
##### 可能原因
访问备份存储的I/O长时间悬挂。
##### 处理步骤
- 可能原因1：访问备份存储的I/O长时间悬挂。
- 获取告警中的备份代理IP地址和微服务名称信息。
- 微服务名称是否为Unknown。
- 是，联系技术支持工程师协助解决。
- 否，执行1.c。
- 根据备份代理IP地址登录后台操作系统。
- 执行ps -ef |grep "微服务名称" 命令获取微服务进程ID。
- 执行kill -9 微服务进程ID命令终止该微服务进程。
终止后，该微服务会被重新启动。