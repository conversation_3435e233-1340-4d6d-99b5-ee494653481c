# 5.2.3.1.113 ALM-1316003 QEMU版本异常告警

##### 告警解释
QEMU热替换补丁安装后，如果主机上有虚拟机未成功替换成新版本QEMU，仍然使用替换前的老版本QEMU，系统产生此告警。当前主机上所有虚拟机都使用热替换后的新版本QEMU时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316003 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生该告警的主机ID。<br>虚拟机ID：产生该告警的虚拟机ID。 |
| 附加信息 | 服务名：产生该告警的服务名。<br>微服务名：产生该告警的微服务名。<br>主机名：产生该告警的主机名。<br>虚拟机名：产生该告警的虚拟机名。<br>错误信息：该告警相关的错误信息。 |
##### 对系统的影响
出现该告警时，说明有虚拟机对应的QEMU组件未成功替换成新版本。由于QEMU热替换补丁通常用于解决安全漏洞，此异常说明对应虚拟机的安全漏洞没有被修复。
##### 可能原因
- 系统异常，导致QEMU热替换失败。
- 虚拟机配置了不支持QEMU热替换的设备，如配置vGPU设备。
##### 处理步骤
1. 获取告警信息中的虚拟机uuid，找到对应的主机。
2. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
6. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
7. 执行命令，尝试对虚拟机进行热替换。此命令会获取主机上的虚拟机列表，对QEMU版本异常的虚拟机逐一进行QEMU热替换。
python /usr/libexec/uvp_hotreplace_upgrade --action=http://localhost:7890/upgradevm | grep -w "Domain"
- 如果虚拟机热替换成功，会打印以下关键日志：
[ INFO ] ... Domain %s do hot replace success
- 否则会打印以下关键日志：
[ ERROR ] ... Domain %s hot replace fail, reason %s
%s 包含虚拟机名和失败原因。
8. 执行如下命令，查询热替换结果。
python /usr/libexec/uvp_hotreplace_upgrade --action=http://localhost:7890/check_vm_running_at_current_bin 2&>1 > /dev/null
执行如下命令，查看命令执行的返回值。
echo $?
- 返回值0，修复成功，执行9。
- 返回值非0，对QEMU版本异常虚拟机进行重启来完成修复，完成后执行9。
执行重启虚拟机会先关闭虚拟机再重新启动（重新启动的虚拟机会使用新版本的QEMU），此操作会中断虚拟机内的业务。
9. 修复动作执行成功后，请确认下一个检查周期后（12小时），告警是否自动清除。
- 是，处理完毕。
- 否，执行10。
10. 请联系技术支持工程师协助解决。