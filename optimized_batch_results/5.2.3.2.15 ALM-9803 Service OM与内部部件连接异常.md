# 5.2.3.2.15 ALM-9803 Service OM与内部部件连接异常

##### 告警解释
Service OM检查各个内部部件的连接状态，如果连接异常，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9803 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>对端地址：异常组件的地址信息 |
| 附加信息 | 部件名称：异常组件的连接器名称<br>连接类型：异常组件的连接器类型<br>本端地址：ServiceOM的浮动IP地址<br>对端地址：异常组件的地址信息 |
##### 对系统的影响
如果Service OM与内部部件连接异常，Service OM的绝大部分功能将不可用，如：创建虚拟机等 。
##### 可能原因
- 接入部件的用户名、密码不对、token失效。
- 网络中断。
- 接入部件管理进程服务终止或数据库无法访问。
- 第三方部件内部错误。
##### 处理步骤
检查部件状态
1. 在告警列表中，查看当前时间距离本次告警的产生时间是否已达5分钟。
- 是，执行2。
- 否，等待约5分钟后再次检查。
2. 在告警列表中，告警是否仍然存在，并未自动清除。
- 是，执行3。
- 否，任务结束。
3. 在告警列表中，在此告警所在行单击告警名称，在弹出的窗口中查看告警详细信息，在区域“附加信息”中，根据告警对应的连接类型检查该部件的状态。
- 连接类型为FusionCompute、fc-license或者fm-license，请使用“PuTTY”， 以“galaxmanager”用户，通过管理浮动IP登录服务器。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令检查告警中的IP是否可以正常ping通。
ping 告警IP
- 是，执行4。
- 否，请排查网络配置，确保网络处于正常状态后等待5分钟左右，查看告警是否自动清除，如果告警自动消除，任务结束。如果告警没有自动清除请执行4。
- 连接类型为OpenStack或者fusionsphereplatform，请使用“PuTTY”， 以“galaxmanager”用户，通过浮动IP登录Service OM服务器。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令查看Service OM使用的keystone服务信息。
configHosts show | grep identity
检查Service OM使用的keystone服务信息和OpenStack使用的keystone服务信息是否一致：
- 是，请按照3.b.ii排查。
- 否，按照以下步骤排查。
- 登录FusionSphere OpenStack安装部署界面，选择“云化服务 > FusionSphere Openstack OM”，单击“同步域名”，在域名同步完成后等待10分钟左右查看告警是否自动清除：
如果告警自动消除，任务结束。
如果告警没有自动清除，请按照3.b.ii排查。
- 检查是否按照帐户一览表修改过密码：
是，请按照3.b.iii排查。
否，请联系技术支持工程师协助解决。
- 检查在按照“重置除数据库及rabbit帐户外的内部帐户密码”章节修改密码的过程中是否有步骤遗留：
如果有遗漏，按照该章节重新修改密码，修改完成后等待10分钟左右，查看告警是否自动清除，如果告警自动清除，任务结束。如果告警没有自动清除，请联系技术支持工程师协助解决。
如果没有遗漏，请联系技术支持工程师协助解决。
- 连接类型为tangram，检查OpenStack告警上报的配置是否正确。如果不正确，修改成正确的配置，具体操作方法请参见配置FusionSphere OpenStack告警上报章节，等待约5分钟后，查看告警是否自动清除。
- 是，任务结束。
- 否，按照以下操作排查处理。
- 参考数据库故障恢复指导排查数据库是否存在故障。
如果数据库正常，请按照3.d排查。
如果数据库存在故障，需要按照“数据库故障恢复指导”章节恢复数据库，在数据库恢复之后等待15分钟左右，查看告警是否自动清除。如果告警自动清除，任务结束。如果告警未清除，请按照3.d排查。
- 请使用“PuTTY”， 以“galaxmanager”用户，通过浮动IP登录Service OM服务器。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
- 执行以下命令，查看告警IP是否冲突。（在告警列表中，在此告警所在行单击告警名称，在弹出的窗口中查看告警详细信息，在区域“附加信息”中获取告警IP ）。
sudo arping -D 告警IP -c 5 -I eth1
如果只收到1个响应，需要按照3.e排查。
如果收到多个响应，说明IP冲突，请排查网络配置，确保网络处于正常状态后执行4。
- 登录OpenStack的后台，导入环境变量，具体操作请参见导入环境变量。执行以下命令检查告警服务的状态：
cps template-instance-list --service tangram omm-server | grep fault
如果结果不为空，说明告警服务故障，需要按照3.f尝试重启告警服务。
如果结果为空，说明告警服务正常，执行4。
- 执行命令尝试停止告警服务，命令如下：
cps host-template-instance-operate --action stop --service tangram omm-server
等待30秒执行以下命令尝试启动告警服务：
cps host-template-instance-operate --action start --service tangram omm-server
等待1分钟后再次检查告警服务状态。
如果正常，等待约5分钟后，查看告警是否自动清除，如果告警自动清除，任务结束。如果告警没有自动清除，执行4。
如果不正常，请联系技术支持工程师协助解决。
4. 询问维护人员，检查部件侧、Service OM的用户名、密码是否修改过，造成两边的密码不一致。
- 用户名密码错误，修改为正确的用户名及密码，重新连接。执行5。
- 用户名密码正确，执行6。
5. 等待约5分钟后，查看告警是否自动清除。
- 是，处理完毕。
- 否，执行6。
6. 请使用“PuTTY”， 以“galaxmanager”用户，通过浮动IP登录Service OM服务器。
7. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
8. 执行以下命令，查看connector进程是否normal状态。
galaxmanager status | grep connector
- 是，执行10。
- 否，执行9。
9. 执行如下命令，重启进程。
connector restart
10. 等待约5分钟后，查看告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。