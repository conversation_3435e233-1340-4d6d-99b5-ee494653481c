# 5.2.3.1.104 ALM-1200077 Dpdk lacp bond聚合失败

##### 告警解释
当LACP模式的DPDK bond协商失败时，系统产生告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200077 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
虚拟网络uplink端口网络业务中断。
##### 可能原因
- 对端交换机配置错误。
- 主机网卡和光模块不匹配导致的链路不通。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，查询网卡类型。
ovs-vsctl show
6. 根据告警附加信息中显示的bond名称，slave名称，获取异常的bond及slave信息。
- 如果5中的查询的网卡类型为hwbond，请执行如下命令查看EVS或OVS-DPDK网桥上的dpdk lacp bond端口lacp_status状态。
ovs-appctl hwoff/hwbond-show bond_name
- 如果5中的查询的网卡类型为dpdkbond，请执行如下命令查看EVS或OVS-DPDK网桥上的dpdk lacp bond端口lacp_status状态。
ovs-appctl dpdk-bond/show bond_name
- 如果lacp_status为active negotiated，请手动清除告警。
- 如果lacp_status为active configured，请执行7。
- 如果lacp_status为active disabled，请执行8。
7. 登录对端主机或者交换机检查trunk设置是否正确。
- 如果配置错误请修改配置正确后查看告警是否恢复。
- 如果配置正确，请执行9。
8. 查看本端网卡硬件状态指示，如指示错误请尝试插拔网线或更换硬件（如光模块），然后观察状态指示是否恢复正常。
- 如无法恢复，请联系硬件服务支持。
- 如指示恢复正常，请查看告警是否恢复，如告警不恢复，请执行9。
9. 请联系技术支持工程师协助解决。