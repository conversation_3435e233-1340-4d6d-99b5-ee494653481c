# 5.2.4.10.1 ALM-MOMaintenanceService_100106 证书即将过期

5.2.4.10.1.3 ALM-MOMaintenanceService_100106 证书即将过期
##### 告警解释
系统维护定时检测CA证书过期情况，当到期时间距离当前时间间隔小于等于30天时，产生此告警，告警级别为重要，当到期时间距离当前时间间隔小于等于7天时，告警级别为紧急。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100106 | 紧急/重要 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测证书所在节点的IP地址。 |
| 证书服务名称 | 被检测的证书服务名称。 |
| 证书即将过期天数 | 被检测的证书即将过期的天数。 |
##### 对系统的影响
CA证书过期不可用，影响业务功能的正常运行。
##### 可能原因
CA证书即将过期。
##### 处理步骤
1. 检查CA证书过期时间距离当前时间是否小于等于30天或小于等于7天。
- 使用PuTTY，登录告警发生所在节点。在该告警的“定位信息”列获取告警发生的节点IP。
默认帐户：sopuser，默认密码：*****。
- 执行如下命令，切换至ossadm帐户。
su - ossadm
默认密码：*****
- 执行openssl命令，查看CA身份证书和信任证书过期时间。例如，查看ca.cer证书的到期时间，执行如下命令。
openssl x509 -enddate -noout -in /opt/oss/manager/var/ca/ca.cer
回显信息如下，表示该证书到期时间为2032年6月10日，凌晨0点0分0秒 。
notAfter=Jun 10 00:00:00 2032 GMT
- 计算证书到期时间距离当前时间是否小于等于30天或小于等于7天。
- 是，执行2。
- 否，执行4。
2. 参见更换ManageOne的CA证书，更新CA证书。
3. 待凌晨一点自动执行证书过期检查之后，查看告警是否清除。
- 是，处理完毕。
- 否，执行4。
4. 请收集上述告警处理过程中的信息，联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
< 上一节