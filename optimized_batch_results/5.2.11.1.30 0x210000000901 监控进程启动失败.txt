# ********.30 0x210000000901 监控进程启动失败

##### 告警解释
在备份节点（[Node_Ip]）上微服务（[MicroService_Name]）的监控进程启动失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000901 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | eBackup备份节点IP |
| MicroService_Name | eBackup微服务名称 |
##### 对系统的影响
不涉及。
##### 可能原因
初始化配置失败。
##### 处理步骤
- 可能原因1：初始化配置失败。
- 使用PuTTY，登录告警上报的备份节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，切换到root帐户。
- 执行“cd /opt/huawei-data-protection/ebackup/microservice/ebk_XXX/conf”命令，进入微服务配置目录。“ebk_XXX”为微服务名称。
- 执行“ls -l srv_checked_processes.xml”命令，查看配置文件权限是否为600，所有者是否为hcpprocess:hcpmgr。
- 是，执行1.e。
- 否，执行“chmod 600 srv_checked_processes.xml”命令，设置文件权限为600；执行“chown hcpprocess:hcpmgr srv_checked_processes.xml”命令，设置文件所有者为hcpprocess:hcpmgr，处理结束。
- 执行“cat srv_checked_processes.xml”命令，查看配置项autoRestart的值是否为“yes”或“no”、配置项failedThreshold和failedPeriod的值是否在“0-1440”范围内、配置项onlyRunOnLeaderNode的值是否为“yes”或“no”。
- 是，执行1.f。
- 否，执行“vim srv_checked_processes.xml”命令，修改1.e中配置项的值为合法值，处理结束。
- 执行“cd /opt/huawei-data-protection/ebackup/microservice/ebk_XXX/script”命令，进入微服务脚本目录。“ebk_XXX”为微服务名称。
- 执行“ls -l srv_processes_monitor.sh”命令，查看脚本文件权限是否为550，所有者是否为root:hcpmgr。
- 是，请联系技术支持工程师协助解决。
- 否，执行“chmod 550 srv_processes_monitor.sh”命令，设置文件权限为550；执行“chown root:hcpmgr srv_processes_monitor.sh”命令，设置文件所有者为root:hcpmgr。处理结束。
##### 参考信息
无