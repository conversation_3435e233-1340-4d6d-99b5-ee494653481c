# *******.3 *******.3.2.2 查询被级联层cinder-scheduler日志

*******.3.2.2 查询被级联层cinder-scheduler日志
仅在创建空白卷和从镜像创卷时有需要查询被级联层cinder-scheduler日志，从备份和快照创卷不涉及。
##### 操作步骤
1. 使用PuTTY，以“Cascaded-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录被级联层FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
2. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
3. 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
4. 执行如下命令，查看所有cinder-scheduler控制节点。
cps template-instance-list --service cinder cinder-scheduler
5. 通过4中查询到的IP地址依次登录所有cinder-scheduler控制节点。
默认帐号：fsp，默认密码：*****
6. 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
7. 执行如下命令，通过request id查询每一个节点上的日志信息。
zgrep req_id /var/log/fusionsphere/component/cinder-scheduler/*
req_id 为7中查询到的request id。
8. 根据日志错误信息进行分析和处理。