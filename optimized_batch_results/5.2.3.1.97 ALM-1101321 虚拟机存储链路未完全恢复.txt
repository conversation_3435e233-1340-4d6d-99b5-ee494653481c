# 5.2.3.1.97 ALM-1101321 虚拟机存储链路未完全恢复

##### 告警解释
如果虚拟机挂载的磁盘有多条存储链路，当主机存储故障后，所有链路未完全恢复，则虚拟机启动时，系统产生此告警。
用户在计算节点确认链路恢复后，需要手动清除告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101321 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 可用分区名：产生告警的可用分区名称<br>主机名：产生告警的虚拟机所在主机名称<br>主机ID：产生告警的虚拟机所在主机ID<br>虚拟机名：产生告警的虚拟机名称<br>LUN_WWN：未完全恢复的存储磁盘WWN编号 |
##### 对系统的影响
可能会降低虚拟机运行的稳定性。
##### 可能原因
虚拟机所在的主机存储链路出现异常，未完全恢复。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行命令upadmin show vlun，根据告警信息中的LUN_WWN，查看命令执行结果中对应行的“No. of Paths”字段，查看显示的Paths数量是否与预期安装部署存储设备上的链路数一致。
- 是，根据ALM-6023 主机存储链路中断处理告警，处理完毕后，执行6。
- 否，执行7。
6. 手动清除告警后，查看告警是否继续上报。
- 是，执行7。
- 否，处理完成。
7. 请联系技术支持工程师协助解决。
##### 参考信息
无。