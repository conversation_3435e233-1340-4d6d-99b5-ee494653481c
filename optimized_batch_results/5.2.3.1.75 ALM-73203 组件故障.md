# 5.2.3.1.75 ALM-73203 组件故障

##### 告警解释
OpenStack周期（默认为90s）检查每台主机上的组件状态，如果该主机存在状态不正常的组件，产生此告警。如果在主机改造或者配置变更过程中上报此告警且很快告警自动恢复，可以忽略。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73203 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 详细信息：<br>component fault：组件故障 。<br>host：产生告警的主机ID。<br>components：产生告警的组件名称。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。 |
##### 对系统的影响
如果主机上存在状态不正常的组件，则该主机不能正常的承载业务。
##### 可能原因
- 系统资源不够，导致组件进程退出。
- 组件内部故障，组件无法工作。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
检查系统资源
3. 执行以下命令，查看是否系统资源不够。
grep -i 'out of memory' /var/log/messages |grep -v grep
回显如下所示：
2019-01-03T21:09:41.543181+08:00|err|kernel[-]|[63624.912407] Memory cgroup out of memory: Kill process 26511 (java) score 51 or sacrifice child
查看回显结果是否包含out of memory：
- 是 ，则表明系统资源不够，请联系技术支持工程师协助解决。
- 否，执行4。
检查组件内部故障
4. 先检查是否有如下故障，如果有，请先排查如下故障：
- ALM-6010 NTP服务器与外部时钟源时间差超过阈值
- ALM-6015 NTP服务器与外部时钟源网络故障或外部时钟源故障
- ALM-6017 主机状态异常
- ALM-6022 主机与NTP服务器心跳状态异常
- ALM-6028 本地NTP客户端与本地NTP服务器时间差超过60秒
- ALM-73010 文件系统故障告警
- ALM-73401 rabbitmq服务故障
5. 根据告警附加信息中的components参数获取到当前故障的组件名称，是否包含在如下组件中，如果有，参考如下故障：
- gaussdb组件故障处理方法：gaussdb组件故障处理
- keystone组件故障处理方法：keystone组件故障处理
- rabbitmq组件故障处理方法：rabbitmq组件故障处理
- nova-compute组件故障处理方法：nova-compute组件故障处理
- nova-api组件故障处理方法：nova-api组件故障处理
- nova-scheduler组件故障处理方法：nova-scheduler组件故障处理
- nova-conductor组件故障处理方法：nova-conductor组件故障处理
- nova-proxy组件故障处理方法：nova-proxy组件故障处理
- fc-nova-compute组件故障处理方法：fc-nova-compute组件故障处理
- vmware-nova-compute组件故障处理方法：vmware-nova-compute组件故障处理
- memcache组件故障处理方法：memcache组件故障处理
- cinder-volume组件故障处理方法：cinder-volume组件故障处理
- cinder-proxy组件故障处理方法：cinder-proxy组件故障处理
- nova-compute-ironic组件故障处理方法：nova-compute-ironic组件故障处理
- glance组件故障处理方法：glance组件故障处理
- neutron-server组件故障处理方法：neutron-server组件故障处理
- neutron-l3-service-agent组件故障处理方法：neutron-l3-service-agent组件故障处理
- neutron-vrouter组件故障处理方法：neutron-vrouter组件故障处理
- neutron-l3-nat-agent组件故障处理方法：neutron-l3-nat-agent组件故障处理
- neutron-cascading-proxy组件故障处理方法：neutron-cascading-proxy组件故障处理
- neutron-openvswitch-agent组件故障处理方法：neutron-openvswitch-agent组件故障处理
- neutron-garbage-collector组件故障处理方法：neutron-garbage-collector组件故障处理
- neutron-l3-dummy-agent组件故障处理方法：neutron-l3-dummy-agent组件故障处理
- neutron-dvr-compute-agent组件故障处理方法：neutron-dvr-compute-agent组件故障处理
- neutron-dhcp-agent组件故障处理方法：neutron-dhcp-agent组件故障处理
- neutron-metadata-agent组件故障处理方法：neutron-metadata-agent组件故障处理
- neutron-sriov-nic-agent组件故障处理方法：neutron-sriov-nic-agent组件故障处理
- neutron-evs-agent组件故障处理方法：neutron-evs-agent组件故障处理
- neutron-vc-vswitch-agent组件故障处理方法：neutron-vc-vswitch-agent组件故障处理
- neutron-l3-agent组件故障处理方法：neutron-l3-agent组件故障处理
- neutron-ipv6-vrouter组件故障处理：neutron-ipv6-vrouter组件故障处理
- neutron-elb-proxy组件故障处理：neutron-elb-proxy组件故障处理
- neutron-nat-gw-dataplane组件故障处理：neutron-nat-gw-dataplane组件故障处理
- neutron-nat-gw-data-agent组件故障处理：neutron-nat-gw-data-agent组件故障处理
- neutron-ipv6-service-agent组件故障处理：neutron-ipv6-service-agent组件故障处理
- neutron-ngfw-agent组件故障处理：neutron-ngfw-agent组件故障处理
- neutron-fw-proxy组件故障处理：neutron-fw-proxy组件故障处理
- neutron-ngfw-vpn-agent组件故障处理：neutron-ngfw-vpn-agent组件故障处理
- ceilometer-agent-compute组件故障处理方法：ceilometer-agent-compute组件故障处理
- ceilometer-agent-hardware组件故障处理方法：ceilometer-agent-hardware组件故障处理
- mongodb组件故障处理方法：mongodb组件故障处理
- ceilometer-collector组件故障处理方法：ceilometer-collector组件故障处理
6. 如果以上步骤还解决不了问题，请联系技术支持工程师协助解决。
##### 参考信息
无。