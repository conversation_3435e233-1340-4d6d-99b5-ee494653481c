# 5.2.11.2.16 1023298 组件状态异常

##### 告警解释
系统周期性检查每个节点上的组件状态，如果该节点存在状态不正常的组件，产生此告警；当该组件状态变成正常后，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023298 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 组件 | 异常的组件。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
如果节点上存在状态不正常的组件，则该节点不能正常的承载业务。
##### 可能原因
组件进程异常退出。
##### 处理步骤
- 可能原因：组件进程异常退出。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下操作查询告警节点的组件状态。
show_service --node 节点名
查看回显信息中是否存在状态为“fault”的组件。
- 是，请执行1.d。
- 否，请执行1.h。
- 执行如下操作停止故障组件。
stop_service --service 组件名
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.e。
- 否，请执行1.h。
- 执行如下操作重新启动故障组件。
start_service --service 组件名
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.f。
- 否，请执行1.h。
- 执行如下操作重新查询告警节点的组件状态。
show_service --node 节点名
查看回显信息中是否存在状态为“fault”的组件。
- 是，请执行1.d。
- 否，请执行1.g。
- 等待一分钟，查看告警是否恢复。
- 是，处理结束。
- 否，请执行1.h。
- 联系技术支持工程师协助解决。
##### 参考信息
无。