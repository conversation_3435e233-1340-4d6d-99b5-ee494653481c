# 5.2.4.3.2 ALM-100551 LVS中断服务告警

##### 告警解释
MORCLVSService服务会定期检测LVS的运行状态，如果检测到LVS没有启动并重新启动失败时，产生该告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 100551 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 发生告警的云服务名称。 |
| 服务 | 发生告警的服务名称。 |
| 节点名称 | 发生告警的服务名称 |
| IP地址 | 发生告警的服务所在服务器的IP地址。 |
##### 对系统的影响
告警发生之后，依赖LVS的SNMP告警无法上报。
##### 可能原因
- 系统错误或系统文件损坏。
- MORCLVSService服务部署的节点有其他的服务在使用keepalived。
keepalived用于倒换LVS服务部署节点的主备状态。
##### 处理步骤
1. 查看MORCLVSService服务部署节点的“/var/log/messages”日志中回显信息是否出现关键字“error”。
- 使用PuTTY，通过regionAlias-ManageOne-Service01的IP地址登录到service01节点。请在参考信息中查询节点对应的IP
默认帐号：sopuser，默认密码：*****
- 执行如下命令，切换到root帐号。
sudo su - root
默认密码：*****
- 执行如下命令，查看回显信息中是否出现关键字“error”。
cat /var/log/messages | grep "*error*"
- 是，请联系技术支持工程师协助解决。
- 否，请执行2。
2. 执行如下命令检查该节点是否有其他服务运行keepalived。
ps -ef | grep keepalive | grep -v "RCLVSService/lvs_install" | grep -v "grep" | awk '{print $2}'
如果有回显产生，则表明有其他服务运行keepalived。
- 是，请联系技术支持工程师协助解决，将MORCLVSService服务与其他服务分不同节点部署，MORCLVSService服务部署的节点将不再有其他的服务使用keepalived。
- 否，请联系技术支持工程师协助解决，重新部署MORCLVSService服务。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
以下介绍查找节点对应的管理IP地址的方法。
3. 启动浏览器，在地址栏中输入https://部署面的客户端登录IP地址:31945，按“Enter”。
4. 输入用户名、密码，单击“登录”。
5. 在主菜单中，选择“产品 > 系统监控”，在“系统监控”页面左上方，光标移至并选择对应的产品。然后进入“系统监控”页面的“节点”页签。
6. 在“节点名称”列查找待查询管理IP地址的节点。
7. 单击节点名称，在节点详情页面上方的IP地址即为该节点的管理IP地址。
< 上一节