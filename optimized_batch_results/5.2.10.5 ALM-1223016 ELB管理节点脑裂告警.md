# 5.2.10.5 ALM-1223016 ELB管理节点脑裂告警

##### 告警解释
ELB API每10秒查询备节点状态，如果主备节点都处于主状态，上报ELB API脑裂异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223016 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
对现有业务无影响，新下发的ELB业务会出现配置异常。
##### 可能原因
两个ELB管理节点之间keepalived心跳中断，仲裁无法对其感知，导致ELB管理节点出现双主情况。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：
- 对端地址
- 本端地址
5. 使用PuTTY，登录ELB管理节点。
登录地址：4中查出的本端地址和对端地址。
默认帐户：elb
默认密码：*****。
6. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
7. 分别在这两个节点执行以下命令，查看两个节点是否都为master。
cat /etc/keepalived/status
命令执行后回显如下信息：
[root@localhost elb]# cat /etc/keepalived/status
master
- 若一个节点为master，另一个节点为backup，则管理节点进程正常，请执行9。
- 若两个节点都为master，则进程不正常，继续执行8。
8. 使用PING命令查看两个管理节点之间是否连通。
- 是，请联系技术支持工程师协助解决。
- 否，请解决网络问题后执行9。
9. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节