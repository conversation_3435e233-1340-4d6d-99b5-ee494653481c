# ********.63 0x10E01C000F 浮动IP服务异常

##### 告警解释
在HA节点（IP：[Node_Name]）上浮动IP服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000F | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能引起外部对接业务中断。
##### 可能原因
- 管理平面网卡异常。
- 浮动IP冲突。
##### 处理步骤
- 可能原因1：管理平面网卡异常。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器 > HA管理 > 修改HA参数”，进入“修改HA参数”界面，查看配置的浮动IP地址。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行1.f；如果是IPv6，执行1.i。
- 执行“ifconfig”命令，查看是否存在“mgr”网卡。
- 是，执行2。
- 否，执行1.g。
- 执行ifconfig 管理平面网卡名称 up命令，查看是否可以将该网卡启动。
如：执行ifconfig mgr up命令，其中mgr为管理平面的网卡名称。
- 是，执行1.h。
- 否，请联系技术工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，2。
- 执行“ifconfig”命令，查看浮动IP地址，是否在管理平面网卡展示的信息内。
- 是，执行2。
- 否，请联系技术支持工程师协助解决。
- 可能原因2：浮动IP冲突。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 浮动IP地址”，如果是IPv6，执行“ping6 浮动IP地址”，检查是否可以ping通。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器 > HA管理 > 修改HA参数”，进入“修改HA参数”界面，更换浮动IP。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无