# 5.2.6.4.2 ALM-1101312 ECS连接FSP失败

##### 告警解释
ECS服务连接fsp失败时产生此告警，可能原因为ECS的URL配置不正确，需要变更该节点的URL配置。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101312 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源设备名称 | 来源设备名称 | 产生告警信息的设备名称 |
| 监控系统名称 | 监控系统名称 | 对接系统的类型 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 发生时间 | 发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | Service | 产生此告警的服务名 |
| 附加信息 | 云服务 | 云服务名 |
| 附加信息 | 服务 | 服务名 |
| 附加信息 | Fsp组件 | 组件名 |
##### 对系统的影响
ECS服务无法正常使用。
##### 可能原因
ECS的URL配置不正确。
##### 处理步骤
1. 使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐户：fsp，默认密码：*****
2. 执行以下命令，输入root帐号密码，切换至root帐号，默认密码为*****。
sudo su root
3. 执行以下命令，导入环境变量。导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
source set_env
4. 执行以下命令查询nova安装部署时的地址。以下图为例，图示中的URL地址为nova的准确地址：
openstack endpoint list | grep nova
5. 使用PuTTY，依次以“CPT-SRV01”和“CPT-SRV02”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录两个节点。
默认帐户：apicom，默认密码：*****
6. 执行以下命令，输入root帐号密码，切换至root帐号，默认密码为*****。
sudo su root
7. 执行以下命令查看ECS的配置文件：
vim /opt/apicom/tomcat/taskmgr/WEB-INF/classes/taskmgr-config.properties
在配置文件中查找“nova.endpoint.publicurl”配置项的值，即ECS配置的nova地址：
对比4中获取的地址，若不一致，参考4中获取的地址更新该配置项。
8. 执行如下命令重启apicom的进程：
sh /opt/apicom/tomcat/bin/shutdown.sh
sh /opt/apicom/tomcat/bin/startup.sh
9. 等待5分钟后，查看告警是否自动清除。
- 是，操作处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。