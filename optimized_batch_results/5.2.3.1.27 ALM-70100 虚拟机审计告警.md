# 5.2.3.1.27 ALM-70100 虚拟机审计告警

##### 告警解释
当执行系统审计时发现存在非正常状态的虚拟机或CPS主机已删除，主机的nova-compute服务残留，或nova数据库有大于一小时未提交事务时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70100 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务名称<br>服务：产生告警的服务名称 |
| 附加信息 | 详细信息：告警的详细信息 |
| IP地址/URL/域名 | 产生告警的主机IP地址或者URL地址或者域名 |
##### 对系统的影响
- 此告警产生时，系统中存在不正常状态的虚拟机。影响系统对虚拟机的管理。
- 野虚拟机会一直占用计算资源和网络资源。
- 假虚拟机是可以明确查询到的虚拟机，但是在对应的主机上却不存在，给用户显示错误的虚拟机信息。
- 所在主机不一致的虚拟机可能无法使用。
- 处于中间状态的虚拟机无法使用，且占用系统资源。
- 状态不一致的虚拟机使租户对虚拟机的操作受限。
- 虚拟机冷迁移处于中间态以及冷迁移受主机影响的虚拟机使得租户不能维护。
- 属性不一致的虚拟机可能是已无法使用的虚拟机。
- nova-compute服务残留会呈现一个不可用的服务。
- nova数据库中存在大于一小时未提交的事务，会占用数据库连接，导致系统可用的数据库连接变少，处理其他正常事务缓慢或失败。
- 数据库quota配额表中的已使用配额与实际使用的数据不一致，可能导致租户创建虚拟机因资源受限而失败。
##### 可能原因
- 系统存在野虚拟机。
- 系统存在假虚拟机。
- 系统存在状态处于中间态的虚拟机。
- 系统存在所在主机不一致的虚拟机。
- 系统存在虚拟机冷迁移处于中间态。
- 系统存在冷迁移受主机影响虚拟机。
- 系统存在虚拟机属性不一致的虚拟机。
- 系统存在CPS主机已删除，主机的nova-compute服务残留。
- nova数据库中存在大于一小时未提交的事务。
- quota配额表的变动与虚拟机等的变动不在事务中保证，存在不一致风险。
- 当创建、resize、删除虚拟机等流程中间，发生网络异常等偶现性的问题时，会导致实际使用的配额与quota配额表不一致。
请在告警的详细信息中获取引发告警的具体审计问题，并参考处理步骤完成相应问题的处理。
##### 处理步骤
1. 获取告警详情中“附加信息”参数中的“详细信息”取值，并参考表1，获取对应的审计报告名称。
| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| audit_orphan_vms | orphan_vm.csv |
| audit_invalid_vms | invalid_vm.csv |
| audit_host_changed_vms | host_changed_vm.csv |
| audit_stucking_vms | stucking_vm.csv |
| audit_diff_state_vms | diff_state_vm.csv |
| audit_stucking_migrations | cold_stuck.csv |
| audit_host_invalid_migrations | host_invalid_migration.csv |
| audit_diff_property_vms | diff_property_vm.csv |
| audit_nova_service_cleaned | nova_service_cleaned.csv |
| nova_idle_transaction | nova_idle_transactions.csv |
| audit_nova_vcpus | nova_quota_vcpus.csv |
| audit_nova_memory_mb | nova_quota_memory_mb.csv |
| audit_nova_quota_instance | nova_quota_instance.csv |
2. 确定当前环境部署的场景，获取审计报告。
- Region Type I：
- 判断部署的场景是级联层还是被级联层的方法：在OpenStack首节点，执行命令cps productinfo-show，查看product_type的取值，cascading表示级联层，cascaded表示被级联层。
- 如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。
- 级联层：收集审计报告
- KVM虚拟化（被级联层）：收集审计报告
- Region Type II&Region Type III：
- FusionCompute虚拟化：收集审计报告
- KVM虚拟化：收集审计报告
3. 确定当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。
- Region Type I：
- 级联层：审计结果定位
- KVM虚拟化（被级联层）：审计结果定位
- Region Type II&Region Type III：
- FusionCompute虚拟化：审计结果定位
- KVM虚拟化：审计结果定位
4. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。