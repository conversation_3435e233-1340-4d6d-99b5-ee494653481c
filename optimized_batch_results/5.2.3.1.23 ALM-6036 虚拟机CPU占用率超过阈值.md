# 5.2.3.1.23 ALM-6036 虚拟机CPU占用率超过阈值

##### 告警解释
OpenStack按60秒周期检测虚拟机CPU占用率，当检测到虚拟机CPU占用率大于等于用户配置的告警阈值时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6036 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID。 |
| 附加信息 | 虚拟机名称：产生告警的虚拟机名称。<br>使用率：虚拟机当前cpu使用率。<br>阈值：当前阈值信息。 |
##### 对系统的影响
可能会造成Service OM界面虚拟机上部署业务运行速度慢。
##### 可能原因
虚拟机CPU占用率过高。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，展开此条告警详细信息，获取定位信息和附加信息，包括虚拟机ID、虚拟机名称。
3. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 导入环境变量。
7. 执行如下命令，查看该虚拟机在最近10min内的cpu占用率。
ceilometer sample-list -m cpu_util -l 10 -q resource=虚拟机ID
其中，虚拟机ID从告警定位信息中获取。
回显信息如下图所示。
8. 在Service OM界面，选择“监控 > 告警 > 告警设置”，进入“虚拟机告警阈值”界面，根据告警的虚拟机名称和虚拟机ID，查找对应的虚拟机阈值配置。
9. 根据7中获取的cpu占用率分布范围，检查对应虚拟机的CPU占用率阈值的设置是否过低。
- 是，执行10。
- 否，执行11。
10. 调整“虚拟机告警阈值”界面对应虚拟机的CPU占用率阈值大小，设置虚拟机CPU占用率阈值在偏移量范围内都大于虚拟机当前的CPU监控值，等待10分钟，检查告警是否清除。
- 是，处理完毕。
- 否，执行11。
11. 检查虚拟机的规格是否合理。
- 是，执行14。
- 否，执行13。
12. 选择“资源 > 计算资源 > 虚拟机”页面，单击对应虚拟机名称进入虚拟机详情页面。进入“配置”，单击规格属性后面的“配置”，单击“变更规格”，修改虚拟机的规格。
13. 等待10分钟左右，检查告警是否清除。
- 是，处理完毕。
- 否，执行14。
14. 请联系技术支持工程师协助解决。
##### 参考信息
无。