# 5.2.3.1.8 ALM-6019 主机内存占用率超过阈值

##### 告警解释
OpenStack周期（默认为300s）检测主机内存占用率，当检测到主机内存占用率大于等于系统设置的告警阈值（默认为99%）时，系统产生此告警。
支持自定义主机内存告警阈值，请在Service OM管理页面的告警设置中调整阈值。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6019 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 告警阈值：Minor：99%-100%，表示告警阈值。<br>主机名：产生告警的主机名。<br>内存总量：主机内存的大小。<br>已使用：当前主机内存占用率。<br>进程列表：部分占用率较高的pid。 |
##### 对系统的影响
可能会造成系统运行速度慢。
##### 可能原因
主机业务繁忙负载过重。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“节点类型”列，查看当前主机节点类型是否为控制节点。
- 是，执行5。
- 否，执行3。
3. 迁移该主机上的虚拟机到其他主机，具体操作请参见迁移虚拟机。
如果无主机可迁移，执行5。
4. 等待3分钟~4分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，执行5。
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。