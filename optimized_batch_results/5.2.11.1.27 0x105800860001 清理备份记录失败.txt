# 5.2.11.1.27 0x105800860001 清理备份记录失败

##### 告警解释
微服务（[MicroService_Name]）清理备份记录（备份ID：[backupId]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800860001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| backupId | 备份ID |
##### 对系统的影响
不涉及。
##### 可能原因
网络中断。
##### 处理步骤
- 可能原因1：网络中断。
- 参考登录eBackup服务器登录Manager。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping OpenStack控制节点IP或域名命令，如果是IPv6，执行ping6 OpenStack控制节点IP或域名命令，测试Manager与OpenStack控制节点的网络是否连通。
- 是，执行1.d。
- 否，执行1.h。
- 使用PuTTY，登录任意OpenStack控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行source set_env命令导入环境变量。
- 执行cinder backup-delete 备份id命令删除快照，检查执行结果是否成功。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 请联系机房管理员修复网络连通性，确保网络正常连接后查看告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无