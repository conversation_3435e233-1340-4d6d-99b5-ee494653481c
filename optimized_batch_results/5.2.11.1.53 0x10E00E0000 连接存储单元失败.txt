# ********.53 0x10E00E0000 连接存储单元失败

##### 告警解释
Proxy（IP：[Node_IP]）与存储单元（路径：[Brick_path]）已失去连接。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E00E0000 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_IP | 备份代理的IP地址。 |
| Brick_path | 存储单元的路径。 |
##### 对系统的影响
Proxy将不再运行需访问此存储单元的任务，除非连接恢复正常。
##### 可能原因
- Proxy与存储设备之间的网络连接中断。
- 网络性能差。
- 如果存储单元类型为S3，检查S3的AK和SK是否正确。
- Manager或Server没有权限访问共享存储。
##### 处理步骤
- 可能原因1：Proxy与存储设备之间的网络连接中断。
- 使用PuTTY，通过告警上报的IP地址登录Proxy节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping 存储单元的IP地址，如果是IPv6，执行ping6 存储单元的IP地址，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络，确保网络连接正常。
- 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证备Proxy和存储设备间通信稳定，处理结束。
- 否，执行3。
- 可能原因3：如果存储单元类型为S3，检查S3的AK和SK是否正确。
- 使用PuTTY，通过告警上报的IP地址登录Proxy节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cd /opt/huawei-data-protection/ebackup/sbin命令，进入“sbin”目录；
- 执行export LD_LIBRARY_PATH=/opt/huawei-data-protection/ebackup/libs命令，导入LD_LIBRARY_PATH环境变量。
- 执行./uds_plug-in TestBucket S3存储IP地址 桶名 AK SK命令，查看AK、SK以及桶名是否正确。
- 是，4。
- 否，请联系管理员获取正确的AK、SK以及桶名，重新配置存储单元。
- 可能原因4：Manager或Server没有权限访问共享存储。
- 参考登录eBackup服务器登录eBackup-Manager和Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
- 执行chown hcpprocess:hcpmgr /opt/huawei-data-protection/ebackup/db_bak命令和ls /opt/huawei-data-protection/ebackup/db_bak命令，检查该目录能否更改目录权限和正常访问。
- 是，联系技术支持工程师协助解决。
- 否，请联系存储设备管理员，开通Manager或Server对存储设备的访问权限。
当为NFS存储时，确保存储侧不限制eBackup服务器root用户的权限，使该用户对NFS共享目录具有完全控制权限。
当为CIFS存储时，确保该用户对CIFS共享目录具有完全控制权限。
当为S3存储时，确保eBackup对S3存储的桶有完全控制权限。
##### 参考信息
无