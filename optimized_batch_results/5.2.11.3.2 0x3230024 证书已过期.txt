# ********.2 0x3230024 证书已过期

##### 告警解释
网元的证书已过期，导致证书校验失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230024 | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 网元IP地址 | 网元证书所在节点的IP地址 |
| 服务器IP地址 | 服务器的IP地址 |
##### 对系统的影响
存在对端被仿冒风险。
##### 可能原因
证书已过期。
##### 处理步骤
1. 通过告警详细信息当中的服务器IP地址，以DRManager帐号登录到服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
2. 执行su - root命令切换到root帐号。
默认帐号：root，默认密码：*****。
3. 执行命令cd /opt/BCManager/Runtime;./jre6.0.18/bin/keytool -list -v -keystore ./LegoRuntime/certs/bcm.keystore检查根证书是否过期（密钥库默认密码：BCM@DataProtect123）。
回显中“Valid from”行对应的“until”是否已经超过当前日期。
- 如果已超过，表示证书已经过期，请记录过期证书对应的“Owner”行信息。请转4。
- 如果未超过，请转6。
4. 登录ManageOne运维面，通过“系统管理 > 统一证书”进入统一证书管理界面。在所有区域中查找主体信息与3中记录的“Owner”行信息相同的证书，并单击证书右侧的“更新”完成更新操作。
5. 通过ManageOne运维面的快速访问“eReplication”进入BCManager服务管理界面，在BCManager界面选择“资源 > localServer > FusionSphere”界面，选择 Openstack组件，单击“刷新”，查看告警是否清除。
- 是，流程结束。
- 否，请转6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。