# 5.2.3.1.114 ALM-1507002 RabbitMQ资源使用率超过阈值

##### 告警解释
rabbitmq资源使用率超过阈值后，系统会产生此告警。
系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
- 紧急：80% <= 使用率阈值
- 重要：70% <= 使用率阈值< 80%
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1507002 | 紧急/重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>实例名：产生告警的服务所在的实例名。 |
| 附加信息 | 告警信息：告警的类型及具体的参数。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>云服务：产生告警的云服务。<br>微服务：产生告警的微服务名称。<br>阈值：资源使用率阈值。 |
##### 对系统的影响
此告警产生，组件对应资源可使用量较少，长期持续会造成业务无法正常进行。
##### 可能原因
主机业务繁忙负载过重。
##### 处理步骤
1. 告警是否自动清除。
- 是，任务结束。
- 否，执行2。
2. 根据附加信息找出超过阈值告警项。
- 如果是fd_use_rate，proc_use_rate，sockets_use_rate使用率超过阈值，请联系技术支持工程师协助解决。
- 如果是mem_use_rate，内存水位线(memory_high_watermark)超过阈值，执行3。
- fd_use_rate：文件描述符(file descriptor)使用率。
- proc_use_rate：erlang进程数使用率。
- sockets_use_rate：socket数使用率。
3. 在告警附加信息中获取发生告警主机ID。
4. 以"fsp"用户到登录Openstack控制节点，切换到"root"用户并导入环境变量，执行下面命令,根据告警主机ID查看对应管理IP地址。
su - root
cps host-list | grep 主机ID
5. 使用管理IP地址登录发生告警主机。
6. 执行命令cps template-params-show --service rabbitmq $rabbitmq_template，其中$rabbitmq_template的取值根据告警主机对应的RabbitMQ是否为分库以及是哪个分库确定，对应关系请参见表1，后续步骤针对$rabbitmq_template 变量的取值相同处理。
结果如下图所示,查看配置项内存水位线(memory_high_watermark)的值，默认为空。
- 确认当前系统主机及虚拟机规模，如果内存水位线值小于表2对应规模下的水位线值memory_size或者为空，请执行如下命令(其中$memory_size为memory_size的值，设置时需带上单位，如：16G)，完毕后执行10。
cps template-params-update --service rabbitmq $rabbitmq_template --parameter memory_high_watermark=$memory_size
cps commit
请确保修改的内存水位线值不能大于主机物理内存的40%，如果物理内存不足，请先扩容物理内存才能执行下面的变更操作。
- 如果内存水位线值大于或者等于表2对应规模下的水位线值，请联系技术支持工程师协助解决。
7. 在发生告警的rabbitmq主机所在节点执行df -h命令，结果如下图所示。
查看上图中$rabbitmq_template分区(示例为：/opt/fusionplatform/data/rabbitmq)可用空间大小，记为size，其中$rabbitmq_template的取值根据告警主机对应的RabbitMQ是否为分库以及是哪个分库确定，对应关系见表1，后续步骤针对$rabbitmq_template变量的取值相同处理。
8. 执行命令cps template-params-show --service rabbitmq $rabbitmq_template，查看配置项内存水位线(memory_high_watermark)值，记为memory_size。
9. 执行命令cps hostcfg-list --type storage，查看rabbitmq所在主机磁盘分组信息如下图所示。
10. +---------+-----------------------+---------------------------------------------+--------------------+
11. | type    | name                  | hosts                                       | metadata           |
12. +---------+-----------------------+---------------------------------------------+--------------------+
13. | storage | control_group2_6A7554 | hostid:6A7554E9-410C-61A7-E811-6D0CD8E2B1D4 | grouptype:control, |
14. |         |                       |                                             | cpufamily:x86_64   |
15. |         |                       |                                             |                    |
16. | storage | control_group1_8B4BA4 | hostid:8B4BA4EF-98F8-3EB5-E811-5B2B7A47E62D | grouptype:control, |
17. |         |                       |                                             | cpufamily:x86_64   |
18. |         |                       |                                             |                    |
19. | storage | default               | default:all                                 |                    |
20. |         |                       |                                             |                    |
21. | storage | control_group0_304BA4 | hostid:304BA4EF-98F8-8F94-E811-852B82CB59EF | grouptype:control, |
22. |         |                       |                                             | cpufamily:x86_64   |
23. |         |                       |                                             |                    |
24. | storage | compute_group0        | hostid:8EA1E3EF-98F8-08BC-E811-5B2BA28835CA | grouptype:compute, |
25. |         |                       |                                             | cpufamily:x86_64   |
26. |         |                       |                                             |                    |
27. | storage | compute_group1        | hostid:F95330EF-98F8-D9A5-E811-DB289E423FB0 | grouptype:compute, |
28. |         |                       |                                             | cpufamily:x86_64   |
29. |         |                       |                                             |                    |
+---------+-----------------------+---------------------------------------------+--------------------+
记发生告警的rabbitmq主机hostid对应的name为group_name，如果rabbitmq主机hostid不在上图中，则分组名默认为default。
- 如果size小于memory_size大小的50%，执行如下命令扩容rabbitmq数据分区磁盘。
cps hostcfg-item-update --item logical-volume --lvname $rabbitmq_template --size $size --type storage $hostcfg_name
cps commit
其中$size为当前规模下对应的建议磁盘大小（见表2中磁盘分区大小列，设置时需带上单位，如：16g），$hostcfg_name为刚查询确认的hostcfg组的name字段（即group_name），若RabbitMQ所在节点hostid分属多个hostcfg组，则需针对每个$hostcfg_name执行此步骤。
磁盘分区大小建议为内存水位线的50%。
- 如果size大于memory_size大小的50%，请联系技术支持工程师协助解决。
30. 修改之后等待3-4分钟查看告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
| 表1 RabbitMQ template名称与分库情况对应关系 | 表1 RabbitMQ template名称与分库情况对应关系 |
| --- | --- |
| RabbitMQ分库情况 | RabbitMQ template名称 |
| 未分库 | rabbitmq |
| nova分库 | rabbitmq_nova |
| neutron分库 | rabbitmq_neutron |
| 表2 RabbitMQ参数配置规格 | 表2 RabbitMQ参数配置规格 | 表2 RabbitMQ参数配置规格 |
| --- | --- | --- |
| 规模 | 内存水位线（GB） | 磁盘分区大小（GB） |
| 100PM/1000VM | 32 | 16 |
| 256PM/2000VM | 32 | 16 |
| 512PM/5000VM | 50 | 25 |
| 1024PM/10000VM | 50 | 25 |
< 上一节