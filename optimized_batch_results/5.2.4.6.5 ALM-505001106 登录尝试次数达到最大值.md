# *******.5 ALM-505001106 登录尝试次数达到最大值

##### 告警解释
当三方系统接入用户通过某个IP地址在指定时间内连续登录失败次数达到最大值（默认10分钟内连续输错密码5次）时，产生该告警。
- 三方系统接入用户属于机机帐号，用于ManageOne运维面与第三方系统对接的用户。
- 三方系统接入用户通过调用接口登录。
- 用户登录尝试次数可在“帐号策略”中配置：
- 在主菜单中选择“系统管理 > 安全管理 > 安全策略”。
- 在左侧导航树中选择“帐号策略”。
- 配置“启用帐号锁定策略”。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 505001106 | 紧急 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 用户名 | 登录失败的用户名称。 |
| 终端 | 登录失败的IP地址。 |
| 限定时间段长度 | 限定时间段内连续输入错误密码次数达到最大值产生该告警。例如，参数“限定时间段长度”为10分钟，“限定时间段内连续输入错误密码次数”为5，表示10分钟内连续输入错误密码5次产生该告警。 |
| 限定时间段内连续输入错误密码次数 | 限定时间段内连续输入错误密码次数达到最大值产生该告警。例如，参数“限定时间段长度”为10分钟，“限定时间段内连续输入错误密码次数”为5，表示10分钟内连续输入错误密码5次产生该告警。 |
##### 对系统的影响
三方系统接入用户在锁定期间无法登录ManageOne运维面。
##### 可能原因
三方系统接入用户忘记登录密码或非法用户尝试密码登录，上报该告警提醒用户。
##### 处理步骤
该告警无需处理。
##### 告警清除
产生该告警后，三方系统接入用户被锁定，解锁后系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
< 上一节