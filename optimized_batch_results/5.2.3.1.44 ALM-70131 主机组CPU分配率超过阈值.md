# 5.2.3.1.44 ALM-70131 主机组CPU分配率超过阈值

##### 告警解释
OpenStack周期性（默认5分钟）检测主机组内CPU分配率，当检测到CPU已分配个数达到主机内CPU总个数的85%时，系统产生此告警。
当检测到CPU分配个数低于主机组内CPU总个数的70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70131 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：产生告警的主机组ID |
| 附加信息 | 云服务：产生告警的云服务名称<br>服务：产生告警的服务名称<br>主机组名称：产生告警的主机组名称<br>cpu总量：产生告警的主机组cpu总量<br>cpu使用量：产生告警的主机组cpu使用量<br>cpu复用比：产生告警的主机组cpu复用比<br>cpu门限量：产生告警的主机组cpu门限量 |
##### 对系统的影响
主机组CPU分配率过高，导致该主机组内虚拟机创建失败或者HA失败。
##### 可能原因
主机组总体资源过少、虚拟机个数过多或者存在故障主机。
##### 处理步骤
1. 通过告警信息得到该主机组内总CPU资源、已分配CPU资源以及上报告警的门限值，通过以下方式清除告警：
- 清理CPU资源，通过删除多余虚拟机方式进行清理。
- 扩容该主机组，增加该主机组中总的CPU资源，例如，通过往该主机组中增加空闲主机 。
- 参考更换主机，修复主机组中存在的故障主机。
2. 若主机组CPU分配率低于70%告警未清除，请联系技术支持工程师协助解决。
##### 参考信息
无。