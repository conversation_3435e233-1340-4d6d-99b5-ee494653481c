# 5.2.3.1.54 ALM-70402 整机快照残留

##### 告警解释
当执行系统审计，发现残留整机快照时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70402 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 云服务：产生告警的云服务<br>详细信息：告警的详细信息 |
##### 对系统的影响
残留的整机快照的镜像文件会占用存储系统的空间。
##### 可能原因
- 用户在后台手动删掉了整机快照对应的虚拟机。
- 其它未知错误导致的整机快照残留。
##### 处理步骤
1. 确定当前环境部署的场景，获取审计报告。
- Region Type I 级联层：收集审计报告
- Region Type II&Region Type III：
KVM虚拟化：收集审计报告
2. 确定当前环境部署的场景，参考“处理残留整机快照”章节，处理残留的整机快照。
- Region Type I 级联层：处理残留整机快照
- Region Type II&Region Type III：
KVM虚拟化：处理残留整机快照
3. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I 级联层：手动审计
- Region Type II&Region Type III：
KVM虚拟化：手动审计
4. 在Service OM界面，单击“服务列表 > Service OM > 集中运维 > 告警 > 告警列表”，查看告警是否清除。
- 是，处理完毕。
- 否，执行5。
5. 请联系技术支持工程师协助解决。
##### 参考信息
无。