# *******.3 *******.3.2.1 查看被级联层cinder-api日志

*******.3.2.1 查看被级联层cinder-api日志
- 使用PuTTY，以“Cascaded-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录被级联层FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
- 执行如下命令，根据级联层volume_id，查询被级联层的卷详情。
通常级联层和被级联层的volume_id是相同的。
cinder show 级联层volume_id
级联层volume_id即为在ManageOne运营面显示的磁盘ID。
- 若能够查询到卷详情，执行如下命令，查看该卷的host信息。
cinder show 级联层volume_id | zgrep host
- 若能够查询到host信息，即host字段不为None，如图1所示，则说明调度过程没有错误，进入查询被级联层cinder-volume日志。
- 若不能够查到host信息，则执行5。
图1 查询后端存储
- 如果未查询到卷详情信息，则说明级联层volume_id可能与被级联层volume_id不一致，或该卷已经被删除，此时需要查询数据库。
- 执行如下命令，查询数据库所在节点的IP地址，即“omip”对应的IP地址。如图2所示。
cps template-instance-list --service gaussdb gaussdb
图2 数据库信息
- 使用PuTTY，通过4.a中查询到的IP地址（“status”为“active”的IP地址）登录数据库主节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”。
- 执行如下命令，登录数据库。
su gaussdba
- 执行如下命令，进入数据库。
gsql -d cinder
数据库的默认密码为*****。
- 执行如下命令，查询被级联层的volume id，如图3所示。
select id from volumes where display_name = 'volume@级联层volume_id';
图3 查询数据库
- 执行如下命令，查询该被级联层卷对应的host信息。
select host from volumes where id = '被级联层volume_id';
- 若能够查询到host信息，即host字段不为None，则说明已经筛选到主机，进入步骤查询被级联层cinder-volume日志。
- 若不能够查到host信息，则执行5。
- 执行如下命令，查询被级联层所有cinder-api控制节点的IP地址，即“omip”对应的IP地址。
cps template-instance-list --service cinder cinder-api
- 通过5中查询到的IP地址依次登录所有cinder-api控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，查询每一个被级联节点上的cinder-api日志，以获取request id，request id的格式为req-xxx。
zgrep volume_id /var/log/fusionsphere/component/cinder-api/ *
volume_id 为4.g中查询到的被级联层volume id。
- 执行如下命令，依据request id，查询该节点cinder-api日志，获取报错信息。
zgrep rep-id /var/log/fusionsphere/component/cinder-api/*
req_id 为7中查询到的request id。
如未定位错误，则继续查询被级联层cinder-scheduler日志。