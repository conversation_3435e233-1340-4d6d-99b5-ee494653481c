# 5.2.3.1.26 ALM-6039 主机虚拟化域内存占用率超过阈值

##### 告警解释
OpenStack按60秒周期检测主机内存占用率，当检测到主机内存占用率大于系统设置的告警阈值时，系统产生此告警。
- 系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
- 紧急：90%≤主机虚拟化域内存占用率
- 重要：80%≤主机虚拟化域内存占用率<90%
- 自定义主机内存告警阈值，请在Service OM界面的“告警设置”中调整阈值。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6039 | 紧急/重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机 ID：产生告警的主机ID<br>主机名：产生告警的主机名称<br>复用内存总量：主机的内存总量<br>复用内存已使用量：主机的内存使用量<br>阈值：主机的内存告警阈值 |
##### 对系统的影响
造成已有虚拟机故障。
##### 可能原因
- 主机虚拟化域内存占用率告警阈值的设置过低。
- 主机上虚拟机负载过重。
##### 处理步骤
1. 在Service OM界面，选择“监控 > 告警 > 告警设置”。
进入“告警阈值”界面，查看主机虚拟化域内存占用率告警阈值的设置是否过低。
- 是，执行2。
- 否，执行4。
2. 调整告警阈值大小，具体操作请参见配置告警阈值。
3. 等待3分钟～4分钟，告警是否清除。
- 是，处理完毕。
- 否，执行4。
4. 迁移该主机上的虚拟机到其他主机，具体操作请参见迁移虚拟机。
如果无主机可迁移，执行6。
5. 等待3分钟～4分钟，告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。