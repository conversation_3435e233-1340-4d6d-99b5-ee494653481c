# 5.2.6.4.4 5.2.6.4.4 导入环境变量

5.2.6.4.4 导入环境变量
系统在安装完成后，默认打开鉴权模式，在执行CPS及OpenStack命令前，需要先导入环境变量才能操作。系统提供了导入环境变量的快捷脚本，可直接执行脚本即可。
##### 前提条件
- 已完成FusionSphere OpenStack第一台主机的安装。
- 已通过root用户登录主机。
##### 操作步骤
1. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
2. 选择鉴权方式。
目前提供四种方式可供用户选择，使用内置DC管理员的Keystone V3鉴权、CPS鉴权、Keystone V2鉴权、使用内置云管理员的Keystone V3鉴权。请根据以下详细介绍按照实际需求选择。
- 使用内置DC管理员的OpenStack Keystone V3鉴权，输入“1”，按“Enter”，并按提示输入“OS_USERNAME”的密码。
默认密码：“*****”。
密码输入成功后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。导入成功后系统使用内置DC管理员的Keystone V3鉴权。可以正常执行CPS命令和OpenStack命令。
在OpenStack的Mitaka版本中，Keystone命令行接口做了归一化处理，全部封装为OpenStack命令。在Identity service注册为V3路径时，原Keystone命令行在执行tenant/role/service相关命令时会存在兼容性问题。请优先使用Keystone V3鉴权，并使用同等功能的openstack project/role/service命令。
- 使用CPS的鉴权，输入“2”，按“Enter”，并按提示输入“CPS_USERNAME”的用户名和密码。
用户名：“cps_admin”，默认密码：“*****”。
输入完成后如果有cps host-list命令的自动回显信息，则表示环境变量导入成功。使用CPS鉴权导入环境变量后只能执行CPS相关命令，无法执行OpenStack命令，所以回显信息会在执行完nova list后提示“ERROR...”。
在未完全部署完成FusionSphere OpenStack之前，OpenStack鉴权还未启用，或Keystone鉴权异常时，可使用CPS鉴权。
- 使用OpenStack的Keystone V2鉴权，输入“3”，按“Enter”，并按提示输入“OS_USERNAME”的密码。
默认密码：“*****”。
密码输入成功后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。导入成功后系统使用Keystone V2鉴权。可以正常执行CPS命令和OpenStack命令。
为了兼容之前版本的鉴权方式，仍然支持Keystone V2鉴权，建议优先使用Keystone V3鉴权。
- 使用内置云管理员帐号的OpenStack Keystone V3鉴权，输入“4”，按“Enter”，并按提示输入“cloud_admin”（内置的云管理员帐号）的密码。
默认密码：“*****”。
密码输入成功后如果有cps host-list和nova list命令的自动回显信息，则表示环境变量导入成功。导入成功后系统使用内置云管理员帐号的Keystone V3鉴权。可以正常执行CPS命令和OpenStack命令。
- 在对接Service OM或ManageOne的情况下，请使用V3版本的鉴权方式。
- 如果执行的操作需要使用“cloud_admin”帐户权限（例如，使用PasswordManager命令重置GaussDB数据库帐户密码），请使用内置云管理员帐号的OpenStack Keystone V3鉴权。
- 如果导入环境变量时选择使用Keystone鉴权，但环境还未部署nova-api，将导致cps host-list回显成功，nova list回显异常，部署nova-api服务后即可正常使用nova相关命令。
3. 在多DC场景下，如果每个DC都部署了独立的glance，在每个DC导入环境变量时，还需要执行以下命令，导入该DC的OS_IMAGE_URL。
export OS_IMAGE_URL=https://image.az.dc.domainname.com:443
az.dc.domainname.com:443为该DC中设置的glance域名。
< 上一节