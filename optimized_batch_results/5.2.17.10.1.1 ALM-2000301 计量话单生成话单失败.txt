# 5.2.17.10.1 ALM-2000301 计量话单生成话单失败

5.2.17.10.1.1 ALM-2000301 计量话单生成话单失败
##### 告警解释
计量话单服务会在每个整点时刻过15分钟后生成话单文件。生成话单文件时，当数据源异常（数据源无法访问或者本身存在异常）会导致话单生成失败，服务会启动失败重试机制，当重试次数等于服务设定的阈值时，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000301 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域。 |
| 定位信息 | 云服务 | 产生告警信息的云服务。 |
| 附加信息 | 云服务 | 产生告警信息的云服务。 |
| 附加信息 | 失败话单总数 | 生成话单失败的告警次数。 |
| 附加信息 | 失败资源类型 | 生成话单失败的资源类型。 |
| 附加信息 | 失败原因 | 生成告警的原因，例如“Query data from ceilometer error”。 |
| 附加信息 | 开始时间 | 开始时间对应的时间戳。 |
| 附加信息 | 结束时间 | 结束时间对应的时间戳。 |
| 附加信息 | 虚拟机名称 | 产生告警信息的虚拟机名称。 |
##### 对系统的影响
计费系统无法采集到本周期话单，影响正常计费。
##### 可能原因
- 数据源异常，数据源有两种：一种是ceilometer提供的；另外一种是非ceilometer的，即服务自身提供的。
- 上传通道异常，即连接ManageOne的SFTP异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 从告警的“定位信息”中获取当时告警所在Region。
5. 使用PuTTY，登录4中所获取Region下的PUB-SRV03节点。
PUB-SRV03节点IP地址请在导出的包含IP地址和参数信息汇总文件《xxx_export_all_CN.xlsm》的“2.1 工具生成的IP参数”页签搜索“PUB-SRV03”获取。
默认帐号：meteradmin，默认密码：*****。
6. 执行以下命令，手动生成话单文件并清除告警，检查回显信息中是否包含“all task successfully”。
sh /home/<USER>/meterticket-controller/bin/handleAlarm.sh
- 是，执行7。
- 否，请联系技术支持工程师协助解决。
7. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
如无计费需求，请参考配置屏蔽规则屏蔽本条告警。