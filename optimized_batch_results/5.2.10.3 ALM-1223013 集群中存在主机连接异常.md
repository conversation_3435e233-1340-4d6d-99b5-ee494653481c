# 5.2.10.3 ALM-1223013 集群中存在主机连接异常

##### 告警解释
ELB每10秒检测后端LVS、 Nginx或API节点，如果后端服务端口检测异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223013 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
影响现有业务性能或者业务无法处理。
##### 可能原因
管理节点健康检查时连接集群主机出现异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：对端地址
5. 使用PuTTY，登录连接异常的主机节点。
登录地址：LVS节点或Nginx节点的IP地址，节点IP地址为4中查出的对端地址。
默认账户：elb
帐户默认密码：*****。
6. 执行以下命令，查看nginx进程是否运行正常。
ps -ef|grep nginx|grep -v grep
5登录的是LVS节点，回显如下所示，表示nginx进程运行正常。
5登录的是Nginx节点，回显如下所示，表示nginx进程运行正常。
- 是，请执行9。
- 否，请执行7。
7. 执行以下命令，重新启动nginx进程。
- LVS节点
sh /usr/local/NSP/etc/lua/bin/restart_lvs.sh
- Nginx节点
sh /usr/local/NSP/etc/router/bin/restart_nginx.sh
8. 重启nginx进程不会对其他业务造成影响。
9. 执行以下命令，查看nginx进程是否恢复正常。
ps -ef|grep nginx|grep -v grep
- 是，执行9。
- 否，请联系技术支持工程师协助解决。
10. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。