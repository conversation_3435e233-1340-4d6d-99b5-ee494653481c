# 5.2.17.12.3 ALM-2002101 Monitor进程异常

##### 告警解释
- Monitor进程异常时，本节点业务不可用，系统产生此告警。
- 此告警提示系统故障或风险，需要处理。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2002101 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
产生告警的节点业务不可用。
##### 可能原因
Monitor配置错误，进程无法启动。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录告警节点。
默认帐号：arbiter，默认密码：*****。
6. 执行如下命令，重启Monitor进程。
systemctl restart arbitration-monitor
7. 执行如下命令，查看进程状态是否为“active (running)”。
systemctl status arbitration-monitor
根据不同的回显信息，执行相应步骤。
- 回显中包含“active (running)”，执行8。
- 回显中不包含“active (running)”，执行9。
8. 等待3分钟，查看告警是否自动清除。
- 是，处理完成。
- 否，执行9。
9. 收集如下日志文件。
/var/log/arbitration-monitor/arbitration-monitor.log
10. 请联系技术支持工程师处理。
##### 参考信息
无。