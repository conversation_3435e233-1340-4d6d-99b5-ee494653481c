# ********.6 1020783 启动消息队列服务失败

##### 告警解释
启动消息队列服务失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020783 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
消息队列服务启动失败，可能无法正常提供服务。
##### 可能原因
网络故障。
##### 处理步骤
- 可能原因：网络故障。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下操作查询rabbitmq组件状态。
show_service --service rabbitmq
查看回显信息中rabbitmq组件状态是否为“fault”。
- 是，请执行1.d。
- 否，请手工清除该告警，处理结束。
- 执行如下操作停止rabbitmq组件。
stop_service --service rabbitmq
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.e。
- 否，请执行1.g。
- 执行如下操作重新启动rabbitmq组件。
start_service --service rabbitmq
查看回显信息中，执行结果是否为“Successfully”。
- 是，请执行1.f。
- 否，请执行1.g。
- 执行如下操作重新查询rabbitmq组件状态。
show_service --node rabbitmq
查看回显信息中rabbitmq组件状态是否为“fault”。
- 是，请执行1.g。
- 否，请手工清除该告警，处理结束。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。