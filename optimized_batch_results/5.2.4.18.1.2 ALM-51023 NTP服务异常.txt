# 5.2.4.18.1 ALM-51023 NTP服务异常

5.2.4.18.1.2 ALM-51023 NTP服务异常
##### 告警解释
部署面默认间隔5分钟检测一次部署节点和产品节点上NTP服务器的状态，当连续3次检测到NTP服务器存在且状态为不生效时，产生此告警。当连续3次检测到NTP服务器存在且状态为正常时，告警将自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 51023 | 紧急 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点名称 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
| NTP地址 | 出现异常的NTP服务器IP地址。 |
##### 对系统的影响
各ManageOne服务如果无法从上级NTP服务器进行时间同步，将导致网络中各设备时间不准确，在执行备份恢复、操作日志记录等需要记录时间戳的操作时，有可能会出现因备份包恢复错误或日志获取错误等问题而影响业务处理效率。
##### 可能原因
- 节点间的时间同步关系异常。
- 多个NTP服务器之间的时间不一致。
- 节点的NTP服务异常（可能由于NTP配置文件异常或者进程运行异常导致）。
- NTP服务器异常。
##### 处理步骤
查看该告警中的可能原因信息。
- 若可能原因为节点间的时间同步关系异常，按照如下方法处理异常。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在部署面主菜单中选择“维护 > 时间管理 > 配置NTP”，在“配置NTP”页面单击“重新配置”，修复节点的时间同步关系。检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
- 若可能原因为多个NTP服务器之间的时间不一致，请用户自行调整NTP服务器的系统时间，保证所有NTP服务器的系统时间一致。检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
- 若可能原因为节点的NTP服务异常（可能由于NTP配置文件异常或者进程运行异常导致），则按照如下方法处理异常。
- 使用PuTTY工具以sopuser用户通过SSH方式登录产生告警的节点。获取节点IP地址的方法请参见如何查找节点对应的IP地址。
- 检查NTP服务是否已经启动。
> sudo su root
Password: root用户的密码
# service ntpd status
- 若系统回显信息中包含“active (running)”字样的信息，说明节点的NTP服务已启动。
- 若系统回显信息提示无ntpd service时，请执行以下命令检查NTP服务。
# ps -ef|grep ntp|grep -v grep
若系统回显信息中包含“ntpd”字样的信息，说明节点的NTP服务已启动。否则，说明节点的NTP服务未启动，执行以下命令启动NTP服务。
# service ntp start
如果系统提示以下信息，说明启动NTP服务成功，否则请联系华为技术支持工程师。
Starting network time protocol daemon (NTPD)                          done
- 否则，说明节点的NTP服务未启动，执行以下命令启动NTP服务。
# service ntpd start
- 执行以下命令，退出root用户。
# exit
- 检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
- 若可能原因为NTP服务器异常，则按照如下方法处理异常。
- 检查部署节点与NTP服务器网络连接是否正常。
- 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
- 检查NTP服务器是否能正常通信。
> sudo su root
Password: root用户的密码
# ntpdate -q NTP服务器的IP地址
如果回显包含如下显示，且信息中stratum的值为1~16， offset 不为0，delay 不为0，表示NTP能正常通信。否则联系华为技术支持工程师处理。
server NTP服务器的IP地址, stratum 6, offset -0.000010, delay 0.02599
- 执行以下命令，退出root用户。
# exit
- 检查NTP服务器是否正常，如果异常，请自行修复NTP服务器异常。
- 修复以后，建议等待半小时后，再检查告警是否清除。
- 是，处理结束。
- 否，请联系华为技术支持工程师处理。
##### 参考信息
无。