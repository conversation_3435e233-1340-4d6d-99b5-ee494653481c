# 5.2.3.1.103 ALM-1200076 主机设备端口丢包告警

##### 告警解释
当主机设备端口（如果是Trunk口，则按照Trunk为单位）的丢包率达到告警配置中配置的百分比后，系统会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200076 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>端口名称：产生告警的端口名称 |
| 附加信息 | 主机名：产生告警的主机名称<br>丢包率：产生告警的丢包率<br>阈值：产生告警的阈值<br>每秒包数：产生告警时的每秒收发包数 |
##### 对系统的影响
引起主机设备端口数据丢包或者网络不通。
##### 可能原因
- 网络IO压力达到极限。
- 用于转发的CPU资源不足。
- 网络配置错误，无法正确转发。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 根据告警信息中的端口名称，获取异常端口的名称（如果端口名称是ethx，表示端口没有组bond）。通过 "ip link | grep 端口名称" 查看端口，查看回显信息是否为空。
- 是，说明异常端口是用户态网桥上的端口，执行6。
- 否，说明异常端口是内核态网桥上的端口，执行7。
6. 执行如下命令查看端口状态。
ovs-vsctl get interface port_name link_state
- 如果连接不正常，请重新插拔网线。
- 如果连接正常，请执行如下命令收集具体丢包信息，执行9。
执行命令 ovs-vsctl iface-to-br port_name 获取 bridge_name，
执行命令 ovs-ofctl dump-ports bridge_name port_name 收集具体丢包信息。
7. 执行如下命令检查端口链路状态是否正常。
ethtool port_name
- 如果图中Link detected为no，请重新插拔网线，检查端口。
- 如果图中Link detected为yes，执行8。
8. 执行如下命令查看对应端口流量是否达到网卡支持的最大带宽。
sar -n DEV 1，命令回显如下所示：
- 如果端口流量已达到网卡支持的最大带宽，且流量为正常业务流量，请考虑更换更大带宽的物理网卡或者通过组bond的方式提高uplink带宽。
- 如果端口流量未达到网卡支持的最大带宽，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。