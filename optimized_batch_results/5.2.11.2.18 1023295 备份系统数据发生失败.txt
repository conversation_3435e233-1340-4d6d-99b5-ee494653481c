# ********.18 1023295 备份系统数据发生失败

##### 告警解释
备份系统数据发生失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1023295 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 节点名 | 产生告警的节点名。 |
| 节点IP | 产生告警的节点IP。 |
##### 对系统的影响
系统数据无法完成备份，影响系统的可靠性。
##### 可能原因
- 备份目录所在磁盘空间不足。
- 系统与外部备份服务器网络通信异常。
##### 处理步骤
1. 查看告警附加信息中的“节点IP”参数，确定发生故障的节点。
- 可能原因：备份目录所在磁盘空间不足。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行cd /opt/djbackup/db命令进入管理数据备份目录，删除目录下无用的备份数据。
- 可能原因：系统与外部备份服务器网络通信异常。
- 检查系统与外部备份服务器的物理网络连接是否正确。
- 是，请执行3.b。
- 否，请联系技术支持工程师协助解决。
- 检查外部备份服务器ftps服务是否正常：如果是ftp类型的服务器，使用curl -u {ftp_username}:{ftp_password} -k ftp://{ftp_ip}:{ftp_port} --noproxy {ftp_ip}查看是否能够获取到数据；如果是ftps类型的服务器，使用curl -u {ftp_username}:{decrypt_pwd} --ftp-ssl-reqd -k ftps://{ftp_ip}:{ftp_port}/ --noproxy {ftp_ip}。
- 是，请联系技术支持工程师协助解决。
- 否，恢复外部备份服务器ftps服务，完成后查看告警是否恢复。若仍未恢复，请联系技术支持工程师协助解决。
##### 参考信息
无。