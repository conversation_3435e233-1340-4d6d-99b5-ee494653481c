# *******.3 *******.3.1.1 查询对应卷信息

*******.3.1.1 查询对应卷信息
- Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行以下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
- 执行如下命令，查询卷详情。
cinder show volume_id
- 若返回“Service Unavailable (HTTP 503)”，如图1所示，说明cinder-api服务未启动。
- 执行如下命令，启动cinder-api服务，如图2所示，启动服务大概需要几十秒时间，请稍作等候。
cps host-template-instance-operate --action start --service cinder cinder-api
- 执行如下命令，查看服务的状态。
cps template-instance-list --service cinder cinder-api
- 若仍旧未启动，查看/var/log/fusionsphere/component/cinder-api/cinder-api_*.log日志，以及/var/log/fusionsphere/component/cinder-apiControl/cinder-apiControl_*.log启动日志，排查失败原因。
- 服务正常后，执行4。
图1 Service Unavialable
图2 启动cinder-api服务
- 若回显显示卷详情，则说明该卷未被删除。
执行如下命令，查询host的详细信息，如图3所示。
cinder show volume_id | zgrep host
图3 host信息
- 若回显信息类似图4，则说明该卷已被删除，执行以下步骤在数据库查看host详细信息。
图4 卷被删除后的回显信息
- 执行如下命令，查询数据库所在节点的IP地址，即“omip”对应的IP地址。如图5所示。
cps template-instance-list --service gaussdb gaussdb
图5 数据库信息
- 使用PuTTY，通过4.a中查询到的IP地址（“status”为“active”的IP地址）登录数据库主节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”。
- 执行如下命令，登录数据库。
su gaussdba
- 执行如下命令，进入数据库。
gsql -d cinder
数据库的默认密码为*****。
- 执行如下命令，查询该volume_id的host信息，如图6所示。
select host from volumes where id = 'volume_id';
图6 volume_id的host信息
通过上述操作可以获得host的信息。
- 若host信息不为“None”，说明调度成功已选择AZ。
Region Type I场景，参考排查被级联层OpenStack继续在FusionSphere OpenStack被级联层进行错误定位。
Region Type II，Region Type III场景，参考查询cinder-volume日志查询cinder-volumer日志进行错误定位。
- 若host信息为“None”，表示未选择到AZ，说明cinder-api或cinder-scheduler报错，继续执行查看cinder-api日志进行错误定位。