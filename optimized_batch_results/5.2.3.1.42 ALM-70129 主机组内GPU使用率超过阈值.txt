# 5.2.3.1.42 ALM-70129 主机组内GPU使用率超过阈值

##### 告警解释
OpenStack周期性（默认5分钟）检测主机组内GPU使用率，当检测到GPU使用个数达到主机组内GPU总个数的85%时，系统产生此告警。
当检测到GPU使用个数降低到主机组内GPU总个数的70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70129 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：gpu所在主机组ID<br>gpu类型：gpu类型 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机组名称：gpu所在的主机组名称<br>gpu总量：gpu的总个数<br>gpu使用量：已使用的gpu个数<br>阈值：gpu个数阈值 |
##### 对系统的影响
可能会造成之后创建GPU虚拟机时，由于GPU资源不够而创建失败。
##### 可能原因
主机组内GPU使用率过高。
##### 处理步骤
1. 通过告警信息得到GPU卡类型，建议新增相应类型的GPU卡。
参考用户指南中对应Region Type的“GPU加速型云服务器配置方案”章节，进行GPU配置。
2. 当GPU使用率低于70%时，告警是否恢复。
- 是，处理完毕。
- 否，执行3。
3. 如果上述操作出现问题，请联系技术支持工程师协助解决。
##### 参考信息
无。