# 5.2.4.18.2 ALM-101206 SSH管理通道故障

5.2.4.18.2.8 ALM-101206 SSH管理通道故障
##### 告警解释
当部署面连续4次检测（检测周期为60秒）到部署节点和产品节点之间的SSH连接异常时，将产生此告警。当部署节点和产品节点之间的SSH连接恢复正常，此告警将自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101206 | 紧急 | 处理出错告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
无法通过部署面对相应节点进行管理，影响相应节点的系统监控、备份恢复等功能。
##### 可能原因
- 产品节点状态异常。
- 部署节点和产品节点之间的网络连接异常。
- 产品节点的ossadm用户密码失效。
- 部署节点和产品节点之间的SSH信任关系损坏。
##### 处理步骤
1. 查看是否存在告警“101208 节点状态异常”，并且该告警和本告警的告警参数“主机”对应的节点名称相同。
- 是，处理异常告警，具体操作请参见ALM-101208 节点状态异常。
- 否，执行2。
2. 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
3. 执行以下命令，切换到ossadm用户。登录产生告警的节点。
su - ossadm
ossadm的默认密码为*****。
4. 执行以下命令，测试部署节点与产品节点的SSH连通性。
> ssh 告警参数中的节点IP地址
如果告警参数中“主机”对应的节点是部署节点，则登录部署节点连接任意一个产品节点测试SSH连通性。
- 如果不输入密码就可以成功登录节点，说明部署节点和产品节点的SSH互信关系正常，执行4.f。
- 如果显示以下回显信息，说明节点ossadm用户密码失效，请更新密码后再次测试部署节点与产品节点的SSH连通性。
WARNING: Your password has expired.
产品节点ossadm用户密码需要和部署节点ossadm用户密码保持一致。若产品节点与部署节点ossadm用户密码不一致，请参见《华为云Stack 6.5.1 安全管理指南》附件中的《华为云Stack 6.5.1 帐户一览表》帐户一览表进行修改。
- 如果要求输入ossadm用户密码，说明部署节点和产品节点的SSH互信关系异常，请执行以下操作恢复SSH互信关系。
- 按“Ctrl+c”结束当前操作。
- 执行以下命令，打开部署节点的“id_rsa.pub”文件。
> vi <ossadm用户的家目录>/.ssh/id_rsa.pub
- 将“id_rsa.pub”文件中的内容复制到本地，复制完成后，按“Esc”并输入“:q!”关闭“id_rsa.pub”文件。
- 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。登录待恢复SSH信任关系的节点。
su - ossadm
ossadm的默认密码为*****。
- 执行以下命令，打开待恢复SSH信任关系的节点的“authorized_keys”文件。
> vi <ossadm用户的家目录>/.ssh/authorized_keys
以上命令会打开vi编辑器，打开vi编辑器后，按“i”并将3.c中获取的内容复制并添加到“authorized_keys”文件的末端。
- 配置完成后，按“Esc”并输入“:wq!”保存“authorized_keys”文件。操作完成后，并再次测试部署节点与产品节点的SSH连通性。
5. 查看告警是否清除。
- 是，处理完毕。
- 否，请联系华为技术支持工程师处理。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
6. 产生该告警的节点名称发生了变化。
7. 产生该告警的站点名称发生了变化。
8. 产生该告警的服务器不被监控了。
##### 参考信息
无。