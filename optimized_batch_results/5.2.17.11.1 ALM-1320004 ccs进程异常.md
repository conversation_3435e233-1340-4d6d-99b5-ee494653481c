# *********.1 ALM-1320004 ccs进程异常

##### 告警解释
自定义监控项，系统每隔5分钟检查一次CCS进程是否正常，否则产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1320004 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
产生告警的CCS主进程异常，导致上层云服务（例如ECS、EVS、VPC等服务）无法通过CCS打标签，影响云服务正常使用，需尽快处理告警。
##### 可能原因
CCS服务主进程异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的主机节点IP地址登录产生告警的节点。
默认帐号：ccs，默认密码：*****。
6. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行以下命令，启动服务进程。
sh /etc/ccs/init-script/start_ccs_service.sh -M VM
9. 执行以下命令，调用服务整体状态检查脚本，检查CCS业务是否健康。
curl -i -k https://$IP:8889
参数“$IP”是4中获取的主机节点IP。
回显信息中包含“200 OK”，表示CCS业务健康。
- 如果CCS业务健康，等待5分钟，查看告警是否清除。如仍未清除，请联系技术支持工程师协助解决。
- 如果CCS业务异常，请直接联系技术支持工程师协助解决。
##### 参考信息
无。