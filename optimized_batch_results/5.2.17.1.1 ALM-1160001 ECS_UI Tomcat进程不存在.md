# 5.2.17.1.1 ALM-1160001 ECS_UI Tomcat进程不存在

##### 告警解释
告警节点的tomcat进程不存在时触发该告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1160001 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 对接系统的类型。 |
| 来源系统 | 对接系统的名称。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 最后发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
异常节点不能正常提供服务。
##### 可能原因
tomcat进程异常等。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”，在界面上方导航栏选择“集中告警 > 当前告警”，在告警的定位信息中获取进程异常的节点IP。
3. 使用“PuTTY”，登录异常节点IP对应的CONSOLE01或CONSOLE02虚拟机节点。
默认帐号：ecm，默认密码：*****。
4. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
5. 执行以下命令，查看tomcat进程是否存在。
ps -ef | grep tomcat | grep ecm
- 是，执行8。
- 否，执行6。
回显如下信息说明存在：
[ecm@CONSOLE01 ~]$ ps -ef | grep tomcat | grep ecm
ecm      20092     1  0 14:58 ?        00:00:16 /opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager -Djdk.tls.ephemeralDHKeySize=2048 -Dorg.apache.catalina.security.SecurityListener.UMASK=0077 -Djava.endorsed.dirs=/opt/ecm/tomcat/endorsed -classpath /opt/ecm/tomcat/bin/bootstrap.jar:/opt/ecm/tomcat/bin/tomcat-juli.jar -Dcatalina.base=/opt/ecm/tomcat -Dcatalina.home=/opt/ecm/tomcat -Djava.io.tmpdir=/opt/ecm/tomcat/temp org.apache.catalina.startup.Bootstrap start
6. 通过命令vim /opt/ecm/tomcat/logs/catalina.out在日志最底部查看是否有ERROR字段。
- 是，请联系技术支持工程师协助解决。
- 否，执行步骤7。
7. 执行以下命令。
sh /opt/ecm/tomcat/bin/startup.sh
8. 等待10分钟，检查告警是否已自动清除。
- 是，告警处理完毕。
- 否，请联系技术支持工程师协助解决。
请务必在ecm用户下执行操作。
##### 参考信息
无。