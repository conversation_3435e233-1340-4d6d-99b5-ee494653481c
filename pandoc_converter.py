#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 pandoc 的Word到Markdown转换器
pandoc 是最强大的文档转换工具，支持几乎所有格式
"""

import subprocess
import os
import sys
import shutil
from pathlib import Path


class PandocConverter:
    def __init__(self, docx_path, output_dir="pandoc_output"):
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.image_dir = os.path.join(output_dir, "images")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)
    
    def check_pandoc_installation(self):
        """检查 pandoc 是否已安装"""
        try:
            result = subprocess.run(['pandoc', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.split('\n')[0]
                print(f"✅ 发现 {version}")
                return True
            else:
                return False
        except FileNotFoundError:
            return False
    
    def install_pandoc_instructions(self):
        """提供 pandoc 安装说明"""
        print("❌ 未找到 pandoc，请按以下方式安装：")
        print("\n🍎 macOS:")
        print("   brew install pandoc")
        print("\n🐧 Ubuntu/Debian:")
        print("   sudo apt-get install pandoc")
        print("\n🪟 Windows:")
        print("   从 https://pandoc.org/installing.html 下载安装包")
        print("\n📦 或使用 conda:")
        print("   conda install pandoc")
    
    def convert_to_markdown(self, output_filename="document.md"):
        """使用 pandoc 转换为 Markdown"""
        if not self.check_pandoc_installation():
            self.install_pandoc_instructions()
            return None
        
        print("使用 pandoc 开始转换...")
        
        try:
            output_path = os.path.join(self.output_dir, output_filename)
            
            # 构建 pandoc 命令
            cmd = [
                'pandoc',
                self.docx_path,
                '-t', 'markdown',  # 输出格式为 markdown
                '-o', output_path,  # 输出文件
                '--extract-media', self.output_dir,  # 提取媒体文件
                '--wrap=none',  # 不自动换行
                '--markdown-headings=atx',  # 使用 ATX 风格标题 (###)
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            
            # 执行转换
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ 转换成功!")
                print(f"📄 输出文件: {output_path}")
                
                # 检查是否有提取的媒体文件
                media_dir = os.path.join(self.output_dir, "media")
                if os.path.exists(media_dir):
                    print(f"🖼️  媒体文件: {media_dir}")
                    # 移动媒体文件到 images 目录
                    self._organize_media_files(media_dir)
                
                # 后处理 Markdown 文件
                self._post_process_markdown(output_path)
                
                return output_path
            else:
                print(f"❌ 转换失败:")
                print(f"错误信息: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 转换过程中出错: {e}")
            return None
    
    def _organize_media_files(self, media_dir):
        """整理媒体文件"""
        try:
            if os.path.exists(media_dir):
                # 移动所有媒体文件到 images 目录
                for item in os.listdir(media_dir):
                    src = os.path.join(media_dir, item)
                    dst = os.path.join(self.image_dir, item)
                    if os.path.isfile(src):
                        shutil.move(src, dst)
                        print(f"移动媒体文件: {item}")
                
                # 删除空的 media 目录
                try:
                    os.rmdir(media_dir)
                except:
                    pass
        except Exception as e:
            print(f"整理媒体文件时出错: {e}")
    
    def _post_process_markdown(self, markdown_path):
        """后处理 Markdown 文件"""
        try:
            with open(markdown_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复图片路径
            content = content.replace('](media/', '](images/')
            
            # 添加文档标题
            if not content.startswith('#'):
                doc_name = os.path.splitext(os.path.basename(self.docx_path))[0]
                content = f"# {doc_name}\n\n{content}"
            
            # 清理多余的空行
            import re
            content = re.sub(r'\n{3,}', '\n\n', content)
            
            # 写回文件
            with open(markdown_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 后处理完成")
            
        except Exception as e:
            print(f"后处理时出错: {e}")
    
    def convert_with_options(self, output_filename="document.md", **options):
        """带自定义选项的转换"""
        if not self.check_pandoc_installation():
            self.install_pandoc_instructions()
            return None
        
        output_path = os.path.join(self.output_dir, output_filename)
        
        # 基础命令
        cmd = ['pandoc', self.docx_path, '-t', 'markdown', '-o', output_path]
        
        # 添加选项
        if options.get('extract_media', True):
            cmd.extend(['--extract-media', self.output_dir])
        
        if options.get('wrap', 'none'):
            cmd.extend(['--wrap', options['wrap']])
        
        if options.get('atx_headers', True):
            cmd.append('--markdown-headings=atx')
        
        if options.get('preserve_tabs', True):
            cmd.append('--preserve-tabs')
        
        if options.get('standalone', False):
            cmd.append('--standalone')
        
        # 表格选项
        if options.get('table_style'):
            cmd.extend(['--table-style', options['table_style']])
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ 转换成功!")
                print(f"📄 输出文件: {output_path}")
                
                # 整理媒体文件
                media_dir = os.path.join(self.output_dir, "media")
                if os.path.exists(media_dir):
                    self._organize_media_files(media_dir)
                
                # 后处理
                self._post_process_markdown(output_path)
                
                return output_path
            else:
                print(f"❌ 转换失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 转换出错: {e}")
            return None


def main():
    """主函数"""
    input_file = "test.docx"
    output_dir = "pandoc_output"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    # 创建转换器
    converter = PandocConverter(input_file, output_dir)
    
    # 基础转换
    print("=== 基础转换 ===")
    converter.convert_to_markdown("test_basic.md")
    
    # 高级转换（带选项）
    print("\n=== 高级转换 ===")
    converter.convert_with_options(
        "test_advanced.md",
        extract_media=True,
        wrap='none',
        atx_headers=True,
        preserve_tabs=True,
        standalone=False
    )


if __name__ == "__main__":
    main()
