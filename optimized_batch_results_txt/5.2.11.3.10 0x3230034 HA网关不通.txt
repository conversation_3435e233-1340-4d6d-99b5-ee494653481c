# ********.10 0x3230034 HA网关不通

##### 告警解释
主节点与网关通信异常。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230034 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 主节点角色 | 主节点的角色名称 |
| 网关 | 网关的IP地址 |
##### 对系统的影响
备端不能切换为主端。
##### 可能原因
- 网关服务器处于异常状态（如复位、下电等）。
- 连接网关的网络连接异常，造成链路中断。
- 连接网关的网络配置发生变更，造成链路中断。
##### 处理步骤
1. 查看网关服务器是否处于复位或下电状态。
- 是，转到2。
- 否，转到3。
2. 若网关服务器处于复位状态，则等待复位启动完成，若网关服务器处于下电状态，则给机器上电，启动完成后，查看实时告警，确认告警是否清除。
- 是，处理完毕。
- 否，转到3。
3. 使用PuTTY，登录到管理服务器后台操作系统节点。
默认帐号：DRManager，默认密码：*****。
4. 使用ping命令检查管理服务器与网关的连接是否正常。
- 是，转到5。
- 否，修复管理服务器与网关之间的网络连接，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转到5。
5. 使用PuTTY，登录到管理服务器后台操作系统节点。
默认帐号：DRManager，默认密码：*****。
6. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
7. 执行cd /opt/BCManager/Runtime/bin; sh configSystem.sh -a命令检查告警主机连接网关的网络配置是否正常。
- 是，转到8。
- 在《OceanStor BCManager 8.0.0 eReplication 用户指南》中搜索configSystem.sh命令，按指导重新配置，配置完成后，检查告警是否已清除， 如果已清除，流程结束， 否则请转8。
8. 请联系技术支持工程师协助处理。
##### 参考信息
无。