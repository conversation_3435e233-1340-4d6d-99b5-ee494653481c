# 5.2.19.2.8 ALM-48319-证书无法生效

##### 告警解释
ManageOne下发证书替换任务后，ManageOne下发的证书无法正常使用。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48319 | 重要 | 处理错误告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 未生效证书所属组件名称 |
| 定位信息 | File_Name | 未生效证书名称 |
| 定位信息 | Node | 告警源节点IP地址 |
##### 对系统的影响
组件证书无法使用，组件无法正常提供服务。
##### 可能原因
ManageOne下发证书替换任务，可能由于系统时间未到达证书生效时间、已超过证书失效时间、证书格式错误等原因导致。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- Component：表示组件名称。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录告警源节点Node。
默认帐号： paas，默认密码：*****。
3. 执行以下命令，切换到root用户。
su - root
默认密码：*****。
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 执行以下命令，切换到相应组件运行用户。
su - 用户
除apimgr组件使用apigw_apimgr用户外，其他组件使用apigateway用户。
6. 以相应组件用户，执行以下命令，检查组件运行状态。
sh /opt/apigateway/Component/shell/health_check.sh
- 正常 => 7
- 异常 => 12
7. 执行date命令，检查APIG节点系统时间是否准确。
- 正常 => 10
- 异常 => 8
8. 执行如下命令，检查NTP服务并修正系统时间。
- 执行以下命令，切换到apigateway用户。
exit
su - apigateway
- 执行以下命令，修正系统时间。
sh /opt/apigateway/ntp/shell/ntpdate.sh
回显信息示例：
1 Mar 20:31:14 ntpdate[24700]: step time server 10.109.164.136 offset 1.904335 sec
9. 手动清除告警。
10. 参考《华为云Stack 6.5.1 安全管理指南》“证书管理 > 更换B类&C类证书”查看CA参数和证书规格，并替换证书。
11. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 12
12. 获取Component组件相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
- 故障组件（Component对应值）为“shubao” => 执行命令：cd /var/log/apigateway/shubao/run
- 故障组件（Component对应值）为其他组件 => 执行命令：cd /var/log/apigateway/Component/runtime
- 下载日志“Component.log”到本地，并联系技术支持。
如果目录下不存在“Component.log”，下载“Component_shell.log”即可。
##### 告警清除
手动清除。
##### 参考信息
无。