# 5.2.11.1.9 0x201000F40007 License授权容量耗尽

##### 告警解释
License中资源项（[res_name]）的授权容量（[res_capacity]）已经耗尽。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40007 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| res_name | 资源项名称。 |
| res_capacity | 资源项的授权容量。 |
##### 对系统的影响
用户无法执行备份操作。
##### 可能原因
License中资源项的授权容量已经耗尽。
##### 处理步骤
- 可能原因1：License中资源项的授权容量已经耗尽。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 获取资源项描述中授权容量。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件，并保证新License文件中的授权容量上限大于原有License文件的授权容量。
- 选择“设置 > License”界面，单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无