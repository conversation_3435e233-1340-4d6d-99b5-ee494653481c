# 5.2.3.1.64 ALM-73018 swap分区I/O时延过大

##### 告警解释
对于主机上的swap分区，系统每2秒读取一次本地磁盘的I/O时延并保存，每100s内读取50次I/O时延的值。如果连续50次内有35次I/O延时的值大于100ms，则系统产生告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73018 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。<br>磁盘：异常磁盘的名称。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
- 处理磁盘I/O读写占用的CPU利用率过高。
- CPU处理业务速度较慢。
- 对于非全预占虚拟机，虚拟机内部内存性能下降，用户体验降低。
全预占虚拟机：虚拟机的内存是预先分配的。
非全预占虚拟机：虚拟机的内存是使用时再分配的。
##### 可能原因
- 硬件原因导致磁盘I/O读写响应过慢。
- 主机内存实际使用率超分，导致频繁使用交换分区。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 进入“配置 > 磁盘”界面，单击对应主机所在的主机组。在“逻辑磁盘”区域单击，配置swap分区，建议选择独立使用的卷组。
在“扩容存储”区域单击，提交修改。
3. 等待30分钟后，查看告警是否自动清除。
- 是，处理完毕。
- 否，执行4。
4. 将该主机上的部分虚拟机迁移到其他主机上，降低该主机内存复用率。
具体操作请参考迁移虚拟机。
5. 等待告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。