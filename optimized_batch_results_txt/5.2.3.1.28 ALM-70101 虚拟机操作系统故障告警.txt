# 5.2.3.1.28 ALM-70101 虚拟机操作系统故障告警

##### 告警解释
虚拟机内部操作系统异常，或虚拟机长时间没有向外部的watchdog服务发送心跳信号时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70101 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>租户ID：产生告警虚拟机所属租户 |
| 附加信息 | 事件ID：告警的事件ID<br>可用分区名：告警虚拟机的可用分区<br>租户名：告警虚拟机所属租户<br>虚拟机名：告警虚拟机的名称<br>主机名：告警虚拟机的主机名<br>主机ID：告警虚拟机的主机ID |
##### 对系统的影响
虚拟机操作系统故障，虚拟机内部业务已不可用。虚拟机可能已被自动重启。
##### 可能原因
- 虚拟机操作系统或虚拟机内部软件bug，导致panic，事件ID为0或9。
- 虚拟机内部没有安装向外部的watchdog服务发送心跳信号的软件或该软件卡住，事件ID为1。
- 虚拟机内部重启，事件ID为8（仅针对部分定制镜像）。
- 虚拟机qemu进程异常终止，事件ID为3。
- CGP虚拟机内部关机，事件ID为12。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 选择“资源 > 计算资源 > 虚拟机”，显示虚拟机列表界面，选中要处理的虚拟机，单击“VNC登录”。查看虚拟机是否已被自动重启。
- windows系统查看重启日志：
a. 按 windows键 + R，打开“运行”窗口，执行命令 eventvwr。
b. 在事件查看器中， 选择“windows日志 > 系统 > 筛选当前日志”，在弹出的“筛选当前日志”界面， 输入“1074”（1074，表示重启），单击“确定”，筛选重启日志。
- linux系统执行命令：last reboot查看重启日志。
3. 确认虚拟机是否有自动重启日志，执行对应步骤。
- 是，执行5。
- 否，执行4。
4. 手动重启虚拟机。在Service OM界面关闭虚拟机然后重新启动虚拟机，查看虚拟机是否能被重新启动。
- 是，执行5。
- 否，执行6。
5. 手动清除告警，等待10分钟查看告警是否再次产生。
- 是，可能是由于虚拟机内部开启了watchdog服务，并且没有开启相应的喂狗程序发送心跳信号，执行12。
- 否，处理完毕。
6. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
7. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
8. 执行以下命令，防止系统超时退出。
TMOUT=0
9. 导入环境变量，具体操作请参见导入环境变量
10. 若一直无法重新启动虚拟机，在FusionSphere Openstack后台，查看是否有qemu进程处于D状态。
- 执行 nova show 虚拟机ID | grep host ，获取虚拟机所在主机ID。
- 执行命令 cps host-list | grep 主机ID，获取主机管理面IP。
- 执行命令 su fsp ，切换为fsp用户。
- 执行命令ssh fsp@管理面IP，切换至虚拟机所在主机。
- 执行命令TMOUT=0，防止系统超时退出。
- 执行导入环境变量。
- 执行ps aux | grep qemu | grep 虚拟机ID ，查看是否有qemu进程处于D状态。
- 是，重启主机，然后执行11。
重启主机会影响该主机上运行的虚拟机的服务，建议在重启主机前先将该主机上正常运行的虚拟机迁移至其他主机。
- 否，执行12。
11. 手动清除告警，等待10分钟查看告警是否再次产生。
- 是，执行12。
- 否，处理完毕。
12. 请联系技术支持工程师协助解决。
##### 参考信息
无。