# 5.2.11.2.1 1020799 创建云服务器复制副本失败

##### 告警解释
创建云服务器复制副本失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 1020799 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | 上报告警的云服务，固定为CSBS-VBS。 |
| 服务 | 上报告警的服务，固定为CSBS-VBS。 |
| 租户ID | 产生告警的租户ID。 |
| 项目ID | 产生告警的项目ID。 |
| 云服务器ID | 复制失败的源副本对应的云服务器ID。 |
| 源副本ID | 复制失败的源副本ID。 |
| 租户名称 | 产生告警的租户名称。 |
| 项目名称 | 产生告警的项目名称。 |
| 节点IP | 产生告警的节点IP。 |
| 策略名称 | 绑定此云服务器的策略名称。 |
| 请求ID | 请求ID，可用于日志搜索。 |
| 错误码 | 故障对应的错误码。 |
##### 对系统的影响
创建复制副本失败，影响后续的恢复操作。
##### 可能原因
- 与eBackupWorkFlow连接不通。
- eBackup故障。
##### 处理步骤
- 可能原因：与eBackupWorkFlow连接不通。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行check_karbor_connect命令，查看“eBackupWorkFlow”对应行的“Check_Result”值是否为“OK”。
- 是，请执行2。
- 否，请执行1.d。
- 执行docker exec -ti karborapi bash -c "cat /etc/karbor/karbor.conf" | grep ebackup_lb_ip_address命令获取配置的eBackupWorkFlow的IP地址，在部署或扩容云服务HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“workflow_management_float_ip”，查看二者是否一致。
- 是，请联系技术支持工程师协助解决。
- 否，执行set_ebackup_plugin --ebackup_url eBackupWorkFlow的IP地址命令重新配置eBackupWorkFlow的IP地址，默认密码为“*****”。再次执行check_karbor_connect，若“eBackupWorkFlow”对应行的“Check_Result”值不为“OK”，请联系技术支持工程师协助解决。
- 联系告警附加信息中“租户名称”所对应租户，通知其使用VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，对告警附加信息中“策略名称”对应策略立即重新执行复制。
- 可能原因：eBackup故障。
- 使用告警附加信息中“租户名称”所对应租户下的VDC管理员帐号登录ManageOne运营面，选择告警上报区域，切换到告警附加信息中“项目名称”对应的项目下，进入“云服务器备份”页面，获取对应任务或副本的失败原因。
- 使用PuTTY，通过告警附加信息中“节点IP”登录Service-CSBS节点。
默认帐号：djmanager，默认密码：*****。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 查看日志文件/var/log/huawei/dj/services/system/karbor/karbor-protection/karbor-protection.log，收集失败产生时刻的错误日志详情。
- 请联系技术支持工程师协助解决。
##### 参考信息
无。