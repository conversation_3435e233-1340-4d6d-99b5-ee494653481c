# 5.2.4.18.2 ALM-154 内存占用率过高告警

5.2.4.18.2.4 ALM-154 内存占用率过高告警
##### 告警解释
当部署面检测（检测周期为15秒）到物理内存占用率大于等于告警产生阈值时，产生该告警。当物理内存占用率小于等于告警清除阈值时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 154 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
部署面的响应速度变慢。
##### 可能原因
- 部署面物理内存占用率的产生门限值设置不合理。
- 部署面正在执行消耗系统资源或耗时的操作。
- 业务处理繁忙，物理内存占用率增加。
- 程序处理异常。
##### 处理步骤
1. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
2. 在部署面主菜单中选择“产品 > 系统监控”。
3. 在“系统监控”页面左上方，光标移至并选择告警参数中“主机”对应节点所属的产品。
4. 在“节点”页签右上角单击，检查物理内存占用率的“产生告警阈值”和“清除告警阈值”是否设置合理。
- 是，执行5。
- 否，重新设置物理内存占用率的“产生告警阈值”和“清除告警阈值”为合理的值（缺省值分别为85和80）。告警处理结束。
5. 检查应用程序物理内存占用率。
- 在“节点”页签下，查找产生告警的节点名称。
- 查看对应的物理内存占用率是否超过设定的告警产生阈值。
- 是，可能由于应用程序导致物理内存资源耗尽，待进程对应的业务处理完毕后，执行8。若无法等待业务处理完毕，收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
- 否，执行6。
6. 检查非应用程序物理内存占用率最高的进程。
- 使用PuTTY工具以sopuser用户通过SSH方式登录告警参数中“主机”对应的IP地址，请在如何查找节点对应的IP地址中查询节点对应的IP地址。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 执行以下命令，在“MEM”列查看对应进程的物理内存占用率是否超过设定的告警产生阈值。
> top
...
PID USER      PR  NI    VIRT    RES    SHR S   %CPU  %MEM     TIME+ COMMAND
164860 ossadm    20   0  832312 496564  18164 S 20.199 1.539   1480:48 java
- 是，请华为技术支持工程师协助解决。告警处理结束。
- 否，执行7。
7. 如果物理内存足够，即使业务处理完毕后，物理内存也不会被回收，故告警不会被清除。此时只需确认物理内存占用率是否有继续增加。判断方法如下：
- 在“节点”页签下，查找产生告警的节点名称。
- 查看对应的物理内存占用率是否有持续增加。
- 是，执行8。
- 否，告警处理结束。
8. 等待1分钟，查看本告警是否恢复。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
9. 产生该告警的节点名称发生了变化。
10. 在告警产生后升级了操作系统或者安装了操作系统补丁。
11. 产生该告警的站点名称发生了变化。
12. 产生该告警的服务器不被监控了。
##### 参考信息
无。