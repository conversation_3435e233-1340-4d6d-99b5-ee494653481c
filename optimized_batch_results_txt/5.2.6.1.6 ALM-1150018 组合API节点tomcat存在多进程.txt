# *******.6 ALM-1150018 组合API节点tomcat存在多进程

##### 告警解释
同时存在不止一个tomcat进程时触发该告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150018 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 微服务 | 产生告警的微服务名 |
##### 对系统的影响
系统服务不可用。
##### 可能原因
手动启动进程。
##### 处理步骤
1. 观察1分钟，查看该告警是否清除。
- 是，处理结束。
- 否，执行2。
2. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
3. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
4. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
5. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
6. 使用PuTTY，登录5中确认的虚拟机。
默认帐号：apicom，默认密码：*****。
7. 执行以下命令 ，查看当前所有tomcat进程。
ps -ef | grep tomcat | grep apicom
apicom 3087 1 2 Apr25 ? 00:39:02 /opt/common/jre/bin/java
回显中的“3087”即为tomcat进程的进程号。
8. 执行以下命令，停止所有tomcat进程。
kill -9 进程号
其中进程号为7中获取的进程号。
9. 执行以下命令，启动进程。
sh /opt/apicom/tomcat/bin/startup.sh
10. 观察1分钟，查看该告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节