# 5.2.4.4.1 ALM-CloudCapacityMgmt_Base_1001 vCPU分配率超过阈值告警

##### 告警解释
当某个位置（区域、资源池、AZ、集群）的vCPU分配率等于或超过所设置的某个阈值时，上报此类告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| CloudCapacityMgmt_Base_1001 | 提示/次要/重要/紧急 | 业务质量告警 |
各级别告警的默认阈值如下所示。
| 容量指标 | 紧急阈值(%) |
| --- | --- |
| vCPU分配率 | 90 |
提示阈值、次要阈值和重要阈值系统默认未设置，用户可根据自身情况设置。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 区域 | 上报的告警对应的位置信息。 |
| 资源池 | 上报的告警对应的资源池名称。 |
| 可用分区 | 上报的告警对应的可用分区名称。 |
| 集群 | 上报的告警对应的集群名称。 |
| 阈值级别 | 当前上报告警所属的阈值级别。 |
| 阈值门限 | 用户设定上报告警的门限值。 |
| 当前值 | 本条告警产生时的阈值。 |
##### 对系统的影响
当上报此类阈值告警时，说明对应的资源不足，可能会影响ECS云服务业务发放。
##### 可能原因
- vCPU分配率告警阈值设置太低。
- 当前某个位置对应的vCPU资源不足。
##### 处理步骤
1. 检查“vCPU分配率”的告警阈值。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“监控中心 > 监控配置”。
- 在左侧导航树中选择“容量阈值维护”，进入“容量阈值维护”页面。
- 参考告警属性下的各级别告警的默认阈值，检查vCPU分配率的告警阈值是否合理。
- 是：则剩余可用量过小，执行3。
- 否：执行2。
2. 重新设置“vCPU分配率”的告警阈值。
- 单击“操作”列的“修改”，修改告警阈值。
- 等待10分钟，在系统主菜单选择“集中告警 > 当前告警”，检查告警是否清除。
- 是：操作结束。
- 否：当告警级别降低时，该告警还存在，但是会同步在系统上更新“最近发生时间”的信息，执行3。
3. 联系管理员参考《华为云Stack 6.5.1 扩容指南》中的“扩容计算资源池”章节对vCPU资源进行扩容。
##### 告警清除
此告警修复后，系统会自动清除告警，无需手工清除。
##### 参考信息
无。