# 5.2.3.1.102 ALM-1200075 主机设备端口错包告警

##### 告警解释
当主机设备端口（如果是Trunk口，则按照Trunk为单位）的错包率达到告警配置中配置的百分比后，系统会产生此告警。
当错包率低于告警配置中配置的百分比后，告警自动清除。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200075 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID<br>端口名称：产生告警的trunk名称 |
| 附加信息 | 主机名：产生告警的主机名称<br>错包率：产生告警的错包率<br>阈值：产生告警的阈值<br>每秒包数：产生告警时的每秒收发包数 |
##### 对系统的影响
引起主机设备端口数据丢包或者网络不通。
##### 可能原因
主机设备端口收到或发送错误报文。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 根据告警信息中的端口名称，获取异常端口的名称（如果端口名称是ethx，表示端口没有组bond）。通过 "ip link | grep 端口名称" 查看端口，查看回显信息是否为空。
- 是，说明异常端口是用户态网桥上的端口，执行6。
- 否，说明异常端口是内核态网桥上的端口，执行7。
6. 获取端口的收发数据包。
- 执行如下命令使能用户态EVS报文探针功能。
ovs-appctl dpif-dpdk/component load packet_probe
- 执行如下命令开始获取数据包。
ovs-appctl dpif-dpdk/component exec packet_probe start port_name -w 数据包内容保存路径
记录该uuid，执行6.c命令，需要该uuid。
- 执行如下命令停止获取数据包。
ovs-appctl dpif-dpdk/component exec packet_probe stop port_name -uuid 获取数据包时得到的uuid
- 执行如下命令关闭用户态EVS的获取数据包功能。
ovs-appctl dpif-dpdk/component unload packet_probe
7. 执行如下命令，使用tcpdump获取端口的收发包。
tcpdump -i port_name -w 数据包内容保存路径
按Ctrl+C停止获取数据包。
8. 使用wireshark等网络分析工具分析收发的数据包中错误报文来源，排查并解决可能导致错误报文的根源，查看告警是否自动清除。
- 是，处理完毕。
- 否，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。