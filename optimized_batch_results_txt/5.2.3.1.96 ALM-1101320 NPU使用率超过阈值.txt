# 5.2.3.1.96 ALM-1101320 NPU使用率超过阈值

##### 告警解释
OpenStack周期性（默认5分钟）检测主机组内NPU使用率，当检测到NPU使用个数达到主机组内NPU总个数的85%时，系统产生此告警。
当检测到NPU使用个数降低到主机组内NPU总个数的70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101320 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：npu所在主机组ID<br>npu类型：npu类型 |
| 附加信息 | 服务：产生告警的服务<br>微服务：产生告警的微服务<br>主机组名称：npu所在的主机组名称<br>npu总量：npu的总个数<br>npu使用量：已使用的npu个数<br>阈值：npu个数阈值 |
##### 对系统的影响
可能会造成之后创建NPU虚拟机时，由于NPU资源不够而创建失败。
##### 可能原因
主机组内NPU使用率过高。
##### 处理步骤
1. 通过告警信息得到NPU卡类型以及主机组信息，建议在主机组内增加同类型的NPU卡进行扩容。
2. 当NPU使用率低于70%时，告警是否恢复。
- 是，处理完毕。
- 否，执行3。
3. 如果上述操作出现问题，请联系技术支持工程师协助解决。
##### 参考信息
无。