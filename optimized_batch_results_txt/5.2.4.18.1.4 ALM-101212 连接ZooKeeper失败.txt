# 5.2.4.18.1 ALM-101212 连接ZooKeeper失败

5.2.4.18.1.4 ALM-101212 连接ZooKeeper失败
##### 告警解释
当DBHASwitchService连接ZooKeeper失败时，产生此告警，此时数据库倒换功能异常。当DBHASwitchService连接ZooKeeper恢复正常，自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 101212 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 异常ZooKeeper所在节点的主机名 |
| 服务名 | 服务名称 |
##### 对系统的影响
数据库倒换功能无法使用，数据库访问路由无法刷新。
##### 可能原因
- 超过一半的ZooKeeper服务故障。
- 超过一半的ZooKeeper所在节点故障，或者网络故障。
##### 处理步骤
1. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
2. 查看ZooKeeper服务进程运行状态。
- 在部署面主菜单中选择“产品 > 系统监控”。
- 在“系统监控”页面左上方，光标移至并选择“CloudSOP-UniEP”。
- 选择“服务”页签，单击“UniEPMgr”。
- 在“服务进程”区域，查看以“mczkapp”开头的所有服务进程的“状态”。
- 如果“状态”为“正在运行”，说明ZooKeeper服务进程运行正常，执行3。
- 如果“状态”为“启动中”或“停止中”，单服务的启停时长一般不超过1分钟，如果服务长时间处于该状态，请联系华为技术支持工程师处理。
- 如果“状态”为“故障”、“未知”或“未运行”，说明ZooKeeper服务进程运行异常，请联系华为技术支持工程师处理。
3. 在“服务进程”区域，查看并记录ZooKeeper服务进程名称对应的“节点名称”。
4. 查看ZooKeeper服务所在节点的“连接状态”。
- 在“系统监控”页面左上方，光标移至并选择“CloudSOP-UniEP”。
- 在“节点”页签查看3中记录的节点名称的“连接状态”。
- 如果“连接状态”为“正常”，说明ZooKeeper服务所在节点运行正常，执行5。
- 如果“连接状态”为“断开”，说明ZooKeeper服务所在节点运行异常，请联系华为技术支持工程师处理。
5. 检查告警是否清除。
- 是，处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
##### 告警清除
当DBHASwitchService连接ZooKeeper恢复正常，自动清除。
##### 参考信息
无。
< 上一节