# 5.2.10 弹性负载均衡

---chapter---

# 5.2.10.1 ALM-1223005 数据库连接异常

##### 告警解释
ELB API管理节点netcluster_elb_api_vm每10秒检测后端数据库服务，如果服务显示为down，上报数据库异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223005 | 次要 | 是 |
告警的级别不一定和告警定义时的级别一致，每次发送告警可根据需要动态调整告警级别。
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
单节点数据库对业务无影响，双节点数据库异常会导致业务流量异常。
##### 可能原因
ELB API管理节点netcluster_elb_api_vm健康检查连接数据库出现异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：
- 对端地址
- 本端地址
5. 使用PuTTY，登录异常数据库节点。
登录地址：4中查出的对端地址。
默认帐户：elb
默认密码：*****。
如果数据库节点无法登录，请联系技术工程师协助解决。
6. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
7. 执行以下命令，查看gaussdb进程状态是否为正常。
service had query
命令回显如下，则gaussdb进程状态为normal。
- 是，请执行8。
- 否，请联系技术支持工程师协助解决。
8. 观察告警信息是否清除。
- 是，处理结束。
- 否，执行9。
9. 使用PuTTY，登录ELB管理节点。
登录地址：4中查询到本端地址。
默认帐户：elb
默认密码：*****。
10. 执行以下命令重新加载nginx进程。
cd /usr/local/NSP/etc/elb/bin
./elb_process.sh reload
11. 观察告警信息是否清除。
- 是，处理结束。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。

---chapter---

# 5.2.10.2 ALM-1223006 ETCD集群健康检查告警

##### 告警解释
ELB管理节点、LVS节点或Nginx节点连接ETCD节点失败，生成此告警。
管理节点服务正常时，每隔15秒会对ETCD节点连接，如果连续三次一半以上的ETCD节点都连接失败，产生此告警。
LVS、Nginx节点服务正常时，每隔5秒会对ETCD节点连接，如果连续三次所有的ETCD节点都连接失败，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223006 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
- 影响正常业务流程处理
- 配置下发失败
- 配置回滚失败
- 配置不一致
- 拉取配置文件失败
- 管理节点推送配置文件失败
- ELB TCP流量异常
- ELB HTTP流量异常
##### 可能原因
多台ETCD服务器异常导致ETCD服务集群异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：对端地址
5. 使用PuTTY，登录异常ETCD节点。
登录地址：4中查出的对端地址。
默认帐户：elb
默认密码：*****。
6. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
若ETCD节点不能正常登录，请联系技术工程师协助解决。
7. 分别在所有ETCD节点上执行以下命令，查看ETCD进程是否正常启动。
ps -ef|grep etcd
命令回显如下，ETCD进程存在，表示ETCD进程正常启动。
- 是，请联系技术工程师协助解决。
- 否，请执行8
8. 执行以下命令，重新启动ETCD进程。
cd /usr/local/NSP/scripts
sh start_etcd.sh
重启ETCD进程不会对其他业务造成影响。
9. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。

---chapter---

# ******** ALM-1223013 集群中存在主机连接异常

##### 告警解释
ELB每10秒检测后端LVS、 Nginx或API节点，如果后端服务端口检测异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223013 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
影响现有业务性能或者业务无法处理。
##### 可能原因
管理节点健康检查时连接集群主机出现异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：对端地址
5. 使用PuTTY，登录连接异常的主机节点。
登录地址：LVS节点或Nginx节点的IP地址，节点IP地址为4中查出的对端地址。
默认账户：elb
帐户默认密码：*****。
6. 执行以下命令，查看nginx进程是否运行正常。
ps -ef|grep nginx|grep -v grep
5登录的是LVS节点，回显如下所示，表示nginx进程运行正常。
5登录的是Nginx节点，回显如下所示，表示nginx进程运行正常。
- 是，请执行9。
- 否，请执行7。
7. 执行以下命令，重新启动nginx进程。
- LVS节点
sh /usr/local/NSP/etc/lua/bin/restart_lvs.sh
- Nginx节点
sh /usr/local/NSP/etc/router/bin/restart_nginx.sh
8. 重启nginx进程不会对其他业务造成影响。
9. 执行以下命令，查看nginx进程是否恢复正常。
ps -ef|grep nginx|grep -v grep
- 是，执行9。
- 否，请联系技术支持工程师协助解决。
10. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。

---chapter---

# ******** ALM-1223014 僵尸进程告警

##### 告警解释
ELB API每30分钟秒检测后端服务节点业务进程，如果进程显示为无响应状态，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223014 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
影响业务性能或者业务完全无法处理。
##### 可能原因
管理节点进行僵尸进程检查时集群主机（LVS或Nginx）存在僵尸进程。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：对端地址
5. 使用PuTTY，登录出现僵尸进程的虚拟机节点，虚拟机节点可以是LVS节点或Nginx节点。
登录地址：LVS节点或Nginx节点的IP地址，节点IP地址为4中询到的对端地址。
默认帐户：elb
默认密码：*****。
6. 5中登录的节点为LVS节点，请执行7~10。
5中登录的节点为Nginx节点，请执行11~14。
7. 执行以下命令，查看keepalived进程和nginx进程是否运行正常。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "keepalived" | awk 'END{print NR}'
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关进程运行正常。
- 是，请执行15。
- 否，请执行8。
8. 执行以下命令，获取僵尸进程的父进程ID。
ps -A -o stat,ppid,cmd | grep keepalived | grep -e '^[Zz]' | awk '{print $2}'
ps -A -o stat,ppid,cmd | grep nginx | grep -e '^[Zz]' | awk '{print $2}'
9. 执行以下命令，清除僵尸进程的父进程。
kill -s 9 <pid>
其中<pid>为8查询出的进程ID。
清除僵尸进程不会对其他进程造成影响。
10. 执行以下命令，查看僵尸进程是否被清除。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "keepalived" | awk 'END{print NR}'
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关僵尸进程已被清除。
- 是，请执行15。
- 否，请联系技术支持工程师协助解决。
11. 执行以下命令，查看nginx进程是否运行正常。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
回显返回0，表示相关进程运行正常。
- 是，请执行15。
- 否，请执行12。
12. 执行以下命令，获取僵尸进程的父进程ID。
ps -A -o stat,ppid,cmd | grep nginx | grep -e '^[Zz]' | awk '{print $2}'
13. 执行以下命令，清理僵尸进程的父进程。
kill –s 9 <pid>
其中<pid>为12查询出的进程ID。
清除僵尸进程不会对其他业务造成影响。
14. 执行以下命令，查看僵尸进程是否清除。
ps -A -o stat,pid,ppid,cmd | grep -e '^[Zz]' | grep "nginx" | awk 'END{print NR}'
命令回显0，表示僵尸进程已被清除。
- 是，请执行15。
- 否，请联系技术支持工程师协助解决。
15. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。

---chapter---

# 5.2.10.5 ALM-1223016 ELB管理节点脑裂告警

##### 告警解释
ELB API每10秒查询备节点状态，如果主备节点都处于主状态，上报ELB API脑裂异常，生成此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1223016 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 此条告警信息的名称。 |
| 告警源 | 告警来源。 |
| 来源系统 | 对接系统的名称。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 定位信息 | 云服务：服务名称。 |
| 定位信息 | 节点类型：告警资源类型。 |
| 附加信息 | 云服务：服务名称。 |
| 附加信息 | 服务：设备节点类型，例如mgmt, lvs, nginx等。 |
| 附加信息 | 本端地址：产生告警信息的ELB API虚拟机的IP地址。 |
| 附加信息 | 对端地址：产生告警信息的ELB虚拟机的IP地址。 |
##### 对系统的影响
对现有业务无影响，新下发的ELB业务会出现配置异常。
##### 可能原因
两个ELB管理节点之间keepalived心跳中断，仲裁无法对其感知，导致ELB管理节点出现双主情况。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 查询以下告警信息。
附加信息：
- 对端地址
- 本端地址
5. 使用PuTTY，登录ELB管理节点。
登录地址：4中查出的本端地址和对端地址。
默认帐户：elb
默认密码：*****。
6. 执行以下命令，切换到root用户。root帐户默认密码：*****。
sudo su root
7. 分别在这两个节点执行以下命令，查看两个节点是否都为master。
cat /etc/keepalived/status
命令执行后回显如下信息：
[root@localhost elb]# cat /etc/keepalived/status
master
- 若一个节点为master，另一个节点为backup，则管理节点进程正常，请执行9。
- 若两个节点都为master，则进程不正常，继续执行8。
8. 使用PING命令查看两个管理节点之间是否连通。
- 是，请联系技术支持工程师协助解决。
- 否，请解决网络问题后执行9。
9. 观察告警是否清除。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
< 上一节

---chapter---

# 5.2.11 灾备服务

---chapter---

# ******** eBackup

---chapter---

# ********.1 0x1000F40000 License文件无效

##### 告警解释
License文件无效。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40000 | 紧急 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack恢复、复制、远程复制、HA相关操作。
##### 可能原因
License文件无效。
##### 处理步骤
- 可能原因1：License文件无效。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》 中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.10 0x201000F40008 License授权容量即将耗尽

##### 告警解释
License中资源项（[res_name]）的授权容量（[res_capacity]）使用达到了告警阈值（[threshold_value]），即将耗尽。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40008 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| res_name | 资源项名称。 |
| res_capacity | 资源项的授权容量。 |
| threshold_value | 告警阈值。 |
##### 对系统的影响
不涉及。
##### 可能原因
License授权容量即将耗尽。
##### 处理步骤
- 可能原因1：License中资源项的授权容量即将耗尽。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 获取资源项描述中授权容量。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件，并保证新License文件中的授权容量上限大于原有License文件的授权容量。
- 选择“设置 > License”界面，单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.11 0x201000F4000C 存在License不支持的特性

##### 告警解释
当前不支持VMware类型的虚拟机，备份VMware虚拟机的任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F4000C | 次要 | 否 |
##### 对系统的影响
备份VMware虚拟机的任务将不会被执行。
##### 可能原因
当前License不支持备份VMware类型的虚拟机。
##### 处理步骤
- 可能原因1：当前License不支持备份VMware类型的虚拟机。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持备份VMware类型的虚拟机的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.12 0x201000F40013 存在License不支持的特性

##### 告警解释
当前不支持HA相关功能，此类型的任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40013 | 次要 | 否 |
##### 对系统的影响
HA相关任务无法被执行。
##### 可能原因
当前License不支持HA相关功能。
##### 处理步骤
- 可能原因1：当前License不支持HA相关功能。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持HA的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.13 0x201000F40014 存在License不支持的特性

##### 告警解释
当前License不支持重复数据删除特性，此类型的备份任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40014 | 次要 | 否 |
##### 对系统的影响
具有重复数据删除特性的备份任务无法被执行。
##### 可能原因
当前License不支持重复数据删除特性。
##### 处理步骤
- 可能原因1：当前License不支持重复数据删除特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持重复数据删除特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.14 0x201000F40016 存在License不支持的特性

##### 告警解释
当前不支持两级备份特性，此类型的复制任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40016 | 次要 | 否 |
##### 对系统的影响
两级备份任务无法被执行。
##### 可能原因
当前License不支持两级备份特性。
##### 处理步骤
- 可能原因1：当前License不支持两级备份特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持两级备份特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.15 0x201000F40017 存在License不支持的特性

##### 告警解释
当前不支持跨AZ/被级联OpenStack恢复特性，此类型的恢复任务将不会被执行。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40017 | 次要 | 否 |
##### 对系统的影响
跨AZ/被级联OpenStack恢复任务无法被执行。
##### 可能原因
当前License不支持跨AZ/被级联OpenStack恢复特性。
##### 处理步骤
- 可能原因1：当前License不支持跨AZ/被级联OpenStack恢复特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持跨AZ/被级联OpenStack恢复特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.16 0x201000F40018 存在License不支持的特性

##### 告警解释
当前License不支持VPP协议加速特性。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000F40018 | 次要 | 否 |
##### 对系统的影响
系统无法使用VPP协议加速功能。
##### 可能原因
当前License不支持VPP协议加速特性。
##### 处理步骤
- 可能原因1：当前License不支持VPP协议加速特性。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请支持VPP协议加速特性的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.17 0x1000310000 事件转储目录所用空间已超出阈值

##### 告警解释
事件转储目录（[Dump_dir]）所用空间已超过预设的阈值（[Threshold_size]MB），当目录大小超过最大值（[Max_size]MB）后，程序将自动删除最旧的转储文件。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000310000 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Threshold_size | 允许转储目录使用空间的阈值。 |
| Max_size | 允许转储目录使用的最大空间。 |
| Dump_dir | 事件转储路径。 |
##### 对系统的影响
系统事件记录可能丢失。
##### 可能原因
事件转储目录下所保存的文件太多。
##### 处理步骤
- 可能原因1：事件转储目录下所保存的文件太多。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/microservice/ebk_alarm/conf/hcpconf.ini | grep 'EventDumpDirPath'”命令，获取事件转储目录。
- 执行cd EventDumpDirPath命令进入事件转储目录，其中EventDumpDirPath为1.c获取的事件转储目录。
- 请备份事件转储文件。
- 手动删除已经备份的事件转储文件，使“EventDumpDirPath”目录的使用空间小于阈值。
##### 参考信息
无

---chapter---

# ********.18 0x20100031000A 证书校验失败

##### 告警解释
与告警服务器（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000A | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | 告警服务器的IP地址。 |
##### 对系统的影响
与告警服务器之间的连接存在安全风险。
##### 可能原因
- 系统中不存在连接该告警服务器的CA证书。
- 连接该告警服务器的CA证书已过期。
##### 处理步骤
- 可能原因1：系统中不存在连接该告警服务器的CA证书。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看告警服务器的CA证书是否存在。
- 是，执行2。
- 否，执行1.c。
- 请联系管理员获取告警服务器的CA证书。
- 单击“导入”，上传告警服务器的证书。
- 可能原因2：连接该告警服务器的CA证书已过期。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 证书”界面，查看告警服务器的CA证书是否过期。
- 是，执行2.c。
- 否，执行3。
- 请联系管理员获取告警服务器的CA证书。
- 单击“导入”，上传告警服务器的证书。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无

---chapter---

# ********.19 0x20100031000C 证书校验失败

##### 告警解释
在服务器（IP：[NodeIP]）上的微服务（[MicroService_Name]）与数据库（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x20100031000C | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |
##### 对系统的影响
与数据库之间的连接存在安全风险。
##### 可能原因
系统中不存在连接该数据库的CA证书或CA证书已过期。
##### 处理步骤
- 可能原因1：系统中不存在连接该数据库的CA证书或CA证书已过期。
- 请联管理员获取未过期的数据库证书文件，将其重命名为“cacert.pem”。
- 通过WinSCP工具将“cacert.pem”文件拷贝到上报告该警的服务器的“/home/<USER>
默认帐户：hcp，默认密码：*****
- 使用PuTTY，通过告警上报的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行mv /home/<USER>/cacert.pem /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf命令，将证书文件移动到对应微服务的配置目录中。其中ebk_xxx为微服务名称。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf命令，进入证书保存目录。
- 执行chmod 600 cacert.pem命令，将“cacert.pem”文件的权限设置为600。
- 执行chown hcpprocess:hcpmgr cacert.pem命令，将“cacert.pem”文件的所有者修改为hcpprocess。
- 执行service hcp restart命令，重启服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.2 0x1000F40001 License未配置

##### 告警解释
在激活License时，检测到License文件不存在。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000F40001 | 紧急 | 否 |
##### 对系统的影响
用户不能执行备份、跨AZ/被级联OpenStack恢复、复制、远程复制、HA相关操作。
##### 可能原因
用户未导入License文件。
##### 处理步骤
- 可能原因1：用户未导入License文件。
- 请参考《OceanStor BCManager 8.0.1 eBackup License申请指导书》中的“License功能特性”、“获取ESN”和“申请License”章节申请新的License文件。
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > License”界面。
- 单击“导入”，在弹出的对话框中，选择License文件，然后单击“打开”。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.20 0x201000310010 访问告警服务器失败

##### 告警解释
访问告警服务器（IP：[Server_ip]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310010 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Server_ip | 告警服务器管理IP地址。 |
##### 对系统的影响
不能正常上报告警。
##### 可能原因
与告警服务器之间的连接中断。
##### 处理步骤
- 可能原因1：与告警服务器连接中断。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 告警服务器的管理IP”命令，如果是IPv6，执行“ping6 告警服务器的管理IP”命令，检查网络连通性是否正常。
- 是，执行1.d。
- 否，请联系技术支持工程师协助解决。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 进入“设置 > 配置 > 告警上报”界面。
请确认“服务类型”是否为“OC”，“URL”是否为ManageOne告警服务器的域名和端口号。
- 是，执行2。
- 否，重新配置告警上报参数。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无

---chapter---

# ********.21 0x201000310015 数据库连接失败

##### 告警解释
在服务器（IP：[NodeIP]）上的微服务（[MicroService_Name]）连接数据库（IP：[IP_Address]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x201000310015 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| NodeIP | 服务器的IP地址。 |
| MicroService_Name | 微服务的名称。 |
| IP_Address | 数据库的IP地址。 |
##### 对系统的影响
该微服务无法正常运行，可能导致系统整体性能下降。
##### 可能原因
- 数据库服务未启动。
- 网络异常。
##### 处理步骤
- 可能原因1：数据库服务未启动。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“ps -ef | grep gaussdb”命令，查看数据库进程是否存在。
- 是，执行2。
- 否，执行1.d。
- 执行“sh /opt/huawei-data-protection/ebackup/bin/gaussdb_sandbox.sh restart”命令，重启数据库服务。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
- 可能原因2：网络异常。
- 使用PuTTY，以hcp用户通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf/hcpconf.ini | grep Loadbalance”，获取浮动IP地址。其中“ebk_xxx”为微服务名称。
- 如果是IPv4，执行“ping 浮动IP地址”命令，如果是IPv6，执行“ping6 浮动IP地址”，检查网络通信是否正常。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.e。
- 排查网络相关问题后，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.22 0x210000000101 微服务注册失败

##### 告警解释
微服务（名称：[Name]，IP：[IP_Address]，端口：[Port]）注册失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000101 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |
##### 对系统的影响
该微服务无法正常接收和处理请求，可能导致系统整体性能下降。
##### 可能原因
微服务注册失败。
##### 处理步骤
- 可能原因1：微服务注册失败。
- 使用PuTTY，通过告警上报的IP地址登录微服务注册失败所在服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行ps -ef | grep 微服务名称命令，检查微服务进程是否存在。
- 是，执行1.f。
- 否，执行1.d。
- 执行netstat -lp | grep 微服务端口号命令，检查该微服务相关端口是否处于“LISTEN”状态。
- 是，执行1.e。
- 否，执行1.f。
- 找到“LISTEN”状态后面的进程ID，执行kill -9 进程ID命令，释放该端口。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/script命令，进入微服务脚本目录，其中，“ebk_xxx”是微服务名称。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止该微服务。等待1分钟左右，微服务会自动启动。
- 是，执行1.i。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，执行1.j。
- 执行service hcp status命令，检查ebk_governance和ebk_iam微服务是否在运行。
- 是，执行1.k。
- 否，参考1.f~1.h重新启动未运行的ebk_governance或ebk_iam，之后转到1.i。
- 执行ps -ef | grep ebk_lb命令，检查ebk_lb的nginx进程是否正常运行。
- 是，执行1.l。
- 否，参考1.f~1.h重新启动ebk_lb。之后转到1.i。
- 执行cat /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/conf/hcpconf.ini | grep Loadbalance命令，获取浮动IP地址。
- 如果是IPv4，执行ping 浮动IP地址命令，如果是IPv6，执行ping6 浮动IP地址，检查微服务所在服务器的网络通信是否正常。
- 是，请联系技术支持工程师协助解决。
- 否，修复网络。之后转到1.i。
##### 参考信息
无

---chapter---

# ********.23 0x210000000100 微服务已停止

##### 告警解释
微服务（名称：[Name]，IP：[IP_Address]，端口：[Port]）已停止。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000100 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |
##### 对系统的影响
该微服务无法正常运行，可能导致系统整体性能下降。
##### 可能原因
微服务已停止运行。
##### 处理步骤
- 可能原因1：微服务已停止运行。
- 使用PuTTY，通过告警上报的IP地址登录已停止运行的微服务所在服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行ps -ef | grep 微服务名称命令，检查微服务进程是否存在。
- 是，执行1.f。
- 否，执行1.d。
- 执行netstat -lp | grep 微服务端口号命令，检查该微服务相关端口是处于“LISTEN”状态。
- 是，执行1.e。
- 否，执行1.f。
- 找到“LISTEN”状态后面的进程ID，执行kill -9 进程ID命令，释放该端口。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/script命令，进入微服务脚本目录其中，“ebk_xxx”是微服务名称。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止该微服务。等待1分钟左右，微服务会自动启动。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.24 0x6000840001 证书已经过期

##### 告警解释
证书（区域：[Region]，部件：[ServiceName]，证书名：[CertName]）已经过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840001 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |
##### 对系统的影响
外部客户端部件连接eBackup可能失败。
##### 可能原因
证书已经过期。
##### 处理步骤
- 可能原因1：证书已经过期。
- 如果告警中的证书名是“eBackup-Portal”，请参考更换灾备服务eBackup证书（eBackup-Portal）章节替换证书。
- 如果告警中的证书名是“eBackup-Cert”或“eBackup-IAMCert”，请参考通过ManageOne界面方式单个或批量更换证书章节替换证书。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.25 0x6000840002 证书即将过期

##### 告警解释
证书（区域：[Region]，部件：[ServiceName]，证书名：[CertName]）即将在[Date]过期。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x6000840002 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Region | 来源部件所在区域。 |
| ServiceName | 证书来源部件。 |
| CertName | 证书的名称。 |
| Date | 证书过期日期。 |
##### 对系统的影响
无。
##### 可能原因
证书即将过期。
##### 处理步骤
- 可能原因1：证书即将过期。
- 如果告警中的证书名是“eBackup-Portal”，请参考中的“证书管理 > 更换A类证书 > 更换灾备服务eBackup证书（eBackup-Portal）”更换灾备服务eBackup证书（eBackup-Portal） 章节替换证书。
- 如果告警中的证书名是“eBackup-Cert”或“eBackup-IAMCert”，请参考通过ManageOne界面方式单个或批量更换证书章节替换证书。
- 等待3分钟左右，在当前告警界面查看该告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.26 0x210000000200 证书校验失败

##### 告警解释
与LDAP服务器（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000200 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | LDAP服务器的IP地址。 |
##### 对系统的影响
与LDAP服务器之间的连接存在安全风险。
##### 可能原因
系统中不存在连接该LDAP服务器的CA证书或CA证书已过期。
##### 处理步骤
- 可能原因1：系统中不存在连接该LDAP服务器的CA证书或CA证书已过期。
- 请联系管理员获取未过期的LDAP服务器的CA证书文件。
- 将获取到的LDAP服务器的CA证书文件重命名为“LDAP_CACert.crt”。
- 通过WinSCP工具将“LDAP_CACert.crt”文件拷贝到Manager或Server的“/home/<USER>
登录方式请参考登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行mv /home/<USER>/LDAP_CACert.crt /opt/huawei-data-protection/ebackup/microservice/ebk_iam/conf命令，将证书文件移动到IAM微服务的配置目录中。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_iam/conf命令，进入证书保存目录。
- 执行chmod 600 LDAP_CACert.crt命令，将“LDAP_CACert.crt”文件的权限设置为600。
- 执行chown hcpprocess:hcpmgr LDAP_CACert.crt命令，将“LDAP_CACert.crt”文件的所有者修改为hcpprocess:hcpmgr。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_iam/script命令，进入微服务脚本目录。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止IAM微服务。等待1分钟左右，IAM微服务会自动启动。
- 以LDAP类型的帐户登录Manager或Server的GUI，以此触发证书校验。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.27 0x105800860001 清理备份记录失败

##### 告警解释
微服务（[MicroService_Name]）清理备份记录（备份ID：[backupId]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x105800860001 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| backupId | 备份ID |
##### 对系统的影响
不涉及。
##### 可能原因
网络中断。
##### 处理步骤
- 可能原因1：网络中断。
- 参考登录eBackup服务器登录Manager。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行ping OpenStack控制节点IP或域名命令，如果是IPv6，执行ping6 OpenStack控制节点IP或域名命令，测试Manager与OpenStack控制节点的网络是否连通。
- 是，执行1.d。
- 否，执行1.h。
- 使用PuTTY，登录任意OpenStack控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行source set_env命令导入环境变量。
- 执行cinder backup-delete 备份id命令删除快照，检查执行结果是否成功。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 请联系机房管理员修复网络连通性，确保网络正常连接后查看告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.28 0x21000000090E 清理leftover删除快照失败

##### 告警解释
微服务（[MicroService_Name]）删除卷快照（快照ID：[snapshotId]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x21000000090E | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| MicroService_Name | 微服务名 |
| snapshotId | 快照ID |
##### 对系统的影响
不涉及。
##### 可能原因
网络中断。
##### 处理步骤
- 可能原因1：网络中断。
- 参考登录eBackup服务器登录Manager。
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping OpenStack控制节点IP或域名”命令，如果是IPv6，执行“ping6 OpenStack控制节点IP或域名”命令，测试Manager与OpenStack控制节点网络是否连通。
- 是，1.d。
- 否，执行1.h。
- 使用PuTTY，登录任意OpenStack控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行source set_env命令导入环境变量。
- 执行cinder snapshot-delete 快照id命令删除快照，检查执行结果是否成功。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
- 请联系机房管理员修复网络连通性，确保网络正常连接后查看告警是否恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无

---chapter---

# ********.29 0x21000000090F 组件连接异常

##### 告警解释
备份节点（[Node_Ip]）与组件（[Module_Type]）地址（[IP_Address]）连接异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x21000000090F | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Ip | eBackup节点IP |
| Module_Type | 对接组件类型 |
| IP_Address | 组件地址 |
##### 对系统的影响
不能进行正常业务操作。
##### 可能原因
组件间网络问题。
##### 处理步骤
- 可能原因1：网络中断。
- 使用PuTTY，以hcp帐号登录告警所述备份节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping 组件地址”命令，如果是IPv6，执行“ping6 组件地址”命令，测试告警所述备份节点与组件的网络是否连通。
- 是，执行2。
- 否，执行1.d。
- 执行echo "nameserver DNS server ip" >> /etc/resolv.conf命令，配置DNS服务器地址。DNS配置完成后，查看告警是否恢复。
- 是，处理结束。
- 否，执行2。
- 检查该告警是否为升级前产生的。如果是，则手动清除该告警；如果不是，则联系技术支持工程师协助解决。
可参考以下步骤检查该告警是否为升级前产生的：
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在事件界面查看是否有升级事件0x201000C9001D（备份服务器升级成功）存在。
- 存在，如果该告警的产生时间在升级事件的产生时间之前，则该告警为升级前产生的。
- 不存在，则该告警不是升级前产生的。
##### 参考信息
无