#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面版ALM告警文档批量转换器
支持多种章节格式：*******.x, *******.x, *******.x
"""

import os
import re
import time
from datetime import datetime
from docx import Document
from enhanced_doc_converter import EnhancedDocxToMarkdown
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('alm_conversion_comprehensive.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ComprehensiveALMConverter:
    def __init__(self, source_docx, output_dir="alm_markdown_comprehensive"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        self.temp_dir = os.path.join(output_dir, "temp_docx")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        logger.info(f"初始化全面版ALM批量转换器")
        logger.info(f"源文档: {source_docx}")
        logger.info(f"输出目录: {output_dir}")
    
    def find_all_alarm_chapters(self):
        """查找所有告警章节（支持多种格式）"""
        logger.info("🔍 开始查找所有告警章节...")
        
        chapters = []
        doc = Document(self.source_docx)
        
        # 定义支持的章节格式
        patterns = [
            (r'^5\.2\.3\.1\.\d+\s+ALM-\d+', '*******', 'ALM告警'),
            (r'^5\.2\.3\.2\.\d+\s+\w+', '*******', 'Service OM告警'),
            (r'^5\.2\.4\.1\.\d+\s+\w+', '*******', '其他告警')
        ]
        
        for i, paragraph in enumerate(doc.paragraphs):
            # 只检查标题样式的段落
            if not paragraph.style.name.startswith('Heading 1'):
                continue
                
            text = paragraph.text.strip()
            
            # 检查每种模式
            for pattern, section_prefix, section_type in patterns:
                if re.match(pattern, text):
                    # 提取告警代码或标识符
                    alarm_code = self.extract_alarm_code(text, section_type)
                    
                    if alarm_code:
                        # 提取告警名称
                        alarm_name = self.extract_alarm_name(text, alarm_code)
                        
                        chapters.append({
                            'title': text,
                            'alarm_code': alarm_code,
                            'alarm_name': alarm_name,
                            'section_type': section_type,
                            'section_prefix': section_prefix,
                            'start_para': i
                        })
                        
                        logger.info(f"  找到: {alarm_code} - {alarm_name} ({section_type})")
                        
                        if len(chapters) % 10 == 0:
                            logger.info(f"  已找到 {len(chapters)} 个告警章节...")
                    break
            
            # 如果遇到下一个主要章节（5.3开头），说明所有5.2的章节已经结束
            if text.startswith('5.3'):
                logger.info(f"  遇到下一个主要章节: {text[:50]}...")
                logger.info(f"  停止搜索，所有5.2章节已全部找到")
                break
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                # 最后一个章节的结束位置需要特殊处理
                end_para = len(doc.paragraphs)
                for j in range(chapter['start_para'] + 1, len(doc.paragraphs)):
                    para_text = doc.paragraphs[j].text.strip()
                    if para_text.startswith('5.3') or para_text.startswith('6.'):
                        end_para = j
                        break
                chapter['end_para'] = end_para
        
        # 按章节类型统计
        stats = {}
        for chapter in chapters:
            section_type = chapter['section_type']
            stats[section_type] = stats.get(section_type, 0) + 1
        
        logger.info(f"✅ 找到告警章节总计: {len(chapters)} 个")
        for section_type, count in stats.items():
            logger.info(f"   - {section_type}: {count} 个")
        
        return chapters
    
    def extract_alarm_code(self, text, section_type):
        """提取告警代码"""
        if section_type == 'ALM告警':
            # ALM-xxxx格式
            match = re.search(r'ALM-\d+', text)
            return match.group() if match else None
        elif section_type == 'Service OM告警':
            # 提取章节编号作为标识符
            match = re.search(r'^(5\.2\.3\.2\.\d+)', text)
            if match:
                return f"SOM-{match.group(1).replace('.', '-')}"
            return None
        elif section_type == '其他告警':
            # 提取章节编号作为标识符
            match = re.search(r'^(5\.2\.4\.1\.\d+)', text)
            if match:
                return f"OTHER-{match.group(1).replace('.', '-')}"
            return None
        return None
    
    def extract_alarm_name(self, text, alarm_code):
        """提取告警名称"""
        if alarm_code.startswith('ALM-'):
            # ALM告警：ALM-xxxx 告警名称
            match = re.search(r'ALM-\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"
        elif alarm_code.startswith('SOM-'):
            # Service OM告警：*******.x 告警名称
            match = re.search(r'^5\.2\.3\.2\.\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"
        elif alarm_code.startswith('OTHER-'):
            # 其他告警：*******.x 告警名称
            match = re.search(r'^5\.2\.4\.1\.\d+\s+(.+)', text)
            return match.group(1) if match else "未知告警"
        return "未知告警"
    
    def create_chapter_docx(self, chapter):
        """创建单个章节的DOCX文件"""
        try:
            # 重新打开文档（避免内存问题）
            doc = Document(self.source_docx)
            new_doc = Document()
            
            start_para = chapter['start_para']
            end_para = chapter['end_para']
            
            logger.info(f"     提取段落范围: {start_para} - {end_para}")
            
            # 复制段落
            for i in range(start_para, min(end_para, len(doc.paragraphs))):
                para = doc.paragraphs[i]
                new_para = new_doc.add_paragraph()
                new_para.text = para.text
                
                # 尝试复制样式
                try:
                    new_para.style = para.style
                except:
                    pass
            
            # 生成文件名
            safe_name = self.generate_safe_filename(chapter)
            docx_path = os.path.join(self.temp_dir, f"{safe_name}.docx")
            
            # 保存文档
            new_doc.save(docx_path)
            
            return docx_path, safe_name
            
        except Exception as e:
            logger.error(f"创建章节文档时出错: {e}")
            return None, None
    
    def generate_safe_filename(self, chapter):
        """生成安全的文件名"""
        alarm_code = chapter['alarm_code']
        alarm_name = chapter['alarm_name']
        section_type = chapter['section_type']
        
        # 清理文件名中的非法字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', alarm_name)
        safe_name = safe_name.strip()
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50]
        
        # 添加类型前缀以便分类
        type_prefix = {
            'ALM告警': 'ALM',
            'Service OM告警': 'SOM',
            '其他告警': 'OTHER'
        }.get(section_type, 'UNKNOWN')
        
        return f"{alarm_code}_{safe_name}"
    
    def convert_chapter_to_markdown(self, docx_path, safe_name):
        """将单个章节转换为Markdown"""
        try:
            # 创建转换器
            converter = EnhancedDocxToMarkdown(
                docx_path, 
                os.path.join(self.output_dir, f"{safe_name}_output")
            )
            
            # 执行转换
            md_filename = f"{safe_name}.md"
            output_path = converter.convert_to_markdown(md_filename)
            
            return output_path
            
        except Exception as e:
            logger.error(f"转换章节 {safe_name} 时出错: {e}")
            return None
    
    def process_chapters_batch(self, chapters, batch_size=5):
        """批量处理章节（减少内存使用）"""
        logger.info(f"🚀 开始批量处理 {len(chapters)} 个告警章节...")
        
        successful_conversions = []
        failed_conversions = []
        
        # 分批处理
        for batch_start in range(0, len(chapters), batch_size):
            batch_end = min(batch_start + batch_size, len(chapters))
            batch_chapters = chapters[batch_start:batch_end]
            
            logger.info(f"\n📦 处理批次 {batch_start//batch_size + 1}: 章节 {batch_start+1}-{batch_end}")
            
            for i, chapter in enumerate(batch_chapters):
                chapter_idx = batch_start + i + 1
                chapter_start_time = time.time()
                
                logger.info(f"\n📄 处理第 {chapter_idx}/{len(chapters)} 章: {chapter['alarm_code']}")
                logger.info(f"   类型: {chapter['section_type']}")
                logger.info(f"   标题: {chapter['alarm_name']}")
                
                try:
                    # 1. 创建章节文档
                    logger.info("   📝 创建章节文档...")
                    docx_path, safe_name = self.create_chapter_docx(chapter)
                    
                    if not docx_path:
                        logger.error("   ❌ 创建文档失败")
                        failed_conversions.append({
                            'chapter': chapter,
                            'error': '创建文档失败'
                        })
                        continue
                    
                    # 2. 转换为Markdown
                    logger.info("   🔄 转换为Markdown...")
                    md_path = self.convert_chapter_to_markdown(docx_path, safe_name)
                    
                    if md_path:
                        chapter_time = time.time() - chapter_start_time
                        logger.info(f"   ✅ 转换成功! 耗时: {chapter_time:.2f}秒")
                        logger.info(f"   📁 输出: {md_path}")
                        
                        successful_conversions.append({
                            'chapter': chapter,
                            'md_path': md_path,
                            'docx_path': docx_path,
                            'time': chapter_time
                        })
                    else:
                        logger.error(f"   ❌ 转换失败")
                        failed_conversions.append({
                            'chapter': chapter,
                            'error': '转换失败'
                        })
                    
                    # 清理临时文件（可选）
                    # try:
                    #     os.remove(docx_path)
                    # except:
                    #     pass
                    
                except Exception as e:
                    logger.error(f"   ❌ 处理章节时出错: {e}")
                    failed_conversions.append({
                        'chapter': chapter,
                        'error': str(e)
                    })
                    continue
        
        return successful_conversions, failed_conversions
    
    def process_all_chapters(self):
        """处理所有章节"""
        start_time = time.time()
        
        # 查找章节
        chapters = self.find_all_alarm_chapters()
        
        if not chapters:
            logger.error("❌ 未找到告警章节")
            return []
        
        # 批量处理
        successful_conversions, failed_conversions = self.process_chapters_batch(chapters)
        
        # 输出总结
        total_time = time.time() - start_time
        self.print_summary(successful_conversions, failed_conversions, total_time)
        
        return successful_conversions
    
    def print_summary(self, successful, failed, total_time):
        """打印处理总结"""
        logger.info("\n" + "="*80)
        logger.info("📊 批量转换完成总结")
        logger.info("="*80)
        
        logger.info(f"⏱️  总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        logger.info(f"✅ 成功转换: {len(successful)} 个章节")
        logger.info(f"❌ 转换失败: {len(failed)} 个章节")
        
        if len(successful) + len(failed) > 0:
            logger.info(f"📈 成功率: {len(successful)/(len(successful)+len(failed))*100:.1f}%")
        
        # 按类型统计成功转换的章节
        if successful:
            type_stats = {}
            for item in successful:
                section_type = item['chapter']['section_type']
                type_stats[section_type] = type_stats.get(section_type, 0) + 1
            
            logger.info(f"\n📁 成功转换的章节（按类型）:")
            for section_type, count in type_stats.items():
                logger.info(f"   - {section_type}: {count} 个")
            
            logger.info(f"\n📁 成功转换的文件（前10个）:")
            for i, item in enumerate(successful[:10], 1):
                chapter = item['chapter']
                logger.info(f"  {i:2d}. {chapter['alarm_code']} - {chapter['alarm_name'][:40]}... ({chapter['section_type']})")
            
            if len(successful) > 10:
                logger.info(f"  ... 还有 {len(successful) - 10} 个文件")
        
        if failed:
            logger.info(f"\n❌ 转换失败的章节:")
            for i, item in enumerate(failed, 1):
                chapter = item['chapter']
                error = item['error']
                logger.info(f"  {i:2d}. {chapter['alarm_code']} - {error} ({chapter['section_type']})")
        
        logger.info(f"\n📂 输出目录: {self.output_dir}")
        logger.info("="*80)


def create_comprehensive_index_file(successful_conversions, output_dir):
    """创建全面的索引文件"""
    try:
        index_path = os.path.join(output_dir, "README.md")
        
        # 按类型分组
        type_groups = {}
        for item in successful_conversions:
            section_type = item['chapter']['section_type']
            if section_type not in type_groups:
                type_groups[section_type] = []
            type_groups[section_type].append(item)
        
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write("# 华为云Stack告警处理参考 - 全面章节索引\n\n")
            f.write(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"总计: {len(successful_conversions)} 个告警章节\n\n")
            
            # 按类型输出
            for section_type, items in type_groups.items():
                f.write(f"## {section_type} ({len(items)} 个)\n\n")
                
                for i, item in enumerate(items, 1):
                    chapter = item['chapter']
                    md_filename = os.path.basename(item['md_path'])
                    
                    f.write(f"{i:3d}. [{chapter['alarm_code']} {chapter['alarm_name']}]({md_filename})\n")
                
                f.write("\n")
        
        logger.info(f"📋 已生成全面索引文件: {index_path}")
        
    except Exception as e:
        logger.warning(f"生成索引文件时出错: {e}")


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "alm_markdown_comprehensive_results"
    
    if not os.path.exists(source_file):
        logger.error(f"❌ 错误：找不到源文件 {source_file}")
        return
    
    # 创建转换器
    converter = ComprehensiveALMConverter(source_file, output_dir)
    
    # 执行批量转换
    try:
        successful_conversions = converter.process_all_chapters()
        
        if successful_conversions:
            logger.info(f"\n🎉 批量转换完成!")
            logger.info(f"📄 成功转换 {len(successful_conversions)} 个告警章节")
            logger.info(f"📁 输出目录: {output_dir}")
            
            # 生成索引文件
            create_comprehensive_index_file(successful_conversions, output_dir)
        else:
            logger.error("❌ 没有成功转换任何章节")
            
    except Exception as e:
        logger.error(f"❌ 批量转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
