#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示告警样例
"""

import json

def show_sample():
    """显示告警样例"""
    with open('alarm_samples_formatted.json', 'r', encoding='utf-8') as f:
        samples = json.load(f)
    
    # 找到ALM-1223013的样例
    target_sample = None
    for sample in samples:
        if sample['告警ID'] == '1223013':
            target_sample = sample
            break
    
    if target_sample:
        print("🎯 找到ALM-1223013样例:")
        print("="*50)
        for key, value in target_sample.items():
            print(f"{key}：{value}")
        print("="*50)
    
    # 显示一个十六进制告警样例
    hex_sample = None
    for sample in samples:
        if sample['告警ID'].startswith('0x'):
            hex_sample = sample
            break
    
    if hex_sample:
        print("\n🎯 十六进制告警样例:")
        print("="*50)
        for key, value in hex_sample.items():
            print(f"{key}：{value}")
        print("="*50)

if __name__ == "__main__":
    show_sample()
