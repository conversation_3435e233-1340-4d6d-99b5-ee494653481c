#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版文档拆分工具
快速按章节拆分大文档
"""

from docx import Document
import os
import re


class SimpleDocumentSplitter:
    def __init__(self, source_docx, output_dir="split_documents"):
        self.source_docx = source_docx
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"正在加载文档: {source_docx}")
        self.doc = Document(source_docx)
        print(f"文档加载完成，共 {len(self.doc.paragraphs)} 段落")
    
    def find_chapter_boundaries(self):
        """快速查找章节边界"""
        print("查找章节边界...")
        
        chapters = []
        
        for i, paragraph in enumerate(self.doc.paragraphs):
            text = paragraph.text.strip()
            
            # 检查是否是告警章节标题
            if (paragraph.style.name.startswith('Heading') and 
                'ALM-' in text and 
                text.startswith('*******.')):
                
                chapters.append({
                    'title': text,
                    'start_para': i,
                    'alm_code': self._extract_alm_code(text)
                })
                
                if len(chapters) % 10 == 0:
                    print(f"  已找到 {len(chapters)} 个章节...")
        
        # 设置结束位置
        for i, chapter in enumerate(chapters):
            if i + 1 < len(chapters):
                chapter['end_para'] = chapters[i + 1]['start_para']
            else:
                chapter['end_para'] = len(self.doc.paragraphs)
        
        print(f"找到 {len(chapters)} 个告警章节")
        return chapters
    
    def _extract_alm_code(self, title):
        """提取ALM代码"""
        match = re.search(r'ALM-\d+', title)
        return match.group() if match else "ALM-UNKNOWN"
    
    def create_simple_document(self, chapter):
        """创建简化的章节文档"""
        new_doc = Document()
        
        # 添加章节内容
        start = chapter['start_para']
        end = chapter['end_para']
        
        for i in range(start, min(end, len(self.doc.paragraphs))):
            para = self.doc.paragraphs[i]
            
            # 简单复制段落文本
            new_para = new_doc.add_paragraph(para.text)
            
            # 尝试保持基本样式
            try:
                if para.style.name.startswith('Heading'):
                    # 根据原标题级别设置新标题
                    if 'Heading 1' in para.style.name:
                        new_para.style = 'Heading 1'
                    elif 'Heading 4' in para.style.name:
                        new_para.style = 'Heading 4'
            except:
                pass
        
        return new_doc
    
    def generate_safe_filename(self, chapter):
        """生成安全的文件名"""
        alm_code = chapter['alm_code']
        title = chapter['title']
        
        # 提取告警名称
        name_part = re.sub(r'^[\d\.\s]+ALM-\d+\s*', '', title)
        name_part = re.sub(r'[<>:"/\\|?*]', '_', name_part)
        name_part = name_part.strip()[:40]  # 限制长度
        
        return f"{alm_code}_{name_part}.docx"
    
    def split_documents(self):
        """执行拆分"""
        print("开始拆分文档...")
        
        # 查找章节
        chapters = self.find_chapter_boundaries()
        
        if not chapters:
            print("未找到章节，拆分失败")
            return []
        
        created_files = []
        
        # 拆分每个章节
        for i, chapter in enumerate(chapters):
            try:
                print(f"处理 {i+1}/{len(chapters)}: {chapter['alm_code']}")
                
                # 创建文档
                new_doc = self.create_simple_document(chapter)
                
                # 保存文档
                filename = self.generate_safe_filename(chapter)
                filepath = os.path.join(self.output_dir, filename)
                new_doc.save(filepath)
                
                created_files.append(filepath)
                
                if (i + 1) % 20 == 0:
                    print(f"  已完成 {i + 1} 个文档...")
                
            except Exception as e:
                print(f"  错误处理 {chapter['alm_code']}: {e}")
                continue
        
        print(f"\n✅ 拆分完成!")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"📄 成功创建: {len(created_files)} 个文档")
        
        return created_files


def main():
    """主函数"""
    source_file = "华为云Stack告警处理参考.docx"
    output_dir = "split_alarm_docs"
    
    if not os.path.exists(source_file):
        print(f"❌ 找不到源文件: {source_file}")
        return
    
    try:
        # 创建拆分器
        splitter = SimpleDocumentSplitter(source_file, output_dir)
        
        # 执行拆分
        created_files = splitter.split_documents()
        
        # 显示结果
        if created_files:
            print(f"\n📋 示例文档:")
            for filepath in created_files[:5]:
                filename = os.path.basename(filepath)
                print(f"  • {filename}")
            
            if len(created_files) > 5:
                print(f"  ... 还有 {len(created_files) - 5} 个文档")
            
            print(f"\n🚀 下一步:")
            print(f"   python batch_converter.py  # 批量转换为Markdown")
        
    except Exception as e:
        print(f"❌ 拆分失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
