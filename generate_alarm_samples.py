#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为每种告警生成样例告警信息（JSON格式）
"""

import os
import glob
import json
import re
import random

def extract_alarm_info_from_filename(filename):
    """从文件名中提取告警信息"""
    # 去掉.md扩展名
    name = filename.replace('.md', '')
    
    alarm_info = {
        'filename': filename,
        'section_number': '',
        'alarm_code': '',
        'alarm_name': '',
        'alarm_type': 'unknown'
    }
    
    # 提取章节号
    section_match = re.match(r'^(5\.\d+(?:\.\d+)*)', name)
    if section_match:
        alarm_info['section_number'] = section_match.group(1)
    
    # 判断告警类型并提取代码
    if 'ALM-' in name:
        # ALM告警
        alm_match = re.search(r'(ALM-[\w\d_.-]+)', name)
        if alm_match:
            alarm_info['alarm_code'] = alm_match.group(1)
            alarm_info['alarm_type'] = 'ALM'
            # 提取告警名称
            name_match = re.search(r'ALM-[\w\d_.-]+\s+(.+)', name)
            if name_match:
                alarm_info['alarm_name'] = name_match.group(1)
    
    elif '0x' in name:
        # 十六进制告警
        hex_match = re.search(r'(0x[0-9A-Fa-f]+)', name)
        if hex_match:
            alarm_info['alarm_code'] = hex_match.group(1)
            alarm_info['alarm_type'] = 'HEX'
            # 提取告警名称
            name_match = re.search(r'0x[0-9A-Fa-f]+\s+(.+)', name)
            if name_match:
                alarm_info['alarm_name'] = name_match.group(1)
    
    elif re.search(r'\d{6,}', name):
        # 数字告警代码
        num_match = re.search(r'(\d{6,})', name)
        if num_match:
            alarm_info['alarm_code'] = num_match.group(1)
            alarm_info['alarm_type'] = 'NUM'
            # 提取告警名称
            name_match = re.search(r'\d{6,}\s+(.+)', name)
            if name_match:
                alarm_info['alarm_name'] = name_match.group(1)
    
    else:
        # 其他类型（操作指导等）
        alarm_info['alarm_type'] = 'OTHER'
        # 提取章节号后面的内容作为名称
        name_match = re.search(r'^5\.\d+(?:\.\d+)*\s+(.+)', name)
        if name_match:
            alarm_info['alarm_name'] = name_match.group(1)
    
    return alarm_info

def generate_sample_alarm(alarm_info):
    """为告警信息生成样例告警"""
    
    # 基础模板
    sample = {
        "subject": "",
        "alarm_id": alarm_info['alarm_code'],
        "alarm_level": "",
        "alarm_source": "",
        "source_system": "",
        "location_info": "",
        "additional_info": "",
        "possible_cause": "未知"
    }
    
    # 根据告警类型生成不同的样例
    if alarm_info['alarm_type'] == 'ALM':
        sample.update(generate_alm_sample(alarm_info))
    elif alarm_info['alarm_type'] == 'HEX':
        sample.update(generate_hex_sample(alarm_info))
    elif alarm_info['alarm_type'] == 'NUM':
        sample.update(generate_num_sample(alarm_info))
    else:
        sample.update(generate_other_sample(alarm_info))
    
    return sample

def generate_alm_sample(alarm_info):
    """生成ALM告警样例"""
    alarm_levels = ["严重", "重要", "次要", "提示"]
    
    # 根据告警名称推断服务类型
    service_type = "Unknown"
    if "ELB" in alarm_info['alarm_name'] or "负载均衡" in alarm_info['alarm_name']:
        service_type = "ELB"
    elif "数据库" in alarm_info['alarm_name'] or "DB" in alarm_info['alarm_name']:
        service_type = "Database"
    elif "ETCD" in alarm_info['alarm_name']:
        service_type = "ETCD"
    elif "进程" in alarm_info['alarm_name']:
        service_type = "Process"
    elif "网络" in alarm_info['alarm_name'] or "连接" in alarm_info['alarm_name']:
        service_type = "Network"
    
    return {
        "subject": f"[{random.choice(alarm_levels)}告警]\"{service_type}发生{alarm_info['alarm_name']}\"",
        "alarm_level": random.choice(alarm_levels),
        "alarm_source": service_type,
        "source_system": f"ServiceOM{generate_ip()}",
        "location_info": f"区域=SH_CSVW，云服务={service_type}，节点类型=mgt",
        "additional_info": f"云服务={service_type}，服务=mgt，本端地址=({generate_ip()})，对端地址=(other_{service_type.lower()}={generate_ip()})"
    }

def generate_hex_sample(alarm_info):
    """生成十六进制告警样例"""
    alarm_levels = ["严重", "重要", "次要"]
    
    # 根据告警名称推断服务类型
    service_type = "eBackup"
    if "数据库" in alarm_info['alarm_name']:
        service_type = "Database"
    elif "License" in alarm_info['alarm_name']:
        service_type = "License"
    elif "证书" in alarm_info['alarm_name']:
        service_type = "Certificate"
    elif "备份" in alarm_info['alarm_name']:
        service_type = "Backup"
    
    return {
        "subject": f"[{random.choice(alarm_levels)}告警]\"{service_type}发生{alarm_info['alarm_name']}\"",
        "alarm_level": random.choice(alarm_levels),
        "alarm_source": service_type,
        "source_system": f"eBackup{generate_ip()}",
        "location_info": f"区域=SH_CSVW，云服务={service_type}，节点类型=backup",
        "additional_info": f"云服务={service_type}，服务=backup，节点地址=({generate_ip()})，错误代码={alarm_info['alarm_code']}"
    }

def generate_num_sample(alarm_info):
    """生成数字告警代码样例"""
    alarm_levels = ["重要", "次要"]
    service_type = "System"
    
    return {
        "subject": f"[{random.choice(alarm_levels)}告警]\"系统发生{alarm_info['alarm_name']}\"",
        "alarm_level": random.choice(alarm_levels),
        "alarm_source": "System",
        "source_system": f"SystemOM{generate_ip()}",
        "location_info": f"区域=SH_CSVW，云服务=System，节点类型=compute",
        "additional_info": f"云服务=System，服务=compute，节点地址=({generate_ip()})，错误代码={alarm_info['alarm_code']}"
    }

def generate_other_sample(alarm_info):
    """生成其他类型样例"""
    return {
        "subject": f"[提示信息]\"{alarm_info['alarm_name']}\"",
        "alarm_level": "提示",
        "alarm_source": "System",
        "source_system": f"ManageOne{generate_ip()}",
        "location_info": f"区域=SH_CSVW，云服务=ManageOne，节点类型=management",
        "additional_info": f"云服务=ManageOne，服务=management，操作类型=配置"
    }

def generate_ip():
    """生成随机IP地址"""
    return f"10.200.{random.randint(1, 255)}.{random.randint(1, 255)}"

def main():
    """主函数"""
    directory = "optimized_batch_results"
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return
    
    md_files = glob.glob(os.path.join(directory, "*.md"))
    md_files.sort()
    
    if not md_files:
        print(f"⚠️  在目录 {directory} 中未找到MD文件")
        return
    
    print(f"🔍 找到 {len(md_files)} 个MD文件")
    
    # 生成样例告警
    alarm_samples = []
    
    for file_path in md_files:
        filename = os.path.basename(file_path)
        
        # 提取告警信息
        alarm_info = extract_alarm_info_from_filename(filename)
        
        # 生成样例告警
        sample_alarm = generate_sample_alarm(alarm_info)
        
        # 添加元数据
        sample_alarm['metadata'] = {
            'filename': filename,
            'section_number': alarm_info['section_number'],
            'alarm_type': alarm_info['alarm_type']
        }
        
        alarm_samples.append(sample_alarm)
        
        if len(alarm_samples) % 50 == 0:
            print(f"  已生成 {len(alarm_samples)} 个样例...")
    
    # 保存到JSON文件
    output_file = "alarm_samples.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(alarm_samples, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 生成完成！")
    print(f"📁 输出文件: {output_file}")
    print(f"📊 总样例数: {len(alarm_samples)}")
    
    # 统计各类型数量
    type_stats = {}
    for sample in alarm_samples:
        alarm_type = sample['metadata']['alarm_type']
        type_stats[alarm_type] = type_stats.get(alarm_type, 0) + 1
    
    print(f"\n📈 类型统计:")
    for alarm_type, count in type_stats.items():
        print(f"  {alarm_type}: {count} 个")
    
    # 显示前5个样例
    print(f"\n📋 样例预览（前5个）:")
    for i, sample in enumerate(alarm_samples[:5], 1):
        print(f"\n{i}. {sample['metadata']['filename']}")
        print(f"   主题: {sample['subject']}")
        print(f"   告警ID: {sample['alarm_id']}")
        print(f"   级别: {sample['alarm_level']}")

if __name__ == "__main__":
    main()
