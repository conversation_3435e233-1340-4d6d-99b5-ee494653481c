# 文档转换结果

5. 2.3.1.21 ALM-6033 I层服务CPU占用率超过阈值
   告警解释
   告警采集模块监测IaaS层服务的CPU使用率状态，如果检测到CPU占用率超过阈值，且持续时长达到阈值，则系统产生此告警。
   系统默认告警阈值的偏移量为5%，且系统默认的告警阈值如下：
   重要：I层CPU占用率≥90%
   次要：80%≤I层CPU占用率＜90%
   告警属性
   告警参数
   对系统的影响
   此告警产生时，系统IaaS层服务的CPU占用率过高，可能会影响组件服务质量。
   可能原因
资源隔离配置的IaaS层预留CPU核数较少

存在人为启动的非OpenStack进程

处理步骤

登录FusionSphere OpenStack安装部署界面。

   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
选择“配置”，进入“资源隔离”，查看“资源组配置”中FusionSphere OpenStack隔离的vCPU个数的取值是否较少，标准取值如下表所示。

是，适当增加取值，请等待界面提示生效后，执行4。

否，执行3。

检查BMC上是否有硬件告警。

是，参考管理节点中相关章节 ，根据告警类型进行处理，执行4。

否，执行5。

等待3~4分钟，查看告警是否恢复。

是，处理完毕。

否，执行5。

请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
