# 文档转换结果

5. 2.3.1.36 ALM-70111 本地盘ERROR状态告警
   告警解释
   OpenStack按1小时周期检测本地直通盘状态，当本地直通盘状态故障，则产生此告警。
   告警属性
   告警参数
   对系统的影响
   如果此本地直通盘已经被分配给了虚拟机，则影响虚拟机使用。
   可能原因
   硬盘故障。
   处理步骤
   登录产生告警的主机。
```
先用帐号“fsp”登录，然后使用“su - root”切换到帐号“root”。
```

   帐号“fsp”的初始密码为“*****”。
   帐号“root”的初始密码为“*****”。
   导入环境变量，具体操作请参见导入环境变量。
   产生告警的本地直通盘pci地址是否含有scsi。
   是，执行5。
   否，执行4。
   执行以下命令，查看SSD设备的控制器健康状态（以“nvme0”为例）。
   hioadm info -d nvme0
   nvme0是产生告警的本地盘。
系统回显信息中的“device status”项取值表示SSD设备的控制器健康状态。

“healthy”表示设备健康状态正常。执行6。

“warning”表示设备存在异常。执行7。

执行以下命令，查看本地直通盘健康状态。

smartctl -H /dev/sda

/dev/sda是产生告警的本地盘。

系统回显信息中的“SMART Health Status”项取值表示本地直通盘的健康状态。

“OK”或者“PASSED”表示设备健康状态正常。执行6。

“FAILING”表示设备存在异常。执行7。

在Service OM界面上，选择“监控 > 告警 > 告警列表 > OpenStack告警”，单击“清除”，手动清除告警，任务结束。

建议更换本地直通盘，更换完成后，在Service OM界面上手动清除告警。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
