# 文档转换结果

5. ******** ALM-70106 虚拟机HA中间态告警
   告警解释
   虚拟机HA长时间（默认两个小时以上）卡在中间态，存在无法自动恢复的风险，则上报告警。
   告警属性
   告警参数
   对系统的影响
   涉及虚拟机无法自愈。
   可能原因
   rabbitmq 服务丢失消息。
   主机管理网络异常。
   nova-compute服务重启。
配置的HA中间态超时时间过短。

处理步骤

使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。

   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：

Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。

Region Type II和Type III场景：ExternalOM-Reverse-Proxy。

执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

```
su - root
```

   默认密码：“*****”。
   执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
   执行以下命令，导入环境变量。
   ```
   source set_env
   ```
   回显如下类似信息：
   please choose environment variable which you want to import:
   (1) openstack environment variable (keystone v3)
   (2) cps environment variable
   (3) openstack environment variable legacy (keystone v2)
   (4) openstack environment variable of cloud_admin (keystone v3)
   please choose:[1|2|3|4]
   输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
   默认密码为：*****。
   执行命令 nova show 虚拟机ID | grep task_state，查看虚拟机任务状态是否为“rebuilding”、“rebuild_block_device_mapping”、“rebuild_spawning”、“rescheduling”之一。
是，执行7。

否，HA过程中误报，手动恢复告警并结束处理。

参考“处理虚拟机处于中间态”章节处理。

Region Type I场景：

级联层：参考处理虚拟机处于中间态章节。

被级联层：参考处理虚拟机处于中间态（KVM虚拟化）章节。

Region Type II&Region Type III场景：参考处理虚拟机处于中间态（FusionCompute虚拟化）或处理虚拟机处于中间态（KVM虚拟化）章节处理。

处理是否成功。

执行成功，完成操作。

执行失败，执行9。

请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
