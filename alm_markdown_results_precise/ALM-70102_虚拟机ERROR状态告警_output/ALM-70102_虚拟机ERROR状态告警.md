# 文档转换结果

5. 2.3.1.29 ALM-70102 虚拟机ERROR状态告警
   告警解释
   虚拟机状态变为ERROR后4分钟左右，将产生此告警。
   告警属性
   告警参数
   对系统的影响
   如果虚拟机为HA（High Availiable）类型虚拟机，ERROR状态会触发虚拟机的重调度。如果为非HA的虚拟机，虚拟机会保持ERROR状态，不会自动恢复。
   可能原因
   虚拟机在运行时，在主机上异常停止（虚拟机非自主关机、或进程消失），管理程序尝试恢复虚拟机失败，将虚拟机状态变为ERROR。
   在数据库或消息队列服务不稳定的情况下，重启主机，虚拟机也需要重启，重启过程中可能因连接不到数据库或消息队列，将虚拟机状态变为ERROR。
   HA类型虚拟机在运行时，所在主机异常下电、或者管理网络中断，触发虚拟机重调度时，重建失败，虚拟机状态变为ERROR。
HA类型虚拟机状态变为ERROR后（比如上述原因），一直无法重建成功，或者在主机上重建时异常中断（管理程序异常、主机异常），卡在ERROR状态+rebuild...任务状态，无法再次触发重建。

处理步骤

登录Service OM界面。

具体操作请参见登录和注销Service OM界面。

选择“资源 > 计算资源 > 虚拟机”，显示虚拟机列表页面，查看虚拟机“电源状态”是否为已停止或者运行中。

是，执行11。

否，执行3。

单击虚拟机名称，进入虚拟机概要页面，查看虚拟机“任务状态”字段是否为空。

是，执行4。

否，执行10。

选择“监控 > 告警 > 告警列表 > OpenStack告警”，查看是否还存在如下告警：

ALM-6021 主机网口状态异常

ALM-6023 主机存储链路中断

ALM-6026 主机光纤通道中断

如果存在，则应根据对应的告警帮助处理告警，然后执行11。否则，执行5。

使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。

   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：

Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。

Region Type II和Type III场景：ExternalOM-Reverse-Proxy。

执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

```
su - root
```

   默认密码：“*****”。
   执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
   导入环境变量，具体操作请参见导入环境变量
   重启ceilometer-agent-central服务，等待5分钟再次观察告警是否自动清除。
   ```
   cps host-template-instance-operate --service ceilometer ceilometer-agent-central --action stop
   ```
   ```
   cps host-template-instance-operate --service ceilometer ceilometer-agent-central --action start
   ```
   告警已自动清除，处理完毕。
   告警无法自动清除，执行10。
   参考“处理虚拟机处于中间态”章节处理。
   Region Type I场景：
   级联层：参考处理虚拟机处于中间态章节。
   被级联层：参考处理虚拟机处于中间态（KVM虚拟化）章节。
   Region Type II&Region Type III场景：参考处理虚拟机处于中间态（FusionCompute虚拟化）或处理虚拟机处于中间态（KVM虚拟化）章节处理。
   选择“资源 > 计算资源 > 虚拟机”，在显示的虚拟机列表界面，单击“更多 > 停止”，停止虚拟机，等待虚拟机“电源状态”变成已停止。启动虚拟机。
   成功，处理完毕。
   失败，执行12。
请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
