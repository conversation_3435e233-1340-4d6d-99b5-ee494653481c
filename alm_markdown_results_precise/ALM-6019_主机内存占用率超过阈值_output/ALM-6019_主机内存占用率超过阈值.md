# 文档转换结果

5. 2.3.1.8 ALM-6019 主机内存占用率超过阈值
   告警解释
   OpenStack周期（默认为300s）检测主机内存占用率，当检测到主机内存占用率大于等于系统设置的告警阈值（默认为99%）时，系统产生此告警。
   支持自定义主机内存告警阈值，请在Service OM管理页面的告警设置中调整阈值。
   告警属性
   告警参数
   对系统的影响
   可能会造成系统运行速度慢。
   可能原因
   主机业务繁忙负载过重。
   处理步骤
登录FusionSphere OpenStack安装部署界面。

   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“节点类型”列，查看当前主机节点类型是否为控制节点。

是，执行5。

否，执行3。

迁移该主机上的虚拟机到其他主机，具体操作请参见迁移虚拟机。

如果无主机可迁移，执行5。

等待3分钟~4分钟，查看告警是否恢复。

是，处理完毕。

否，执行5。

请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
