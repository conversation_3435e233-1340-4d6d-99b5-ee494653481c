# 文档转换结果

5. 2.3.1.30 ALM-70103 主机VCPU使用数目超过限制
   告警解释
   在FusionSphere OpenStack对接FusionCompute的场景下，主机上虚拟机同时使用的VCPU个数超过限制时产生告警，该限制指：主机VCPU个数 × 复用比。
   复用比分为全局复用比与主机组复用比。如果nova-scheduler中过滤器打开了CoreFilter则全局复用比有效，如果nova-scheduler中过滤器打开了AggregateCoreFilter并且主机已经加入到了对应的主机组，则主机组复用比有效。
   如果主机组复用比和全局复用比同时生效，则取最小值。
   告警属性
   告警参数
   对系统的影响
   该主机上的虚拟机进行大量计算业务时，虚拟机性能有所下降，影响客户体验。
   可能原因
   FusionSphere OpenStack对接FusionCompute的场景下，由FusionCompute对VCPU资源进行计数，并且关闭的虚拟机不占用VCPU资源。因此，当主机VCPU资源即将耗尽时，在该主机上并发创建了多个虚拟机，可能产生VCPU占用总数超过限制的情况。
处理步骤

通过告警定位信息找到产生告警的fc-nova-compute节点所对应的FusionCompute集群。

是否需要迁移虚拟机到其它fc-nova-compute节点。

是，将该fc-nova-compute节点所对应的集群中部分虚拟机迁移到其他fc-nova-compute节点上

具体操作请参考迁移虚拟机。

否，执行3。

登录FusionSphere OpenStack安装部署界面。

   具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
选择“配置 > 资源池管理”，进入“资源池管理”页面。

单击“HUAWEI”后面的“+”号，进入“集群管理”页面。

单击对应的fc-nova-compute集群后面的，进入“配置计算集群”页。

在“配置参数”页签，配置更高的“vCPU复用比”，单击下面的“提交”。

等待3~5分钟，查看告警是否清除。

是，处理完毕。

否，执行9。

请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
