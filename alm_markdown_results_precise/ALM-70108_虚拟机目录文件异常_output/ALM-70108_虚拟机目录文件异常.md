# 文档转换结果

5. 2.3.1.34 ALM-70108 虚拟机目录文件异常
   告警解释
   OpenStack按5分钟周期检测虚拟机的目录文件，当虚拟机已经在其它主机上稳定运行或者已经被删除，并且底层不存在该虚拟机，而主机上仍然存在该虚拟机的目录文件时，或者虚拟机所在的主机上虚拟机的相关文件有丢失，则产生此告警。
   告警属性
   告警参数
   对系统的影响
   虚拟机残留文件会占用主机磁盘空间，可能造成资源不足，影响虚拟机正常创建。
   虚拟机文件丢失会导致虚拟机关机后无法启动。
   可能原因
   虚拟机删除过程中服务异常或者进程发生重启。
   虚拟机resize或者revert-resize过程中发生错误或者虚拟机被删除。
虚拟机文件被误删除。

处理步骤

使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。

   默认帐号：fsp，默认密码：*****。
   系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：

Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。

Region Type II和Type III场景：ExternalOM-Reverse-Proxy。

执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。

```
su - root
```

   默认密码：“*****”。
   执行以下命令，防止系统超时退出。
   ```
   TMOUT=0
   ```
   导入环境变量，具体操作请参见导入环境变量。
   查看告警中“附加信息”中的“详细信息”，查看是否有“lost files”字样。
   是，执行6。
   否，执行7。
   执行命令nova list --all-t | grep 虚拟机ID，确认虚拟机是否存在。
   虚拟机ID从告警定位信息中获取。
   是，请参照异地重建虚拟机章节，在其它主机上重建虚拟机，执行14。
   否，执行7。
   查看虚拟机是否在告警主机上存在。
   在告警附加信息中，获取告警所在主机id。
执行命令cps host-list | grep 主机id，获取主机的管理面IP。

执行命令su fsp，切换为fsp用户。

执行命令ssh fsp@管理面ip，切换到管理面IP。

重复执行2~4。

执行runsafe命令进入安全模式。

按照提示输入命令，查看虚拟机是否在告警主机上存在：

nova_virsh_cmd virsh-list-uuid | grep 虚拟机ID

回显信息中是否包含指定的虚拟机。

是，执行命令nova show 虚拟机ID | grep instance_name，获取虚拟机底层名称（比如instance-00000070）。然后执行命令virsh undefine 虚拟机底层名称，删除与虚拟机关联的文件，执行14。

否，执行8。

执行runsafe命令进入安全模式，按照提示输入命令：

nova show 虚拟机ID

回显信息中输出如下信息，表示可以查询到虚拟机，执行9。

     +--------------------------------------+----------------------------------------------------------+ 
     | Property                             | Value                                                    | 
     +--------------------------------------+----------------------------------------------------------+ 
     | OS-DCF:diskConfig                    | MANUAL                                                   | 
     | OS-EXT-AZ:availability_zone          | az1.dc1                                                  | 
     | OS-EXT-SRV-ATTR:host                 | 253B9B3A-EC06-117B-8567-000000821800                     | 
回显信息中输出“ERROR (CommandError): No server with a name or ID of '虚拟机ID' exists.”，执行12。

虚拟机详情信息中，查看虚拟机状态是否正常。

字段“OS-EXT-STS:vm_state ”是否为active，“OS-EXT-STS:power_state”电源状态是否为1，“OS-EXT-STS:task_state”任务状态是否为“-”。

是，执行10。

否，请联系技术支持工程师协助解决。

虚拟机详情信息中，查看虚拟机所属主机是否和产生告警主机一致。

字段“OS-EXT-SRV-ATTR:host”是虚拟机所属主机。

是，执行11。

否，执行12。

登录虚拟机所属主机。执行如下命令查看虚拟机目录是否存在：

ls /opt/HUAWEI/image/instances/虚拟机ID

回显信息中是否包含“No such file or directory”。

是，请联系技术支持工程师协助解决。

否，执行12。

登录产生虚拟机目录文件异常告警的主机(注意不是虚拟机所属的主机)，执行如下命令，查看虚拟机残留文件：

ls /opt/HUAWEI/image/instances/ | grep 虚拟机ID

回显信息是否为空。

是，执行14。

否，执行13。

将回显信息里以虚拟机ID或者虚拟机ID_resize命名的虚拟机残留文件清理。

执行命令cd /opt/HUAWEI/image/instances，切换到残留文件目录。

执行命令ll | grep residual_instance_file_name，确认残留文件。

执行命令rm -rf residual_instance_file_name，删除残留文件。

residual_instance_file_name为12回显信息中的虚拟机残留文件名，即以虚拟机ID或者虚拟机ID_resize命名的文件。

手动清除告警，任务结束。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
