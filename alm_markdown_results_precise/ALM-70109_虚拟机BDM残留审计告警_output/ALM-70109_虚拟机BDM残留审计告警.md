# 文档转换结果

5. 2.3.1.35 ALM-70109 虚拟机BDM残留审计告警
   告警解释
   当执行系统审计时发现虚拟机挂载卷时分配盘符超时失败，产生此告警。
   告警属性
   告警参数
   对系统的影响
   此告警产生时，系统中存在虚拟机挂载卷时因分配盘符超时导致虚拟机的BDM残留，影响系统对虚拟机的管理。
   可能原因
   虚拟机挂卷时分配盘符超时失败。
   请在告警的详细信息中获取引发告警的具体审计问题，并参考处理步骤完成相应问题的处理。
   处理步骤
获取告警详情中“附加信息”参数中的“详细信息”取值，并参考表1，获取对应的审计报告名称。

确定当前环境部署的场景，获取审计报告。

Region Type I：

判断部署的场景是级联层还是被级联层的方法：在OpenStack首节点，执行命令cps productinfo-show，查看product_type的取值，cascading表示级联层，cascaded表示被级联层。

如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。

级联层：收集审计报告

KVM虚拟化（被级联层）：收集审计报告

Region Type II&Region Type III：

FusionCompute虚拟化：收集审计报告

KVM虚拟化：收集审计报告

确定当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。

Region Type I：

级联层：审计结果定位

KVM虚拟化（被级联层）：审计结果定位

Region Type II&Region Type III：

FusionCompute虚拟化：审计结果定位

KVM虚拟化：审计结果定位

根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。

Region Type I：

级联层：手动审计

KVM虚拟化（被级联层）：手动审计

Region Type II&Region Type III：

FusionCompute虚拟化：手动审计

KVM虚拟化：手动审计

查看告警是否清除。

是，处理完毕。

否，执行6。

请联系技术支持工程师协助解决。

参考信息

无。

父主题： FusionSphere OpenStack告警参考 

版权所有 © 华为技术有限公司

版权所有 © 华为技术有限公司

< 上一节 下一节 > 
