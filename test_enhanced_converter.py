#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版批量转换器
"""

from optimized_batch_converter import OptimizedBatchConverter

def test_enhanced_converter():
    """测试增强版转换器"""
    print("=== 测试增强版批量转换器 ===")
    
    # 创建转换器
    converter = OptimizedBatchConverter("华为云Stack告警处理参考.docx", "enhanced_test_output")
    
    # 查找所有章节
    chapters = converter.find_all_alarm_chapters()
    
    print(f"\n📊 章节统计:")
    print(f"总章节数: {len(chapters)}")
    
    # 按类型统计
    type_stats = {}
    for chapter in chapters:
        section_type = chapter['section_type']
        type_stats[section_type] = type_stats.get(section_type, 0) + 1
    
    for section_type, count in type_stats.items():
        print(f"  {section_type}: {count} 个")
    
    # 显示各类型的示例
    print(f"\n📋 各类型示例:")
    for section_type in type_stats.keys():
        examples = [ch for ch in chapters if ch['section_type'] == section_type][:3]
        print(f"\n{section_type}:")
        for i, example in enumerate(examples, 1):
            print(f"  {i}. {example['title']}")
            print(f"     代码: {example['alarm_code']}")
            print(f"     名称: {example['alarm_name']}")

if __name__ == "__main__":
    test_enhanced_converter()
