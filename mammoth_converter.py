#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 mammoth 库的Word到Markdown转换器
mammoth 在处理复杂格式和图片方面更加强大
"""

import mammoth
import os
import re
import base64
from pathlib import Path


class MammothDocxConverter:
    def __init__(self, docx_path, output_dir="mammoth_output"):
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.image_dir = os.path.join(output_dir, "images")
        self.image_counter = 0
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)
    
    def image_converter(self, image):
        """自定义图片转换函数"""
        try:
            # 生成图片文件名
            self.image_counter += 1
            
            # 获取图片扩展名
            content_type = getattr(image, 'content_type', 'image/png')
            if 'jpeg' in content_type or 'jpg' in content_type:
                ext = '.jpg'
            elif 'png' in content_type:
                ext = '.png'
            elif 'gif' in content_type:
                ext = '.gif'
            else:
                ext = '.png'  # 默认
            
            filename = f"image_{self.image_counter:03d}{ext}"
            image_path = os.path.join(self.image_dir, filename)
            
            # 保存图片
            with open(image_path, 'wb') as f:
                f.write(image.open().read())
            
            # 返回Markdown图片语法
            relative_path = f"images/{filename}"
            alt_text = getattr(image, 'alt_text', f'图片{self.image_counter}')
            
            print(f"转换图片: {filename}")
            return {"src": relative_path, "alt": alt_text}
            
        except Exception as e:
            print(f"图片转换错误: {e}")
            return {"src": "", "alt": "图片加载失败"}
    
    def convert_to_markdown(self, output_filename="document.md"):
        """转换为Markdown"""
        print("使用 mammoth 开始转换...")
        
        try:
            # 配置转换选项
            convert_image = mammoth.images.img_element(self.image_converter)
            
            # 执行转换
            with open(self.docx_path, "rb") as docx_file:
                result = mammoth.convert_to_markdown(
                    docx_file,
                    convert_image=convert_image
                )
            
            # 获取转换结果
            markdown_content = result.value
            messages = result.messages
            
            # 处理警告信息
            if messages:
                print("转换警告:")
                for message in messages:
                    print(f"  - {message}")
            
            # 后处理Markdown内容
            markdown_content = self._post_process_markdown(markdown_content)
            
            # 保存文件
            output_path = os.path.join(self.output_dir, output_filename)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            print(f"✅ 转换完成!")
            print(f"📄 输出文件: {output_path}")
            print(f"🖼️  图片目录: {self.image_dir}")
            print(f"📊 转换了 {self.image_counter} 张图片")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _post_process_markdown(self, content):
        """后处理Markdown内容"""
        # 修复表格格式
        content = self._fix_table_formatting(content)
        
        # 修复列表格式
        content = self._fix_list_formatting(content)
        
        # 修复标题格式
        content = self._fix_heading_formatting(content)
        
        # 清理多余的空行
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        return content
    
    def _fix_table_formatting(self, content):
        """修复表格格式"""
        # 这里可以添加表格格式的修复逻辑
        return content
    
    def _fix_list_formatting(self, content):
        """修复列表格式"""
        # 修复列表项的格式
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # 处理列表项
            if re.match(r'^\s*[\*\-\+]\s', line):
                fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _fix_heading_formatting(self, content):
        """修复标题格式"""
        # 确保标题前后有空行
        content = re.sub(r'(\n)(#{1,6}\s+.+)(\n)', r'\1\n\2\n\n', content)
        return content


def install_mammoth():
    """安装 mammoth 库"""
    import subprocess
    import sys
    
    try:
        import mammoth
        print("mammoth 库已安装")
        return True
    except ImportError:
        print("正在安装 mammoth 库...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "mammoth"])
            print("mammoth 库安装成功")
            return True
        except subprocess.CalledProcessError:
            print("mammoth 库安装失败")
            return False


def main():
    """主函数"""
    # 检查并安装依赖
    if not install_mammoth():
        print("请手动安装 mammoth: pip install mammoth")
        return
    
    # 重新导入 mammoth
    global mammoth
    import mammoth
    
    # 配置
    input_file = "test.docx"
    output_dir = "mammoth_output"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return
    
    # 创建转换器
    converter = MammothDocxConverter(input_file, output_dir)
    
    # 执行转换
    converter.convert_to_markdown("test_mammoth.md")


if __name__ == "__main__":
    main()
