#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试编号提取问题
"""

from docx import Document
import re

def debug_numbering():
    """调试编号提取"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    print("=== 调试编号提取 ===")
    
    # 找到ALM-6008章节
    start_found = False
    processing_section = False
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        
        # 找到ALM-6008章节开始
        if "*******.1 ALM-6008" in text:
            start_found = True
            print(f"找到章节开始: {text}")
            continue
            
        if not start_found:
            continue
            
        # 找到下一个章节就停止
        if re.match(r'^5\.2\.3\.1\.\d+\s+ALM-\d+', text):
            print(f"找到下一个章节，停止: {text}")
            break
            
        # 检查是否进入处理步骤部分
        if "处理步骤" in text:
            processing_section = True
            print(f"\n=== 进入处理步骤部分 ===")
            continue
            
        # 如果在处理步骤部分，检查编号
        if processing_section and text:
            # 检查段落的列表属性
            if hasattr(para, '_element') and para._element is not None:
                from docx.oxml.ns import qn
                pPr = para._element.find(qn('w:pPr'))
                if pPr is not None:
                    numPr = pPr.find(qn('w:numPr'))
                    if numPr is not None:
                        # 获取列表级别
                        ilvl = numPr.find(qn('w:ilvl'))
                        level = 0
                        if ilvl is not None:
                            level = int(ilvl.get(qn('w:val'), 0))
                        
                        # 获取编号ID
                        numId = numPr.find(qn('w:numId'))
                        num_id = numId.get(qn('w:val')) if numId is not None else "None"
                        
                        print(f"段落 {i}: 级别={level}, 编号ID={num_id}")
                        print(f"  文本: {text[:100]}...")
                        
                        # 尝试提取编号
                        number_match = re.match(r'^(\d+)\.\s*', text)
                        if number_match:
                            print(f"  提取到编号: {number_match.group(1)}")
                        else:
                            print(f"  未找到编号模式")
                        print()

if __name__ == "__main__":
    debug_numbering()
