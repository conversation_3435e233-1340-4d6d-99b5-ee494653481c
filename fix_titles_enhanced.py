#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版MD标题修复器
1. 检查文档标题下的第一句话是否和标题重复，如果重复删除
2. 检查关键词是否为标题，如果不是改为五级标题
"""

import os
import glob
import re

def fix_md_titles_enhanced():
    """修复MD文件中的标题格式和重复内容"""

    # 需要检查的关键词（扩展列表）
    keywords = [
        "告警解释",
        "告警属性",
        "告警参数",
        "对系统的影响",
        "可能原因",
        "处理步骤",
        "告警清除",
        "参考信息",
        "操作场景",
        "前提条件",
        "操作步骤"
    ]

    # 查找所有MD文件
    directory = "optimized_batch_results"
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return

    md_files = glob.glob(os.path.join(directory, "*.md"))

    if not md_files:
        print(f"⚠️  在目录 {directory} 中未找到MD文件")
        return

    print(f"🔍 找到 {len(md_files)} 个MD文件")

    modified_count = 0
    total_fixes = 0
    total_duplicates_removed = 0

    for file_path in md_files:
        filename = os.path.basename(file_path)
        print(f"\n📄 处理文件: {filename}")

        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            new_lines = []
            file_modified = False
            file_fixes = 0
            file_duplicates_removed = 0

            # 预处理：标记需要删除的重复行
            skip_lines = set()

            # 检查标题下的重复内容
            if lines and lines[0].strip().startswith('#'):
                title_text = re.sub(r'^#+\s*', '', lines[0].strip())

                # 查找第一个非空行
                for check_idx in range(1, min(5, len(lines))):
                    check_line = lines[check_idx].strip()
                    if check_line:  # 找到第一个非空行
                        # 检查是否重复（去掉标点符号进行比较）
                        title_clean = re.sub(r'[^\w\s]', '', title_text)
                        check_clean = re.sub(r'[^\w\s]', '', check_line)

                        if title_clean and check_clean and title_clean in check_clean:
                            print(f"  第{check_idx+1}行: 删除重复内容 '{check_line}'")
                            skip_lines.add(check_idx)
                            file_duplicates_removed += 1
                            total_duplicates_removed += 1
                            file_modified = True
                        break

            # 处理每一行
            for i, line in enumerate(lines):
                # 如果这行被标记为跳过，则跳过
                if i in skip_lines:
                    continue

                line_modified = False

                # 检查2：关键词是否为标题
                line_stripped = line.strip()
                if line_stripped:  # 非空行
                    # 检查是否包含关键词且单独成行
                    for keyword in keywords:
                        if line_stripped == keyword:  # 完全匹配且单独成行
                            # 检查是否已经是标题（以#开头）
                            if not line_stripped.startswith('#'):
                                # 不是标题，改为五级标题
                                new_line = f"##### {line_stripped}\n"
                                new_lines.append(new_line)
                                print(f"  第{i+1}行: '{line_stripped}' -> '##### {line_stripped}'")
                                line_modified = True
                                file_modified = True
                                file_fixes += 1
                                total_fixes += 1
                                break

                if not line_modified:
                    # 没有修改，保持原样
                    new_lines.append(line)

            # 如果有修改，写回文件
            if file_modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                print(f"  ✅ 修复了 {file_fixes} 处标题，删除了 {file_duplicates_removed} 处重复内容，文件已保存")
                modified_count += 1
            else:
                print(f"  ✅ 文件无需修复")

        except Exception as e:
            print(f"  ❌ 处理文件时出错: {e}")

    # 输出总结
    print(f"\n" + "="*60)
    print(f"📊 处理完成总结")
    print(f"="*60)
    print(f"📁 处理目录: {directory}")
    print(f"📄 总文件数: {len(md_files)}")
    print(f"🔧 修复文件数: {modified_count}")
    print(f"🔧 标题修复数: {total_fixes}")
    print(f"🗑️  重复内容删除数: {total_duplicates_removed}")

    if modified_count > 0:
        print(f"\n🎉 已修复 {modified_count} 个文件")
        print(f"   - 标题格式修复: {total_fixes} 处")
        print(f"   - 重复内容删除: {total_duplicates_removed} 处")
    else:
        print(f"\n✨ 所有文件都正确，无需修复")

def test_single_file():
    """测试单个文件的修复功能"""
    print("🧪 测试单个文件修复功能...")

    # 创建测试内容
    test_content = """# 5.2.3.1.1 ALM-6008 上传日志到OBS服务失败

上传日志到OBS服务失败

告警解释

这是告警解释的内容。

告警属性

这是告警属性的内容。

##### 可能原因

这已经是标题了。

处理步骤

这是处理步骤的内容。
"""

    # 写入测试文件
    test_file = "test_fix.md"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)

    print(f"创建测试文件: {test_file}")
    print("原始内容:")
    print(test_content)

    # 模拟修复过程
    lines = test_content.split('\n')
    new_lines = []

    keywords = ["告警解释", "告警属性", "可能原因", "处理步骤"]

    for i, line in enumerate(lines):
        if i == 0 and line.strip().startswith('#'):
            # 标题行
            new_lines.append(line)
            title_text = re.sub(r'^#+\s*', '', line.strip())

            # 检查下一行是否重复
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and title_text in next_line:
                    print(f"发现重复内容: '{next_line}'")
                    continue  # 跳过重复行
        else:
            # 检查关键词
            line_stripped = line.strip()
            if line_stripped in keywords and not line_stripped.startswith('#'):
                new_lines.append(f"##### {line_stripped}")
                print(f"修复标题: '{line_stripped}' -> '##### {line_stripped}'")
            else:
                new_lines.append(line)

    # 写入修复后的内容
    fixed_content = '\n'.join(new_lines)
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)

    print("\n修复后内容:")
    print(fixed_content)

    # 清理测试文件
    os.remove(test_file)
    print(f"\n清理测试文件: {test_file}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_file()
    else:
        fix_md_titles_enhanced()
