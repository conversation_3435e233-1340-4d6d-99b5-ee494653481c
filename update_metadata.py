import requests
import json

# 配置参数
dataset_id = "06ea8a7b-17f1-48d5-9e9f-53c0c6caace8"  # 替换为实际数据集 ID
url = f"http://localhost/v1/datasets/{dataset_id}/documents/metadata"
api_key = "dataset-dyT48T6t6lrkKq06EYv3qlKV"  # 替换为你的实际 API Key

document_id = "215502cd-9dc8-4b02-b83b-4e6e9f8a39a2"  # 替换为实际文档 ID


# 请求头
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 请求体数据
payload = {
    "operation_data": [{
        "document_id": document_id,  # 替换为实际文档 ID
        "metadata_list": [
            {
              "id": "5216f8f0-c48b-428b-a370-d8d2036166bf",
              "name": "alert_id",
              "type": "string",
              "value": "36",
            },
            {
              "id": "3bd9475b-a838-47aa-b967-c6339815e93f",
              "name": "alert_title",
              "type": "string",
              "value": "磁盘占用率过高告警",
            }
        ]
    }]
}

try:
#发送 POST 请求
    response = requests.post(
        url.format(dataset_id=dataset_id),
        headers=headers,
        data=json.dumps(payload))  # 自动处理 JSON 序列化

    response.raise_for_status()  # 检查请求是否成功

    # 打印响应结果（自动处理 Unicode 中文）
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
except json.JSONDecodeError as e:
    print(f"JSON 解析失败: {e}")