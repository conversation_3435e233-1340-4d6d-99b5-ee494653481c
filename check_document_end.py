#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文档末尾是否有遗漏的章节
"""

from docx import Document
import re

def check_document_end():
    """检查文档末尾的章节"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    print("🔍 检查文档末尾的章节...")
    
    # 找到"******** 设备告警"的位置
    target_found = False
    target_index = -1
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        if "********" in text and "设备告警" in text:
            target_found = True
            target_index = i
            print(f"找到目标章节: 第{i}行 - {text}")
            break
    
    if not target_found:
        print("❌ 未找到'******** 设备告警'章节")
        return
    
    # 检查该章节之后的内容
    print(f"\n📋 '******** 设备告警'之后的内容:")
    print("="*80)
    
    remaining_chapters = []
    
    for i in range(target_index + 1, len(doc.paragraphs)):
        text = doc.paragraphs[i].text.strip()
        
        if not text:
            continue
            
        # 检查是否是5.x章节
        if re.match(r'^5\.', text):
            remaining_chapters.append({
                'index': i,
                'text': text,
                'style': doc.paragraphs[i].style.name
            })
            print(f"{len(remaining_chapters):3d}. 第{i:4d}行: {text}")
        
        # 如果遇到6.x或更高章节，停止
        if re.match(r'^[6-9]\.', text):
            print(f"\n遇到更高级章节，停止搜索: {text}")
            break
        
        # 检查前100个段落就够了
        if i > target_index + 100:
            break
    
    print(f"\n📊 统计:")
    print(f"'******** 设备告警'之后还有 {len(remaining_chapters)} 个5.x章节")
    
    if remaining_chapters:
        print(f"\n🎯 遗漏的章节类型分析:")
        for chapter in remaining_chapters:
            text = chapter['text']
            chapter_type = "unknown"
            
            if re.match(r'^5\.\d+\.\d+\.\d+\.\d+\s+ALM-\d+', text):
                chapter_type = "ALM告警_5位"
            elif re.match(r'^5\.\d+\.\d+\.\d+\.\d+\.\d+\s+ALM-', text):
                chapter_type = "ALM告警_6位"
            elif re.match(r'^5\.\d+\.\d+\.\d+\.\d+\s+0x[0-9A-Fa-f]+', text):
                chapter_type = "十六进制告警"
            elif re.match(r'^5\.\d+\.\d+\.\d+\.\d+\s+\d+\s+', text):
                chapter_type = "其他告警代码"
            else:
                chapter_type = "其他章节"
            
            print(f"  [{chapter_type}] {text}")
    
    return remaining_chapters

def check_full_document_structure():
    """检查完整文档结构"""
    doc = Document("华为云Stack告警处理参考.docx")
    
    print(f"\n🔍 检查完整文档结构...")
    
    all_5x_chapters = []
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        
        # 查找所有5.x章节
        if re.match(r'^5\.', text):
            all_5x_chapters.append({
                'index': i,
                'text': text,
                'style': para.style.name
            })
        
        # 如果遇到6.x章节，停止
        if re.match(r'^6\.', text):
            print(f"遇到6.x章节，停止搜索: {text}")
            break
    
    print(f"\n📊 完整统计:")
    print(f"文档中总共有 {len(all_5x_chapters)} 个5.x章节")
    
    # 显示最后10个章节
    print(f"\n📋 最后10个5.x章节:")
    for i, chapter in enumerate(all_5x_chapters[-10:], len(all_5x_chapters)-9):
        print(f"{i:3d}. 第{chapter['index']:4d}行: {chapter['text']}")
    
    return all_5x_chapters

if __name__ == "__main__":
    # 检查"******** 设备告警"之后的内容
    remaining = check_document_end()
    
    # 检查完整文档结构
    all_chapters = check_full_document_structure()
    
    print(f"\n🎯 结论:")
    if remaining:
        print(f"❌ 确实有遗漏！'******** 设备告警'之后还有 {len(remaining)} 个章节")
    else:
        print(f"✅ 没有遗漏，'******** 设备告警'是最后一个5.x章节")
